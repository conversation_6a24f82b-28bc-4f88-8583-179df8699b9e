{"version": 2, "files": [{"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/compat/file_fetcher.dart", "hash": "808718676ca459f8941aa519a73bbff2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/proxy_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/queue_item.dart", "hash": "937dad14a7958c57948525533b199296"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/file_content.dart", "hash": "416416560c9553de5c741586424c78b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/conversion_sink.dart", "hash": "efcbc6fd4212ea81281561abddbf29f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/node.dart", "hash": "9ec244272cb6c8da46a6dd5f104f0dfe"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/time_picker.dart", "hash": "bf00ea3c58b6ee2b3f5422cfc3e3cd2b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/outlined_button_theme.dart", "hash": "8ece5be4aa5c8fa615288c4c8c5277a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_random_access_file.dart", "hash": "8584e5707c45dd6bdd567a10dfd8cd0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/models/analysis/analysis_config.dart", "hash": "07e92c32cf800cb027ee44c7a0a0efd7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/text_button_theme.dart", "hash": "becd419f96efe14f36f18a8c8adc82cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/states/preview_camera_state.dart", "hash": "faca4945e7f599b59442b57a784ed87c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/flutter_logo.dart", "hash": "985cf5499dc6e521191985f55245a22c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/semantics/debug.dart", "hash": "3fd33becc9141d8a690c4205c72c5d40"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/curves.dart", "hash": "4aeb4635d84df42e6f220aba366af7d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/palette_uint32.dart", "hash": "7e24dedb91aa69e9e12b8672c25d3e1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/plane.dart", "hash": "f0c6d5d05fbdc95ab84f1a63894b7be6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/merge.dart", "hash": "c2b88768bdc9704848019fd9df8c2546"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/sliver_fill.dart", "hash": "6987c3474a94dd1c4ff8f8540212f16b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/non_storing_object_provider.dart", "hash": "21cb059be81989938ccfbda405ae9a65"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/draw/_calculate_circumference.dart", "hash": "95983ee515850af611358b65f1700c58"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/tga_decoder.dart", "hash": "0e950952d49725a7e39a0ff0e9356974"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/emboss_cmd.dart", "hash": "4ebee1842a20e523dae6e1059defbad3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/box_shadow.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/formats/jpg_cmd.dart", "hash": "21832c9bd4cbe897b2b81a2f58bbcec8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quad.dart", "hash": "739bb2e85022ddfb653590b93216942a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/quantize_cmd.dart", "hash": "c24a48006b0ba99df154c41075523a78"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/list.dart", "hash": "ee730199a496cacbfd82312849e80523"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/posix.dart", "hash": "5e054086533f32f7181757a17890ae56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/draw/blend_mode.dart", "hash": "4a219ce53723e59f87e46f946712ab5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/codec/node_codec.dart", "hash": "1de9311ba0f47dfc96166daab936f705"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/spell_check_suggestions_toolbar.dart", "hash": "e4c4603e78131a8bc950a8029d624a76"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/jpeg_decoder.dart", "hash": "389e659ee490ae1de9992dc76ee474c2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/form_section.dart", "hash": "cd995d0f309bf74d0bbe94eb1e4e8e81"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/draw/fill.dart", "hash": "fbd35105cf5a6b08cab5e7562d392921"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_delegate.dart", "hash": "e78589269f033237f43ffdc87adc47a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality_map.dart", "hash": "700328ab0177ddfd9a003a8c15619c1a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/layout_builder.dart", "hash": "f5dab330de9938d8ad99263892810f3d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_link.dart", "hash": "92be3b74ebf2b10ee5852ddbbc825971"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/matches/matches_iterator.dart", "hash": "4c92351d347c52a00797317aa487600f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/xml_events.dart", "hash": "71b9da53ac40a568e55525799518d891"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/date_picker_theme.dart", "hash": "3ab9652d1101aac3b5d74a4495d860ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/webp/vp8l_color_cache.dart", "hash": "ef4941331806f1aa87d3c24d53048b57"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/LICENSE", "hash": "1d84cf16c48e571923f837136633a265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/LICENSE", "hash": "fb92f0b8decb7b59a08fe851e030948d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/color/format.dart", "hash": "7b3558e0f80f0c86ad6ebbef3755d515"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar_button.dart", "hash": "a46ede2164234d7371852e8f57865dd0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/tiff/tiff_info.dart", "hash": "400469fd65b57f5b3ba500cb7c97fcf6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/expansion_tile_theme.dart", "hash": "045c779ec8564825d7f11fbbd6fb2fa1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/png/png_frame.dart", "hash": "ac97bb1782d69b6c78514bbda633c9fe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/node.dart", "hash": "2f9772d14db922d3a41fb27f6b6382fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/models/sensor_type.dart", "hash": "ec326b3be8548db1421decb532ec7b05"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/lib/src/utils/column_utils.dart", "hash": "35621266913c8ce55d4f9b42b554225c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/subjects.dart", "hash": "1f923e6c3ab08753130b61ba27256959"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/transform/copy_expand_canvas_cmd.dart", "hash": "f83e7aac5a00a0d6d980f6b14e455d6c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/colors.dart", "hash": "f59aed120736d81640750c612c8cfe5c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/draw/draw_rect.dart", "hash": "7a57141b38f9ffbf65b7e15fa4dfe7bf"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/segmented_control.dart", "hash": "8e58a1e955460cf5a4ea1cea2b7606cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/progress_indicators.dart", "hash": "c2c2286fb7180be54cc4fd8b03ba9dea"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/synchronous_future.dart", "hash": "fb23ec509c4792802accd10fa7c8a6b0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/picker.dart", "hash": "4d8781c671b7df5aadf2331931458cfb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/sqflite_method_channel.dart", "hash": "2c294b86e9cf73bb732d8419ab47f434"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/text_form_field_row.dart", "hash": "cbeab9c259374c922b24d3cbd1cb6aa4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/cache_info_repositories.dart", "hash": "8cf88d8eac6c9b46a6fb3877f9fc35d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/whitespace.dart", "hash": "f0df878443ef28db864b73e66f8206a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/text.dart", "hash": "d3de5e8090ec30687a667fdb5e01f923"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/lib/models/watermark_model.dart", "hash": "51c97504917a1c58fdf41ca8e24516d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/sobel.dart", "hash": "602dac4e373148076d77285880dc66aa"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/.dart_tool/flutter_build/dart_plugin_registrant.dart", "hash": "1537324c8f79d4bc3eca8bd06cd2a87b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/unique_widget.dart", "hash": "11b4d96c7383b017773d65cb2843d887"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/debug.dart", "hash": "fab9f5f0fb3bdd9295e12a17fef271c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib_encoder.dart", "hash": "2fb9bf1a094efc775572581a6247aaba"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/timeline.dart", "hash": "2fbba4502156d66db0a739144ccce9a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/logger/sqflite_logger.dart", "hash": "d3f2ace1407cd47efe55881d7060408e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/back_button.dart", "hash": "035b8d3642fa73c21eafbee7851cc85d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/predicate.dart", "hash": "9d95e55b0ed6080e677989c4e1e1cff6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/sequence.dart", "hash": "0b1a431f52b54788ec3e9b6da7d87909"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/list_wheel_viewport.dart", "hash": "2baf11d03f1f50ccef5294c1fe810e25"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/refresh_indicator.dart", "hash": "e0b4c38191be9320c3113762d2dfebbb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/dropdown.dart", "hash": "095edf197865d16a71124cfaa427e31f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/utils.dart", "hash": "05778db9e882b22da2f13083c9f28e0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/compat.dart", "hash": "75e9e8da5881b6c2ebedc871d7bbc064"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id_real.dart", "hash": "0e5b422d23b62b43ea48da9f0ad7fd47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/predicate.dart", "hash": "4fcb0c3d6a9c166d16c124c91e33dcb6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/tiff/tiff_lzw_decoder.dart", "hash": "12bdab71932346bfbdc0e91ad0fd4e09"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/ray.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/input_file_stream.dart", "hash": "764833a66ea26768c45a68b689fafe19"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/encoder.dart", "hash": "dbf4f1e95289bc83e42f6b35d9f19ebe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/utils/labeled.dart", "hash": "715bccb8e9ba9889573a60bf0e457402"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/carousel_slider-5.1.1/lib/carousel_controller.dart", "hash": "4f1a097301e1ee1eac276c3dedef9bad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/webp/webp_huffman.dart", "hash": "cfa03f5e72547e1c68546ec01c0c2d35"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/optimize.dart", "hash": "a4acaadf6a817daad4c485f9c6741bca"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/ticker_provider.dart", "hash": "10cf10518abe4a916f2cb9ed7c4b635f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/semantics/semantics_event.dart", "hash": "c069ad8b31e18adb75c27530f218957a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/scheduler/ticker.dart", "hash": "3e8df17480fcb123b3cdc775ca88dd89"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/hdr_to_ldr.dart", "hash": "7e570f9c51a69cd663073e963aeb3483"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/encryption.dart", "hash": "1116d5040770ee0a85a84b5c86425796"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/psd/psd_layer_data.dart", "hash": "307d402c9f604b2e51b3f99c125c327d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/performance_overlay.dart", "hash": "3d892f04e5e34b591f8afa5dcbcee96d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/image_filter.dart", "hash": "6c0e97a3b04c9819fe935659014f92e8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/input_date_picker_form_field.dart", "hash": "f357bc5433a3205fc48000ad8c569c5b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/carousel_slider-5.1.1/lib/carousel_state.dart", "hash": "764075396ab00d4e48fbbeef4545e6a4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/colors.dart", "hash": "58490e33e6e99c4e4e313491a36cf23f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/services_impl.dart", "hash": "a6d82f072fbaf76b1276861d20c1b788"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/tiff_decoder.dart", "hash": "0c7ebb354aabfba265118aa9fc970667"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/continuous_rectangle_border.dart", "hash": "93d025adfc0409629c51036cb0fdc085"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar.dart", "hash": "04c960ae6d770135bb0b6acf14b134a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/utilities.dart", "hash": "3f5e8feebce49c954d9c5ac1cda935c1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/view_list.g.dart", "hash": "e5b4b18b359c9703926f723a1b8dd4ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/end_with_many.dart", "hash": "902509bf876a10a7b6e534a1d24fb476"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/sibling.dart", "hash": "6cee72f673d593b0b84628bf243727a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/bmp_decoder.dart", "hash": "b7c5cf2cce738208a3d82ac8d1e757c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/reassemble_handler.dart", "hash": "17dd5087a9b407563f662fc112624260"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/search_field.dart", "hash": "6dbd6092d46d1cfb37491463002e960e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/parent.dart", "hash": "210257ed62edd783098ed34d7cfb0204"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_object.dart", "hash": "470452529b3925fdb9a5865578558e83"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/selection.dart", "hash": "cc4a516908b08edff4fade47d6945e5c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/live_text.dart", "hash": "7da554c3a69a1c2d019202e3f63331c5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/ink_decoration.dart", "hash": "a2ab6e0f334e5a28af29766b82f7f4b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/default_mapping.dart", "hash": "72bbe921b18b48d52eb45666e3c52729"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v7.dart", "hash": "eaeef30b0e3cd638d4dad2b0f4db8417"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/file/content/file_content.dart", "hash": "017cae535d3d4ea7136fd64281ef1ddf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/pigeon.dart", "hash": "c11897037e853fac1b8da337c090fc63"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/camera_context.dart", "hash": "612379feb696c76bcb29da00a93467b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/core/parser.dart", "hash": "830859b7bec94f5f922eaba151827455"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/home_menu.g.dart", "hash": "11fc97acd20679368ae2eaa698c6f130"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/fade_in_image.dart", "hash": "b692d4a68a086507a66243761c3d21a6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/transform/copy_crop_circle.dart", "hash": "fdd5a0631739d413bb6249ccf6a6fd4b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/date.dart", "hash": "f36568b4288388242cb6f7775cb60c42"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/formats/decode_named_image_cmd.dart", "hash": "e603a097101ea479e134f2dffdc66fb3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/utf8.dart", "hash": "329d62f7bbbfaf993dea464039ae886c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/transform/bake_orientation_cmd.dart", "hash": "3046bb270d011f594a32dcad44aab140"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/arrow_menu.g.dart", "hash": "555fcdeebbe6517cde1cdd95133cabd7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/mime_converter.dart", "hash": "601a4561a6a4b9a0f99cdc39dbb67c0a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/photofilters/utils/color_filter_utils.dart", "hash": "4005d9195fae49bca8e58f43b74edacd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface.dart", "hash": "5145b27b3db429f9f1da26cfe563bd02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/download_progress.dart", "hash": "1e0f86acf6978afd1769e17506893606"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/pvr/pvr_color_bounding_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/path_provider_linux.dart", "hash": "8ac537f4af05ad812e8cd29f077aee24"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dislike/dislike_analyzer.dart", "hash": "d7eb1678ec74acd9857a4193fd62ed5b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/expand_icon.dart", "hash": "d6008bafffb5b2e7bf16e59a9d3ad934"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/basic.dart", "hash": "e5ebffb07608ee2f93a7aa4c23848564"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/observer_list.dart", "hash": "8ae04de7c196b60c50174800d036642f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/rotated_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/utils/awesome_bouncing_widget.dart", "hash": "f04ddafb17c97e77a9e8e2eff84979bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/palette_uint16.dart", "hash": "09de930ebe811998536764b82a19fee7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/scheduler/service_extensions.dart", "hash": "f49291d1bc73b109df4c162db10003d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/filters/filters.dart", "hash": "e5b7b6dd82f49be089f33b6f437937f3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/photofilters/utils/utils.dart", "hash": "84f339ca8d6fd9dc90c7ffee740a7359"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/scale_rgba.dart", "hash": "d473b0ca22cf7807125e9c2c24f28630"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/text_button.dart", "hash": "dbbc7f46620d816e615bbbe67eb258e7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/data_table.dart", "hash": "752b2b12f0829a4d0abb699adad87062"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/characters.dart", "hash": "99b4d15f76889687c07a41b43911cc39"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/flutter_cache_manager.dart", "hash": "4d339d186836b857e23b70679d7544c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/greedy.dart", "hash": "c138ee7ea69a6621403b3ba6973d4d7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha256.dart", "hash": "1b2339e719143f3b365a03c739ab3916"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/heroes.dart", "hash": "a7ca596d88ce54ac52360d6988d7c9c8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/method_channel/utils/codec.dart", "hash": "020552519d966b598cd3bb17849a3a49"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/text_boundary.dart", "hash": "501bafdb6d3784f18f395d40dfa73cd2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "hash": "22aea0b7487320a5aeef22c3f2dfc977"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/lib/nested.dart", "hash": "5c621d343831cbb9619557942e6b7d9f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar.dart", "hash": "8dedd49e916a59b6940a666481d82e10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter_tools/lib/src/build_system/targets/icon_tree_shaker.dart", "hash": "0763a220fcb5274b6c228b8b440ddb2a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/matches/matches_iterable.dart", "hash": "037df9e7342fc8b812d985c8b6e8a0c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/font/arial_14.dart", "hash": "2cd122d240ef9854acccbc699070e85a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/platform_view.dart", "hash": "72804f9d34b9a247c43d6cc575527370"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/list_tile_theme.dart", "hash": "822ae20c3b70355a4198594745c656f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/database_ext.dart", "hash": "f60f5b36258d9f4596034c1b3655c501"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/gesture_detector.dart", "hash": "2354ff7691e352dd0fe56e0a46338db9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/file_system/file_system_web.dart", "hash": "71b9fd89c14d2a8a39275d81a2500c5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/assets/icons/1_1.png", "hash": "9fccda0fa73f4e7870fc9db46a61b8f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/invert_cmd.dart", "hash": "0b9a225b3246273b2d97df3ab52743d4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v6.dart", "hash": "70ba25c403724d1332ff4a9e426d7e90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_directory.dart", "hash": "18b0559a8cbfb3b3a3d34bbbea4669c7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/menu_anchor.dart", "hash": "ceafe3fee68e6597afe301af3cc318c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/document_fragment.dart", "hash": "88acdeb4b5b5a9e5b057f7696935fc2e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/draw/draw_string.dart", "hash": "ac612a90b5e96f0aad11735643bf6985"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/snack_bar_theme.dart", "hash": "951bd729c13e8dd03a7f4edd8b10c06d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/annotated_region.dart", "hash": "3bc33c65fa44a57d13430fdedef82bc2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/boollist.dart", "hash": "206ef1a664f500f173416d5634d95c8b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/not.dart", "hash": "d4acdced936c4825eed27ed61fc28660"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/system_channels.dart", "hash": "b3d31c9c130a73d5425905f361f63957"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/pattern.dart", "hash": "2108c716fd8198fa3a319a1ec6cadc9d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/collection_utils.dart", "hash": "add5f0afe8e318e91950e5725be6f333"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/visitor.dart", "hash": "27780bbb98adce3f00386fc6223bf2c9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/bzip2_decoder.dart", "hash": "dc4cfb190342521ea2242f8d6414f2d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/pixel_undefined.dart", "hash": "495f6d77997e312eb8b19c91a4a26753"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/radio.dart", "hash": "9b1cee1f8aa8b638cad928232383b02b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/image_cache.dart", "hash": "4a2215ab704d09e97121c1bb71942b3f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/models/analysis/analysis.dart", "hash": "5cd7469310bf5e04503733aab53c6abb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/automatic_keep_alive.dart", "hash": "8e870f9527626d34dc675b9e28edce85"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_context.dart", "hash": "98f725d06ba20a1032cb8770d00d7fca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector4.dart", "hash": "299bd3979d7999412945ac4e3199cdcf"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/box.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/geometry.dart", "hash": "9e353a749332f6cfdbe6f0d07ff17f5f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/physics/gravity_simulation.dart", "hash": "44c1268c1ecafd3b4cd06ab573f6779a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/constants.dart", "hash": "2c6facdb1b63e687304c4b2852f6ef4c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/date_picker.dart", "hash": "561522058c0ec0f631fe295300d190e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/relative_span_scanner.dart", "hash": "b9c13cdd078c3b28c3392f0d6d5d647b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/tooltip.dart", "hash": "c816d604c95b060fbb4fa0831ad7523d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/mutator.dart", "hash": "e105e8d3303975f4db202ed32d9aa4c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/dot_screen_cmd.dart", "hash": "90986574515e1e0a5d9608a06e0a2a26"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_6.dart", "hash": "7eaf5b7e19afccfa1bde4bf16bf53648"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/gestures.dart", "hash": "55324926e0669ca7d823f6e2308d4a90"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/color_filter.dart", "hash": "bc3c12f9555c86aa11866996e60c0ec9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/data_table_theme.dart", "hash": "956c84257f1efe6f10ab24f3d6702307"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/colors.dart", "hash": "5ed8acdae7dd3501b64b0ff3e33c1f45"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/undo_history.dart", "hash": "73089c9737db54a05691e09bc9fc1bcd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/multidrag.dart", "hash": "f56109c40e6fe9e53f9c6ad021d25ff5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/diagnostics.dart", "hash": "5d7b0ee48c302285b90443514166c2d2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/route.dart", "hash": "7e827f3c407d93dfa01d1c8cac14af80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/assertions.dart", "hash": "d77516b410bc8410c6128cb39240acdb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/LICENSE", "hash": "d53c45c14285d5ae1612c4146c90050b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/psd/effect/psd_inner_shadow_effect.dart", "hash": "d428598d2741da83b008efc24d567204"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/ink_sparkle.dart", "hash": "204fb623e2b782051e9bcb6e320e97c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/luminance_threshold_cmd.dart", "hash": "e019bd8c8ae15443e30b7b1bfffd611c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span.dart", "hash": "b7c2cc8260bb9ff9a961390b92e93294"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/util/binary_quantizer.dart", "hash": "1b5ec9f3c6651e6b2910e2304db7f09b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/circle_border.dart", "hash": "a2aa815908f2e15493e374b9380e558a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/gzip_encoder_web.dart", "hash": "ef94bee55980ea8f1e63f68c441cbf64"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/dematerialize.dart", "hash": "6f6ced37453e06f063a482bcb9509370"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/object.dart", "hash": "0cd72a3b3ab10728d2b3234014f43d83"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_metrics.dart", "hash": "6f18c18a1a5649f27b6e0c29dfba4dc9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/tonal_palette.dart", "hash": "44b3c2a3d6e67a3213a49cce58fed932"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/actions.dart", "hash": "1c7764fa08241a44711301c74fb658df"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/process_text.dart", "hash": "94235ba74c3f3ad26e22c4b40538ce07"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_list.dart", "hash": "5b894ae18be3e2442a34288833184ca9"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/lib/providers/gallery_provider.dart", "hash": "06bc000dbffa7c5db2c5f0592ecff21e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/list_converter.dart", "hash": "5f5f3a1074f40b8fc37c2b3ba5ec0432"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/http.dart", "hash": "d9696ef3a9cefaa6bf238175fe214b0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix2.dart", "hash": "7f164e577cfcf8c8295947195cde2a7c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/predictive_back_event.dart", "hash": "16859f5e798cf33fc3c76a7a3dca05d7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/theme.dart", "hash": "d5363426c1acae1c7410b4096cefd94d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/matrix2d-1.0.4/lib/src/extension/extension.dart", "hash": "009dadc7d28cf4b529b94abb7c4e2814"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/button_bar.dart", "hash": "42c4c0281ec179aea5687dbced56aca7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/adapters/pigeon_sensor_adapter.dart", "hash": "0ea905eb1f82abdfaade36213aff564e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/exif/ifd_directory.dart", "hash": "1cda8dbeca29d30384d3d07595bd752f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/assets/icons/16_9.png", "hash": "8d1887c08fb8ab6abffa45a11c81b8dc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/divider_theme.dart", "hash": "04f538d5fc784c89c867253889767be4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/png_encoder.dart", "hash": "7258d5b358b97a8ff41e3698bd52c351"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/edge_glow_cmd.dart", "hash": "7a4f6c724db994a82b78c1ce8b41879d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/dtd/external_id.dart", "hash": "348e54c1032cec91d7a1a5cfce8c2098"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/text_theme.dart", "hash": "f60846aa76dab98607aa06c9bd6cf1dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/core/exception.dart", "hash": "5934d7c0ee1ff94ec21aad560e9ed8ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/buttons/awesome_pause_resume_button.dart", "hash": "a2b553db68120adba1e321bb8f281023"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/text_style.dart", "hash": "0cf873bc441372ec89d746477273af13"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/search.dart", "hash": "66a927b3f610db5ff8c77a6ba3ccee0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/models/media_capture.dart", "hash": "03d7db28b1559630e89e29f193833316"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/matrix2d-1.0.4/lib/src/util/util.dart", "hash": "b8f0b6ed36bf556ace9a132776dbe7dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/bzip2_encoder.dart", "hash": "901730281b8605ecb8ab09a1708ef9ec"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/disposable_build_context.dart", "hash": "edd2f9cabffc7ea6a5a9497a1b1beccd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/navigation_toolbar.dart", "hash": "5be90cbe4bbf72b0264413e4ccb5c275"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/search_view_theme.dart", "hash": "4d673eddc0bd2289539b66a92faae868"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/texture.dart", "hash": "cd6b036d4e6b746161846a50d182c0b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/exr/exr_image.dart", "hash": "a9d93285749c8d79300d9b5d6e500701"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/ink_well.dart", "hash": "38df6f8cafb853c1acf0f6e6a4b4950c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/bzip2/bz2_bit_reader.dart", "hash": "68cac9c19e7454fb503c31e3042bbcc0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/sqflite_import.dart", "hash": "afa8ae229bc41c02a6cd9dcbe10a81e8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/serialization.dart", "hash": "f20071b459b9bbb98083efedeaf02777"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/preview/awesome_camera_gesture_detector.dart", "hash": "4b17682eb71cbd32d4956cd1e5cd2498"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/min.dart", "hash": "ecfd8a09746c8bbb7b51d4741fb4645e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes.dart", "hash": "3e82e75a5b4bf22939d1937d2195a16e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/cdata.dart", "hash": "008d33cc2aea11e7921ee238469947b2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/provider.dart", "hash": "08fb5f27432143c416f473db763fa8c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/location.dart", "hash": "fb2c02d4f540edce4651227e18a35d19"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/wrappers.dart", "hash": "21e56afda1f096f0425a34987708ed56"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/decorated_sliver.dart", "hash": "4b50828d394e7fe1a1198468175270d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/exception.dart", "hash": "9011b30a404dec657806a780b55d0610"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/object.dart", "hash": "daa0c9b859ed1959e6085188a703f387"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/optional.dart", "hash": "7d49b944ccc5ee228590126488731a95"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/ram_file_handle.dart", "hash": "cc9eeca84124af552ad4fe888cb66f9d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/timestamp.dart", "hash": "bca54a6e3c0b80a2300ab9ae4e9db4e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/combine_latest.dart", "hash": "0c4028018783c732ca451e7fff693d3a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/name.dart", "hash": "da50c399c40281c66d3c2582ac225276"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/internal_style.dart", "hash": "974d0c452808a1c68d61285d0bd16b28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_link.dart", "hash": "600a83d8e8dcbc1fde99887eea16f18e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/formats/tga_cmd.dart", "hash": "91060066774223dbf500b4d029c905d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/psd/psd_blending_ranges.dart", "hash": "6f15072cf197d2272821e06ece38757e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib/xdg_directories.dart", "hash": "737107f1a98a5ff745dd4e3236c5bb7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/dither_image_cmd.dart", "hash": "c5103834ded9acc01d905fdfcb93ffe3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/tga/tga_info.dart", "hash": "82d61c1f663fd16b4db7f139a75f5c22"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/extensions.dart", "hash": "38e17b28106d00f831c56d4e78ca7421"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/cast.dart", "hash": "dc379ed249557649f50b9c27d0033be6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/switch_latest.dart", "hash": "a52ae2e097914c25b04abb01abf02183"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/container.dart", "hash": "f663757bacdc28f2692b30a293d75146"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/inherited_provider.dart", "hash": "dd618a65e1f3400d8224fedb42a1881b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/icon_button.dart", "hash": "5d99a505ddc69d5accc4e5a83f5cfa4d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/bottom_navigation_bar_item.dart", "hash": "900a13c9fcd73f4e8e3d069d76af6ffa"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/slider_theme.dart", "hash": "86d7d305c24e6073b89718914fcd3ee0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/draw/_draw_antialias_circle.dart", "hash": "2a174232d25afd4bfcbc221b4f4d847d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/composite_subscription.dart", "hash": "76cc6c2b845bff11813d968688280b36"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/psd/psd_image_resource.dart", "hash": "d8a9ef3d1223543b7b87fb672da94c62"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/platform_view.dart", "hash": "a8513860b3b4c160b57ca6264bc0acf8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/.dart_tool/flutter_build/d4d4db9379df09af04f05ad89c774e35/app.dill", "hash": "78fd97d6ebaee7b95e50734556f28955"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/badge.dart", "hash": "cd7cadd0efa83f26d401a14e53964fd4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/transform/trim_cmd.dart", "hash": "bc5fc832001ea87b8c843eb1cf96bb0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/noise.dart", "hash": "2253e94fd7e821a4a476c82bb088f48b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/formats/png_cmd.dart", "hash": "1d0bc56c1f945a2a97a85539b60f394c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/data.dart", "hash": "d7fab9eeba6ce2b3fae0a93d5622ac93"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/factory_platform.dart", "hash": "2441a967786bd149053b72e22172ce60"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wsmeans.dart", "hash": "6c6dfd5ba4546c1f32201555d6cff215"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/expression/result.dart", "hash": "bc503b6c5e3658a13efaee4e0638935a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/lib/src/filter/custom/advance.dart", "hash": "21e074989c001534ccfd86da3d8fef59"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_file_io.dart", "hash": "8830333c78de58ad9df05d396b651ef7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/slider_value_indicator_shape.dart", "hash": "949350c1ca059ddb517d7f4f80b21ecd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/take_last.dart", "hash": "08f42ef74f129fde820b3414026b8d34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/src/image_provider/cached_network_image_provider.dart", "hash": "8a55a3a014cc2ba2dea85787efc98ee4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/forwarding_stream.dart", "hash": "3473bd2c623a639ff8cc439276a3825f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/floating_action_button_location.dart", "hash": "964f3ee4853c34a4695db0c7e063eaa3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_scheme.dart", "hash": "7536ace8732469863c97185648bb15a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/util/color_util.dart", "hash": "6fbb540fd6560a81f3d374827ce5ab82"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/constants.dart", "hash": "0672d853d5097a03eddc7dbe558eeabd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_content.dart", "hash": "78e53d9a4963c0d19c5ea355a0946e5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_link.dart", "hash": "733eb3422250897324028933a5d23753"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/binding.dart", "hash": "530c4f96f1475cc4e4128ffedd705028"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_map.dart", "hash": "13c9680b76d03cbd8c23463259d8deb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/logger.dart", "hash": "2d92ebd3f2b641fb2975909acbdb6f09"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/card_theme.dart", "hash": "5d8e29422039d9dcce6908b427814d80"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/capabilities.dart", "hash": "5fe5b5ed3ec92338a01f24258b6070a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/streamed_request.dart", "hash": "c738f304008379170f7306e4368d29dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/src/platform_exception.dart", "hash": "89ca6560d39efc4e7a136aafd44f8e49"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/service_extensions.dart", "hash": "eb115c2e8f0ff170bf26a44efd1b5c05"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/node_encoder.dart", "hash": "85ca5d0ad350ba37b698247c24cb70a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/unbounded.dart", "hash": "a617a91b12a3156406da1d95552aa4a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_7.dart", "hash": "c3e5aaaf36524bf9927e80f60f3b0bdf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/max.dart", "hash": "82294310993896043f681e7fd66c4e56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/mixin/constant.dart", "hash": "84fdc97cdb402f94c301f5154682112f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/physics/tolerance.dart", "hash": "43ef2382f5e86c859817da872279301e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_vibrant.dart", "hash": "5b04f80518a8417cb87a0aec07dacf4f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/button.dart", "hash": "78f88eba40852ba0b7700d94f3ecfec6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/parser_exception.dart", "hash": "a62996936bad6c27697a35bed070547d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/restoration_properties.dart", "hash": "a8fdf31698b305c9fdad63aa7a990766"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/min_max.dart", "hash": "50c5f00339854085c2f637109c4166f3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/directory.dart", "hash": "8f4de032f1e2670ca51ce330a4de91a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/cur_encoder.dart", "hash": "c605efd510079bb2d0ed858edfcd7c79"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/buttons/awesome_location_button.dart", "hash": "8d32433abf349776f3b8cc7c6a5b600c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/lib/src/managers/notify_manager.dart", "hash": "c1986e174830b820232a8a5d08e32ed4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/flatten.dart", "hash": "481d21ef07dee6f82302a015f989b597"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/sliver.dart", "hash": "ebd06d8f4cce7c59735a2ba28d6dba97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/monochrome_cmd.dart", "hash": "27642e9e419990ac9ba91b9090cece8c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar_button.dart", "hash": "bce1e8ef07d9830bbf99031d77e0b9fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/exception.dart", "hash": "9a1e38007af02b923a4076ab4e989f48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/change_notifier_provider.dart", "hash": "3ce0eeefa3058c1955fb1f435ce9928b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/where_type.dart", "hash": "93f43c6a287e8cd98477a02e6aa0da8d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/input_memory_stream.dart", "hash": "065a3cbd920a9379dae6b8659869637c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/debug.dart", "hash": "17fec0de01669e6234ccb93fc1d171f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/utils.dart", "hash": "105813825251a3235085757d723ae97c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/method_channel/method_channel_permission_handler.dart", "hash": "219013e8bbe8cf19fde2d3520067ba39"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/operations.dart", "hash": "5dbef5156368d0f25b59750608e025a6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/localizations.dart", "hash": "a64e270c19c9e9ed0c5d9a17e0c4a5d0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/page_storage.dart", "hash": "e5a3ca065f292c0f0b0cca0a55df41aa"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/selection_container.dart", "hash": "97359ca5bc2635f947e7616f792565c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/states/preparing_camera_state.dart", "hash": "641aeb49570b2700888ead93562e3a40"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/input_stream.dart", "hash": "f4de2ef5869ce0806f1e4f449d80e390"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_simulation.dart", "hash": "b29e302994b1b0ea5029734406101b8e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/text_editing_intents.dart", "hash": "47ccb32c843b4075a001e612853b2a31"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/navigation_bar_theme.dart", "hash": "b5eb2fd4d6d9a2ec6a861fcebc0793d2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_configuration.dart", "hash": "c9ab6d9cf33f78fef3ff4ad99fc73390"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/lib/src/internal/extensions.dart", "hash": "60deddb26eb6b4060bcfe9239b9b0c95"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/binding.dart", "hash": "61cf3ac380d43d042f8d9b7e7f6a11e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local.dart", "hash": "22b26473ffd350c0df39ffb8e1a4ba86"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/image_data_uint32.dart", "hash": "2803a6ca54b24d1474d5d787e8fea8e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/models/sensors.dart", "hash": "176b4f0ce15061ee2abdec97fb7ec475"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/unicode_glyph_set.dart", "hash": "cdb411d670a094822c46ead81fc1c4f7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/action_chip.dart", "hash": "c7d65c476f653e952aedcb0cbcab3c73"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/physics/friction_simulation.dart", "hash": "732535ba697d95c80d1215c0879477f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/cam16.dart", "hash": "ca959e5242b0f3616ee4b630b9866a51"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/noise.dart", "hash": "206b1db3ce5f7b9e5efd220712f8d391"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/delay.dart", "hash": "46133866c09984f60ac2731cf9094a27"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/icons.dart", "hash": "790dc5e1e0b058d13efbd42a3f46498e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/vignette_cmd.dart", "hash": "dd30d55a48503e44af2e8ad65d2c4161"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_notification.dart", "hash": "269af8ca7030ccfd9c868fe9af8a6b0a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/predicate/string.dart", "hash": "be2e3e8ab6ed0e2b2b554a26b78f91f0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/time_picker_theme.dart", "hash": "b269f9d6378b540b7d581db466ad98d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/transform/copy_resize_cmd.dart", "hash": "58eb593a77c2e8c8004458067d543175"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/lib/sqflite_darwin.dart", "hash": "b1cb91ea7a56d612d5792dbfe439f2d8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/overlay.dart", "hash": "cd0cbb4d29516ed6b03d1c68f0c08477"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/sliver.dart", "hash": "dc037755b1140b31ffc8295fb9570cff"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/desktop_text_selection.dart", "hash": "dd3402d5403be91584a0203364565b1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/draw/draw_string_cmd.dart", "hash": "d78319f2b32edf36298d637b60287e9f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/execute_result.dart", "hash": "549457b9f5d49bc503135fb6d367c9a8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/beveled_rectangle_border.dart", "hash": "d8060c05b658b8065bc0bfdff6e4f229"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/states/video_camera_state.dart", "hash": "ee5e5e14cea1dee0967f82472c607c38"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/long_press.dart", "hash": "c97a8ffd51479d05a18a54ac27ccba15"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality_set.dart", "hash": "4b5d82ddeb09bc46ae0e980616ce0109"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/file_buffer.dart", "hash": "bac89ce78a84824b60c4bf876e5510ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/LICENSE", "hash": "9633ac2bb6bd16fe5066b9905b6f0d1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/normalize_cmd.dart", "hash": "d58a42199f17a4bbe0a9cf7d7bef2a8d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_3.dart", "hash": "050f96bbbf01a1f86e208d7d8cc08901"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/transform/copy_crop_cmd.dart", "hash": "4a743abb78aeca130b552786bf2af662"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_iterator.dart", "hash": "6c54f90e0db5f42a13be6b3efeb4a04d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/scheduler/priority.dart", "hash": "ac172606bd706d958c4fe83218c60125"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/binding.dart", "hash": "a594e2e46c047f44912e93f2e38f4a47"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/nav_bar.dart", "hash": "8e471191ea3b6cdd6c970bf5be4cc86e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/size_changed_layout_notifier.dart", "hash": "8a39bdc324d0ff25097784bd98333c08"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/pixel_int8.dart", "hash": "78231676670143c7e181afab475b6a9d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_layout_delegate.dart", "hash": "82604e7dbb83dc8f66f5ec9d0962378b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/decoration_image.dart", "hash": "dd510cd97dc23d22aebc7b60affd6329"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/text_selection_theme.dart", "hash": "166478d231aa67eb8e47a7b559955e6b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/banner.dart", "hash": "c9cd996cea2334f644c74ebbdb41f7f5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/action_buttons.dart", "hash": "aed826e965e4aa2fdb3466d39e33d824"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/proxy_sliver.dart", "hash": "1244032abcc6103795809163331238a9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/bottom_navigation_bar_theme.dart", "hash": "307c2ee6ebc77b9995c2799e8e0bed81"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file.dart", "hash": "1026f587763defb6fb1eec88c2154a3d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/jpeg/jpeg_jfif.dart", "hash": "2442a18c6497fffdcc95eabeca48fd2a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/analysis/analysis_to_image.dart", "hash": "b7e69aa0a84f11ad6467dc1d823c5ad1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/src/x_file.dart", "hash": "d06c42e6c83be207b86412e11889266a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector2.dart", "hash": "6a0fa6360b3aca8deb85dc7d88176eb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/adjust_color.dart", "hash": "baa14269ec1e45d422eda6498f06a64f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib_decoder.dart", "hash": "580ac3706ec7b899665c577c7eaac060"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/physics/clamped_simulation.dart", "hash": "5979a1b66500c09f65550fab874ee847"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/blend/blend.dart", "hash": "f487ad099842793e5deeebcc3a8048cb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/copy_image_channels_cmd.dart", "hash": "653f8c48ce67bcbc5ff0fe79ebad7219"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/sliver_tree.dart", "hash": "5cbb66bc2f7ff989a32bc1e5ce5971e6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/table.dart", "hash": "eda0152837e3eb094d8b1f6d0754f088"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/file_info.dart", "hash": "2f3e8198efb4b9ec92c0126b25986acc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/jpeg/jpeg_scan.dart", "hash": "713f0be40d2062f876f91dde39db3b14"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/menu_bar_theme.dart", "hash": "e4a748e0ab7265def948ce2f5dbce86e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/group_by.dart", "hash": "6c3232594edbc47bd6ec36d04c194a9a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/transform/trim.dart", "hash": "5c01317063bf057e501254cbefe97caa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/lib/src/utils/convert_utils.dart", "hash": "c71d8ff07f26d08c81871d7aa9e5eb22"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/lib/screens/gallery_screen.dart", "hash": "34a5e986e72ddd2093c95b981278eedc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/tap_and_drag.dart", "hash": "a2f376b739fa28d7a71312ecf31d6465"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/elevation_overlay.dart", "hash": "ea5bbc17f187d311ef6dcfa764927c9d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/color/channel_iterator.dart", "hash": "95711b018062a6808f19399b4c2024ba"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/widgets.dart", "hash": "946e37d543d3912bef54a551fb02ea1d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scrollable_helpers.dart", "hash": "7f2ccd6eece375fce2e247d3995e45c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/local.dart", "hash": "e81341d4c5ee8dc65f89ae4145cf2107"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/flex.dart", "hash": "4ec9c8dd6d6ecb43d26ebaef03abd1ab"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/oval_border.dart", "hash": "c8a14f8ecb364849dcdd8c67e1299fb3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/image/convert_cmd.dart", "hash": "d04c59459b26478acb6e357a42fafd62"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/packages/camerawesome/assets/icons/1_1.png", "hash": "9fccda0fa73f4e7870fc9db46a61b8f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/carousel_slider-5.1.1/LICENSE", "hash": "d962842ab77037d9a6da813a6c9e0ca4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/gallery_saver_plus-3.2.8/LICENSE", "hash": "0cd3844b792d5a0026a5f6c5b520cd0e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE", "hash": "1bc3a9b4f64729d01f8d74a883befce2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/image_data_int8.dart", "hash": "0f9942877b143a7803215e34216d43af"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/_isolates_io.dart", "hash": "f90beedee11a434d706e3152bfb2fd15"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/parent_exception.dart", "hash": "2ede71f09a240decbc57417850f8feb7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/time_interval.dart", "hash": "17db414327e1f763806c112c6f664ca8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/utils/separated_list.dart", "hash": "031cc72717282ff9f7f588e73fb0fd0b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/viewport.dart", "hash": "68eb8647107febe1419211e153b27a54"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_2.dart", "hash": "dc92a928880163bbe0232a641f7f4276"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/future.dart", "hash": "8d3f31cb53177f3f6315575373249597"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/exr/exr_compressor.dart", "hash": "88e7f9ae5faaeba57f91aaa569787701"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/formats/bmp_cmd.dart", "hash": "a851d181c4365ea3dcf32a3e36aea9b1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/cdata.dart", "hash": "a1bc06d1d53e9b47b32fbdb4d323f44d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/constants.dart", "hash": "3b481084198e4581293dd9ddddb9afb4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/behavior_subject.dart", "hash": "86d361932e590380696b3189090d1034"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/search_anchor.dart", "hash": "873f01c9dae2d98c8df6fc08ca543aca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/character.dart", "hash": "f6f8ad33193db66deb89d68e406eeaf9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/analysis/analysis_controller.dart", "hash": "d2f276204d6b9ebb843f24a66f2f1195"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_random_access_file.dart", "hash": "576c23d693f7712935103974ed9312ee"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/play_pause.g.dart", "hash": "9ad11b4bdb179abe4ccb587eb0e2aebc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/pages.dart", "hash": "068ea69f3733bd1aa72b910e51b41b12"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/octo_set.dart", "hash": "0d750078c87ce8f99c60c3c76305c11a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/animation/tween.dart", "hash": "73f043194b9c158454e55b3cafbdb395"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/accept.dart", "hash": "740f17823564c3c7eca15bca5c110e17"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/system_context_menu.dart", "hash": "5666a74f3f21ee2fa9b0b2aa37360700"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/selector.dart", "hash": "6a72a2ba15880cab1e1d9a28a94f1a2d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/focus_scope.dart", "hash": "fddd73db94bb2fa3a0974bed845f32a8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/chip.dart", "hash": "728c8c2ffdc4b584c67df65b41e6461f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/preview/awesome_focus_indicator.dart", "hash": "4d116967a47ac4731777e1c7ef70dcf1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/magnifier.dart", "hash": "4da5ad5941f2d5b6b3fbb3f7ea217b41"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/file_system/file_system.dart", "hash": "a0432b1db3ddabe8c3edb6f542c9ef48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/core/result.dart", "hash": "fbca1545a7230f0ea39d7884a1722475"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/LICENSE", "hash": "3ae6ba09a5298f314976fa1d1fd58c3f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/canonicalized_map.dart", "hash": "f5e7b04452b0066dff82aec6597afdc5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/predictive_back_page_transitions_builder.dart", "hash": "cb745b78bdb964c02c1c4a843b9c1e7d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/models/permission_utils.dart", "hash": "29e0e909281e882f4c5b38be5459404a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/box_fit.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/palette_float16.dart", "hash": "f287ac1a964513fc6d95b076583b88cb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/quantize.dart", "hash": "a24706f0a889648802cb7c732d853a0e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/none_of.dart", "hash": "2080f99186cef2d2ec3f4c6c5b7c768b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/keyboard_listener.dart", "hash": "bd3f0349089d88d3cd79ffed23e9163b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/placeholder.dart", "hash": "a69e90f683dddaf61ae8d7f094219026"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/synchronized.dart", "hash": "044e7c8ac3258945fe17e90e1a4fff51"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_buffers.dart", "hash": "4b495ff6681b3a7dda3f098bf9ecc77d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/comparators.dart", "hash": "8ac28b43cbabd2954dafb72dc9a58f01"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/image.dart", "hash": "ad04260981a11ff8c051f8b3516f7276"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/limited.dart", "hash": "bfc3692929b6ffa40605428f3cc70e86"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/clock.dart", "hash": "d52c28f679ecf880a21c3ba08df59267"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/LICENSE", "hash": "c17706815151969aa7de6328178cc8bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/lib/src/filter/classical/filter_option_group.dart", "hash": "61734be5adab0e1c7a39bb17c27a7d0b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/bottom_tab_bar.dart", "hash": "019f7b771f1865632d5a36c9e74296db"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/app.dart", "hash": "ca378f8a4dc93cea9ab759f410dcfdb6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/keyboard_maps.g.dart", "hash": "2c582bec6fc77f68c975f84d2252ed8d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_client.dart", "hash": "32a40215ba4c55ed5bb5e9795e404937"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/cache_object_provider.dart", "hash": "39e587e00bba5c8a7978fd25cf983cc8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_plugin.dart", "hash": "ae3622db94fb8368f3577f6e71f3ea4f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/message_codecs.dart", "hash": "256d1c386e48e198e2e0a04345221477"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/sliver_prototype_extent_list.dart", "hash": "9645e1d88d63387bb98a35849f4cbe53"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/switch_map.dart", "hash": "56bb06e8c5f7f7892ae9eb352dd91f9e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file.dart", "hash": "3e30d0b7847f22c4b3674358052de8b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/logger.dart", "hash": "0cdc9d79f7fc4d0920bc6a8fc02e6872"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/sketch.dart", "hash": "0d78b4566b5317bfac79ecd4a8c77454"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/palette_uint8.dart", "hash": "e1f04b9aee61fe07cd4bc0e10b0ba86d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/viewport_offset.dart", "hash": "e45c87e4aadaebf7ba449f4c60929928"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/inherited_model.dart", "hash": "940daf4491e3ab2e15d7eac5d6ce6b23"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/navigation_drawer.dart", "hash": "7755bff1bceea0db42330320ad10baad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/predicate/any.dart", "hash": "35536afe2f48001aa7c588b131051b83"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/predicate/pattern.dart", "hash": "d881c458d06573eb887bdf0f3ce9f586"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/raw_keyboard_web.dart", "hash": "547eac441130505674f44bf786aee606"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/definition.dart", "hash": "8680f57e6ae9665a5f051c06c1efc688"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/lib/src/filter/path_filter.dart", "hash": "0108cdae02aacd7108c72b95c75d6d2c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/text_editing.dart", "hash": "9298606a388e3adb5f1bbe88ae45b1e6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter_tools/lib/src/build_system/targets/native_assets.dart", "hash": "55b4fed5dadc735394ecc0e13867c2eb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/archive_exception.dart", "hash": "a975475afc0c5c2b69ea26d2f4df57fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/io.dart", "hash": "119ed2f372555dcadabe631a960de161"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/helper_methods.dart", "hash": "0d0350902fa7b7c829baf0666f1a74dd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/list_tile.dart", "hash": "8b20b418804c1d6e59afdfcae6e84728"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/iterable_extensions.dart", "hash": "5843b4750179f6099d443212b76f04a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file_system_entity.dart", "hash": "c69896f9c186aab01f7d11624f5c7d4a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/text_input.dart", "hash": "a4c1dffb16d559eb4d22bac89777780e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/factory_impl.dart", "hash": "65614758273c0b82b4ce22b3728be36c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory.dart", "hash": "4ce56dab766f683c213da41402d17049"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/deflate.dart", "hash": "089fe7c30acaab000337d853f9c72307"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/pixel_float32.dart", "hash": "298adc7397487844475171f304fadb1d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/color_utils.dart", "hash": "0938e0447f447ceb7d16477a0213ce2c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/magnifier.dart", "hash": "b56cf23d49289ed9b2579fdc74f99c98"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/consolidate_response.dart", "hash": "04451542afc67a74282bd56d7ee454f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/assets/icons/minimized.png", "hash": "1589a3aefe13c85c8bf2296863881c3d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/material_dynamic_colors.dart", "hash": "81bf43e01741bf8b9df15ec37ffbc9ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/webp/vp8_filter.dart", "hash": "795e142a80341b039e7f08295ea18f07"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/button_bar_theme.dart", "hash": "0f717ff4ecfdaa0347894abbedd5d1e9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/key.dart", "hash": "3ee6304161ca2993b303a8074557fe66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hash_sink.dart", "hash": "ec5409b8e30f22b65a7eee1b00a12d06"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/formats/ico_cmd.dart", "hash": "e959068c47c8cd7be1890b24d556a242"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/hardware_keyboard.dart", "hash": "a32174b6de983c1652638940e75aae6a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/jpeg_encoder.dart", "hash": "313ff432f60453c4fe88c3876d00b48c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/_crc64_io.dart", "hash": "8c90185708fa02620fdeca18f01e0d30"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/boundary_characters.dart", "hash": "9d1525a634d27c83e1637a512a198b4f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/comparison.dart", "hash": "643ca26571c2ba94477233dbb914b1ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/notification.dart", "hash": "b9609815ffdb79eb76f2cd3a77a60906"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/tiff/tiff_entry.dart", "hash": "d157821a585c7027d774aaa2c13b4b06"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/scheduler/binding.dart", "hash": "2122bbdb5de249ae3f2444fe234a5afb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/internal/reference.dart", "hash": "f25bbc73708cc35ac55836cbea772849"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/system_chrome.dart", "hash": "40d43557904504dbd816a205b73461b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/declaration.dart", "hash": "7b254933211feaa1ea185b61dc9b12af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/draw/draw_pixel.dart", "hash": "a2964b52d550e415ac0c837cfb347bcb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/transform/copy_flip.dart", "hash": "e4c197859634f491431403eea3c5f32b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/ink_splash.dart", "hash": "31b0d2bf647a0ce615f4937dd5307b1c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding.dart", "hash": "328ff975234df68963cb19db907493ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/draw/fill_circle.dart", "hash": "c827466c9763f1775dd1a4cc7901ade3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/typography.dart", "hash": "eea9d5a977d3ff4f46bb63a0f140c738"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/utils/utils.dart", "hash": "b498b8a0aebd2f2bde3bb5d94fffd79f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_request.dart", "hash": "8ac37c0f7bea9c97df2a0bef6bb3f858"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/stack.dart", "hash": "2cf5ffb71954128b5e80f17a36bcde43"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style.dart", "hash": "bfb39b98783e4013d9fe5006de40874d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/bottom_app_bar_theme.dart", "hash": "ff2b2e7159e19374f968cf529da25c01"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/case_insensitive_map.dart", "hash": "5893c7d3910e8924bd2dccc8837775c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/states/handlers/filter_handler.dart", "hash": "15c094ac29e9e55243a0d65ad5c97900"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/bulge_distortion_cmd.dart", "hash": "8515f54c560bbdc48d57058c148aa725"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/proxy_provider.dart", "hash": "57b51f6f00c6bc3a29abbf83fbd804f8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/wrap.dart", "hash": "b656f459fa4dd04f817455858d3dd20f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/preview/awesome_camera_floating_preview.dart", "hash": "3a93e95953be849283bf51c4a87af7b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/prefix_name.dart", "hash": "fbb3e43ae57262b3fc190cb173a7b5bf"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/paragraph.dart", "hash": "d9eb28b2265932eb628ad0c3a123bee7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/bottom_app_bar.dart", "hash": "fa60d1a6f81796232bc16dae4ed5f4ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/replay_subject.dart", "hash": "7929b4d3e79087536edc9cd260f8d4c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/pixel_uint1.dart", "hash": "323427984986cd5e4c96dff3b76a1f96"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/util/_circle_test.dart", "hash": "e9b0af130e8d3dce1c3599c8c471043b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/bottom_navigation_bar.dart", "hash": "ccb3c80f13485133893f760c837c8b62"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/text_form_field.dart", "hash": "28219fbae9045c4c3217c0f3fd6fa7ef"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/.dart_tool/flutter_build/d4d4db9379df09af04f05ad89c774e35/native_assets.json", "hash": "f3a664e105b4f792c6c7fe4e4d22c398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/palette_undefined.dart", "hash": "1a9dbfd5ac28c1d49c7e2af51b2b0f1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/pick.dart", "hash": "c60b204fb5e7d501c0addb330c88d2de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/utils.dart", "hash": "e85b4f3cf370581b3ef11497a9a5bce3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/pattern/parser_match.dart", "hash": "d742d41268dec3da5e669142ae344928"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_exception.dart", "hash": "c39101179f8bdf0b2116c1f40a3acc25"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system_entity.dart", "hash": "67918403456e9e1c17b3375ea708292c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/constant.dart", "hash": "8d5660686b2687f3947b822758c82942"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/platform_views.dart", "hash": "1d3f3077faee6bebdc5279446f541502"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/placeholder_span.dart", "hash": "d2386b256656121d501a16234b008e2b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/lookup_boundary.dart", "hash": "37f181e3096dc69dc408bf7d07fcd39a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/tga_encoder.dart", "hash": "cbbb8fbc82acaeb130b291b16b692b6e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/LICENSE", "hash": "86d3f3a95c324c9479bd8986968f4327"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/end_element.dart", "hash": "813218451c1d8dd310e1233bd4ca7a4a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/mouse_tracker.dart", "hash": "0c402ad9ba3f3e4d7f45f24b27447ec2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/form.dart", "hash": "8678afc1455a658ddf2382ad887eec66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/backpressure.dart", "hash": "33135edc8fab501ab562a08b8322d832"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/list_extensions.dart", "hash": "9f8b50d98e75350b41d40fee06a9d7ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/pixel.dart", "hash": "6c569b6193f34e70df9beec2a7a6db7d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/start_with_many.dart", "hash": "1f04f05279660e26d85fff2f5dfec4c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/sql.dart", "hash": "597e7b293e2531edc3ef788375e11c67"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_value.dart", "hash": "6f3422c300e4f005e63a4246631f3372"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/convolution.dart", "hash": "e3a1ebc5f00c4124a4ac384c13e049cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/billboard_cmd.dart", "hash": "0692ead9208925419f60609784cb0595"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/selection_area.dart", "hash": "ed28f6ca17f72062078193cc8053f1bb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/drawer.dart", "hash": "f26e2cb53d8dd9caaaabeda19e5a2de3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/raw_keyboard_listener.dart", "hash": "1f131d7f971396d52ce5fe78ae6a8a83"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/input_border.dart", "hash": "2aec07fe4a1cd25aa500e5e22f365800"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/concat_eager.dart", "hash": "28788651dbafca42ae0d6023352274f3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/util/min_max.dart", "hash": "14aea61ca89fa6563d4850b2c9c9eb82"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/alignment.dart", "hash": "ccdbac117e9349d3ceaa005c645277e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/webp/vp8l.dart", "hash": "151883455be62fe5c4dab07db39bdc37"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/models/camera_flashes.dart", "hash": "71cb8b2a77e042a3af0e215c1c5714fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/queue_list.dart", "hash": "02139a0e85c6b42bceaf3377d2aee3de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/default_cache_manager.dart", "hash": "8ad6f50f623fbd97c2aa23d86d3c22ad"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "hash": "a0e89676ccae6cf3669483d52fa61075"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/subtree_selector.dart", "hash": "e5f1007517b62683935488c5189ebc5d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/drag.dart", "hash": "43ba7557388f413902313df64e072389"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_store.dart", "hash": "b72b9cd4de477e80296c7f58bc9f5f30"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/semantics/semantics.dart", "hash": "c789dd4004265224055546db82c4c7c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/lzma/lzma_decoder.dart", "hash": "a3a00f8aba49a8beb05a311ce1eb7397"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/transform/copy_crop.dart", "hash": "f106871e7dce8c360df92f5cc1216626"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/util/quantizer.dart", "hash": "208f90bc44c986230e1edde18b4f2499"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/flutter_logo.dart", "hash": "044d6bef26a97ada1d56ff6fe9b7cc14"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/config/_config_io.dart", "hash": "7d313ac68ec3f410b31e39f450fdaf0f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/map_to.dart", "hash": "a28073e1b0a1ffd4999c24379f1dfe02"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/raw_keyboard.dart", "hash": "02dabe6a8cd832d69b4864626329ef30"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/slotted_render_object_widget.dart", "hash": "74708cb40b7b102b8e65ae54a0b644be"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/overflow_bar.dart", "hash": "d2694042e337ac1f2d99602c25be195a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/util/float16.dart", "hash": "b970536f089418027069a8d3b1884399"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/element.dart", "hash": "23db80d93d6f37b73648e830d1dda0f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/transaction.dart", "hash": "95701ee376845a2050d29814b7acc7a4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/animation/listener_helpers.dart", "hash": "72bbc3da5da130fb11bb5fc65614653c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/clock.dart", "hash": "2c91507ecca892cf65c6eaf3fbe0a7e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/empty.dart", "hash": "ac4e4c808dab498eb2d5c7f813a5006b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/mouse_tracking.dart", "hash": "5da121a0d3087e7cf021bfcdeb247b77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/jpeg/jpeg_util.dart", "hash": "b7f2a36c743d1bb4abd3c4d1ff0101dc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/separated_by.dart", "hash": "04b800051d2d913cafc3733ee1bb21c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha512_fastsinks.dart", "hash": "7924bc2d95999b2767d9f34e6ac52f98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_4.dart", "hash": "54d72b1c5b9977ccdcb6cd95e8acc7e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/restoration.dart", "hash": "79d4fba74eb854577c9589fb33994287"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/failure.dart", "hash": "2db6cf613a3f03e05a2c19ca6e14447b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/lib/src/types/types.dart", "hash": "b0a1d3768576f849edb7c8d6c3b9d921"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/visibility.dart", "hash": "94dab76e00a7b1155b15796b87ebe506"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_import.dart", "hash": "e0a5a25c69f7362ae3d6e493dfc611ee"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/unicode.dart", "hash": "8b525140e1bf7268e1681a62c7640eea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/buffer.dart", "hash": "15563ca80dc06490676ca80b6b98718f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/dialog.dart", "hash": "fdf500742b45dff0abb3db9cbd350fd4"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/.dart_tool/flutter_build/d4d4db9379df09af04f05ad89c774e35/dart_build_result.json", "hash": "1f8e8e6dbc6166b50ef81df96e58f812"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/models/capture_request.dart", "hash": "a067005b30134b50b3de5908bfc35a9d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection.dart", "hash": "05d4aeae6031730c6aa412a128f67448"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/declaration.dart", "hash": "0ddfa36e71f58e8be68202ab6901bfdf"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/text_selection.dart", "hash": "9c13d1f810b039faf38c54f062c83747"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/tab_controller.dart", "hash": "40587a28640d3c90ad2e52fdfbcd7520"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/transform/flip.dart", "hash": "7e6d4ac45cbb921dcc6ad20d920d07f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/charcodes.dart", "hash": "a1e4de51bdb32e327bf559008433ab46"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/path_provider_foundation.dart", "hash": "9485ecc20aafb0727c2700cf6e34cb65"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/_platform_selectable_region_context_menu_io.dart", "hash": "77e3a9ed54e0497465a4346f273bcccf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/utilities.dart", "hash": "c18ab890f45960c7227edee678cbdf70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/scan.dart", "hash": "352139677d0d5e7dbf8941093403250b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/transform/copy_rectify.dart", "hash": "d2f170ce8e5bf78d3ec979f7534f16f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/file.dart", "hash": "51ffa7b452686eecd94ed080a1da4275"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/psd/effect/psd_inner_glow_effect.dart", "hash": "4ad16c49081a85e09b4beb00afedb4eb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/transform/copy_resize.dart", "hash": "af56128d719dbb3374a573e2f525dcae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_activity.dart", "hash": "bce1bb799fa4cc899b6525721e14c9aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/separable_convolution_cmd.dart", "hash": "7c8f90898254225b33ff088de38d9ffe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/each_event.dart", "hash": "91b72e3a75068042bd3b16de99d2c990"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/platform.dart", "hash": "d2bab4c7d26ccfe4608fe8b47dd3b75c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/animated_cross_fade.dart", "hash": "98772211ffa69a8340f8088cd7193398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/color/color_uint16.dart", "hash": "acc88deccd3512fc9d02143fd74af668"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/debug.dart", "hash": "6f516ffde1d36f8f5e8806e7811b15ba"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/add_event.g.dart", "hash": "a79a6f9bb06c7d6dc5fb74ac53dce31b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/semantics/binding.dart", "hash": "d5bcdae8bba4c191294311428a954783"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/Formatter.dart", "hash": "35054401ba5ecdc8134dfd5dc1e09f10"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/list_section.dart", "hash": "1363e5e6d5efab4bae027262eff73765"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/draw/draw_pixel_cmd.dart", "hash": "5b9105badc82d5a549192a8ffea41dbf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/photofilters/utils/image_filter_utils.dart", "hash": "48e6fff93088ca9da3adcb34027f0e9e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_android.dart", "hash": "23149dd1dabb201f41ccacb25e322741"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/any_of.dart", "hash": "8cd59827d2f99e2d6c62f2f38c275cf5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/name_matcher.dart", "hash": "5c4dc37f36fc78823f785b92b944560d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/from_callable.dart", "hash": "b05a68b737792aa52eaaa4d3e093bb63"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/pretty_writer.dart", "hash": "4c618cb90a20b93f23c554b8745d5f77"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/force_press.dart", "hash": "d3de616e525e730c7b7e3beb57930993"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/gesture_settings.dart", "hash": "b5bd9d15c10929b4a63ea0df649e2d52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider.dart", "hash": "7504c44d1fa6150901dd65ec78877be0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/carousel_slider-5.1.1/lib/utils.dart", "hash": "e97240d7ba94097ee544cbe2e5169b93"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/http_date.dart", "hash": "fb76e9ed5173ac1ae6a6f43288581808"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/jpeg/jpeg_frame.dart", "hash": "6dc4013f75d0c76202dc573a4d079a6f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hash.dart", "hash": "4af79c5c69ccf0cae6ab710dfb84b125"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/LICENSE", "hash": "753206f0b81e6116b384683823069537"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/span_scanner.dart", "hash": "87bcefcfff19652ad296ec7005799840"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/animated_size.dart", "hash": "6b396237a38f3417babe500724de8a84"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/shape_decoration.dart", "hash": "6486bc074c81ec57bdafc82e6a64683a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/animated_switcher.dart", "hash": "008b3ea4691331636bbea9e057357ceb"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/NOTICES.Z", "hash": "e93df1076c25613e4ded17d9111b6367"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/pixel_uint4.dart", "hash": "84bb9dfd8609b996358dc04e4ffea393"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/data_table_source.dart", "hash": "094b2c03ad4e0ef5bc1144e281142b2e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/states/analysis_camera_state.dart", "hash": "02b8b4a1de85a0f832948a38a3eb492f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/util/rational.dart", "hash": "5642b2e21eb7fd50822ccdc09950268a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/window.dart", "hash": "b081e406a9e3448ff172ab7d21f31f7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/platform/platform_io.dart", "hash": "bb7e4bee2f9cca7b7e771e5ff413108d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/bleach_bypass.dart", "hash": "a94145fe9b2a79d709ed12db79ec46a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/LICENSE", "hash": "f26476a70de962928321bf9e80f9029e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/parser.dart", "hash": "13255a7d5a3edaa79e467810f1290f48"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/path.dart", "hash": "157d1983388ff7abc75e862b5231aa28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/util/_cast.dart", "hash": "c5d37a6208e5c133ef7bf15eccb9cd49"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/constants.dart", "hash": "c7cc72c1e40d30770550bfc16b13ef40"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/palette.dart", "hash": "7baaec433902b8755fb0e1414c281bb3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/tooltip_visibility.dart", "hash": "ee2f417f35b5caa4a784b24c1bc32026"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/source_span.dart", "hash": "9f2eb24284aeaa1bacc5629ddb55b287"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/temperature/temperature_cache.dart", "hash": "a6350a577e531a76d89b24942fca3073"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/lib/src/internal/map_interface.dart", "hash": "6fa61b9109a961ffc6087e4a36ad2fc3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/start_with_error.dart", "hash": "47cb151906114ae0161078bb7968ffc9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/draw/fill_rect.dart", "hash": "54093a647fce782ff834815ff0acff7a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/keyboard_key.g.dart", "hash": "4f9995e04ebf5827d1352afca6adda26"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/take_until.dart", "hash": "85fcef4d360ca759563bbfbe7c8d5e8d"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/lib/models/photo_model.dart", "hash": "8dc4d6f8521b0d8f217e827122dcdb79"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/layout/awesome_camera_layout.dart", "hash": "f066d8d0ee19146933d0cc811808d392"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/draw/draw_line_cmd.dart", "hash": "f7a2fb612858fefde08adee1e499c2e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/replay_stream.dart", "hash": "c86f575dce7f62595d9f1e169492f750"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/default_text_editing_shortcuts.dart", "hash": "9a31689295b300aa8ab12d29fb8853ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/pvr/pvr_bit_utility.dart", "hash": "45d4b021624f6fe3d92ce697cd441b44"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/layout/awesome_bottom_actions.dart", "hash": "b6aea8274fabdb27ef80951b51008636"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_gzip_encoder_io.dart", "hash": "8c7bf64921c74572e55b1c01b3df25f3"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/packages/camerawesome/assets/icons/minimized.png", "hash": "1589a3aefe13c85c8bf2296863881c3d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/executor.dart", "hash": "c71fd8a0b51ce5a020da72113da4c7c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/exr/exr_b44_compressor.dart", "hash": "4cec4b61a99ea09d63f12f0ad7d99330"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/bump_to_normal.dart", "hash": "4e3491aebe962269f3f1b88d4649e015"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/document.dart", "hash": "0f90625420cd7d017be4426f0bdaf0e1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/input_chip.dart", "hash": "14177be7a74b321668af2b9effa0f396"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_9.dart", "hash": "e7280a239aa04ba649fc950d8170cb3f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/webp/vp8_types.dart", "hash": "950486f15ab40e7b5fefd92a39393c93"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/_bitfield_io.dart", "hash": "0ae47d8943764c9c7d362c57d6227526"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/utils.dart", "hash": "8986177ba204a808c603c35260601cce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/publish_subject.dart", "hash": "b2fa768bd42261fab936524dd6f1c8ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/windows.dart", "hash": "0d86d4ba2e01e5e62f80fcf3e872f561"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/obb3.dart", "hash": "5ca0b5786bf63efd4fc72fcecfe1b36c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/value_stream.dart", "hash": "4ec7181b3b281703a8fddee43b540ee6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/path_provider_linux.dart", "hash": "b48ba72a2d5d084d297c3d78e351036e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/color/channel_order.dart", "hash": "b6372126b284904c0d53e2a7ba0b6689"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/states/camera_state.dart", "hash": "1a41251ff104de7f06cb62cd67880de1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/src/hct_solver.dart", "hash": "b972c32590c642256132827def0b9923"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/_network_image_io.dart", "hash": "be7392100d4028793c499a48ed55cf29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/buttons/awesome_capture_button.dart", "hash": "2ee901aef05e5d7e3b034e92696e7d62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_rainbow.dart", "hash": "0bc80db5885f9d8ecc0f80ddab6fe8b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/sobel_cmd.dart", "hash": "e1cae922f9bfa4482fa6a59386941119"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/context_menu_button_item.dart", "hash": "5061e0737e2db44e82d8a8c12f328a48"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/borders.dart", "hash": "5de15d7a41897996ef485c087ef4245b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/expansion_tile.dart", "hash": "d9511b6618e15c2df1d5d0ad39256ed1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/app_lifecycle_listener.dart", "hash": "f77f6a903d346f842a7fe474e427d6a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/util/octree_quantizer.dart", "hash": "f6f1419c3360a4b6f3d8c446f1639283"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/pvr/pvr_info.dart", "hash": "3078c32cdc8798e590f7bdb632ab0f55"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/status_transitions.dart", "hash": "59b6b74779849bf5b836b84bb362b99b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/buttons/awesome_camera_switch_button.dart", "hash": "16bd78f0255ff7f60047fb38281bab61"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/navigation_drawer_theme.dart", "hash": "f6d18a38c0986111a3d297424ed6fbcb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/pvr/pvr_packet.dart", "hash": "2bc9f8b510a513997df30506cfbe06d4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/map_not_null.dart", "hash": "929b5628541e8ab826e753c9fe90cd30"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/page_scaffold.dart", "hash": "805f831d339e4ab9e6b172b2bf845809"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/bmp/bmp_info.dart", "hash": "e691944782d5c0a0e3a6149936bdd7ba"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/radio.dart", "hash": "9802442b82d3be84abecae8d0a2c7bd6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/table.dart", "hash": "1f437276972808bf4cf722440da1b231"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zip/zip_directory.dart", "hash": "980c869bd642f4640bf26cf795553ea7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/filters/awesome_filter_selector.dart", "hash": "51facea9d91a0f9e50a655734c3cdde3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/image_format.dart", "hash": "12c7a7d16cecbc89145fb4f36af88844"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/archive/archive_file.dart", "hash": "01fee2f811b7ea870c1d9f2ceb72061b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/lib/permission_handler.dart", "hash": "ae9b498a0c3fd784a628e57eb92307aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/LICENSE", "hash": "bb500500256905950683ee38c95fb238"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/adapter.dart", "hash": "e05529d31a09e4c86cde70483824fa10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/lib/photo_manager.dart", "hash": "be95b03f230631f93e7d77b5f1a54f34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/awesome_media_preview.dart", "hash": "44c6a7687d9316a7dcbcbf331cebbdec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/int_formatter.dart", "hash": "e6646f76f04f9456f5984aea312a50e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/carousel_slider-5.1.1/lib/carousel_slider.dart", "hash": "c4d5bc80ee39c546e8a5ea3b1b2dfdde"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/camera_awesome_builder.dart", "hash": "8189476db45c01147537b4d15fb37a0c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/enums.dart", "hash": "b49758f50c20a4f98a48e3af42de35d7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/toggle_buttons.dart", "hash": "64a2ea17e8058aec30096102af030f98"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/buttons/buttons.dart", "hash": "1c7888f73437b86e24d5312abe8587ad"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/menu_theme.dart", "hash": "89ae530b1eb1ce798ec54bc9b45efdba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/transform/copy_rotate.dart", "hash": "8aee85615a903910370fe0253bfc417c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/text_formatter.dart", "hash": "b139a58dace0b9d9a07a3523ed72ced5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/collection_extensions.dart", "hash": "874c21db82e74ec1d570b48ffb1bad17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/lazy.dart", "hash": "128e022b683572b60bce0c93cd05007c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/LICENSE", "hash": "6e15c47981e2e43ee17849a222e33b76"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system_entity.dart", "hash": "04e7480fb89755fcc5f64f7d80ca610f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/local_platform.dart", "hash": "9cc2170ec43e47681be6cb2a313ba1b5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/implicit_animations.dart", "hash": "c9105f08cb965dfc79cdbe39f062d6c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/clock.dart", "hash": "84ad21db5ba97deb809b65697546e39c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/lib/src/filter/classical/filter_options.dart", "hash": "e7cf2421adbe0ae9ff8444d1dee628f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/start_with.dart", "hash": "46c6500112cb203a46608825824d4d52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/sprintf_impl.dart", "hash": "2e7ac5275644c470359f8b69c555bfd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/preview/picture_in_picutre_config.dart", "hash": "a7524a8bff932eddf581e2d3fc18b606"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/progress_indicator_theme.dart", "hash": "377731ed35ad8d1d36dcfd532a3d308e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/shared_app_data.dart", "hash": "feacc941aea1ec8b3a30601915b7d353"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/charcode.dart", "hash": "b80f25d51570eededff370f0c2b94c38"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/material_localizations.dart", "hash": "063f2360bd47faba2c178ce7da715d92"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/sliver_multi_box_adaptor.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/viewport.dart", "hash": "c211cb790c5fc59f5bb6dcd61e0abcab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/aes.dart", "hash": "a8949b668376a6f80053e689503f40c7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/guid.dart", "hash": "55bb53dd4f9ed89c9ff88c204b59293c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/raw_keyboard_windows.dart", "hash": "266a40131c9f05494e82934fd7096ed0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/clip.dart", "hash": "dc2cfe4408f094916cd5eb1d294d1f2f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/edge_glow.dart", "hash": "d11db5d1c05cc4870f0c2aa4151a5212"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/draw/fill_cmd.dart", "hash": "40ad94e4ddd663b18f1b09799e853135"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/psd/effect/psd_solid_fill_effect.dart", "hash": "acc73ca9c6c2b95f4c68a78e291199de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/util/_internal.dart", "hash": "de18d8051f3ef8481c70808d2d4675c7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/localizations.dart", "hash": "9c051d9a4098051ba8258eae9aae3195"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/decoration.dart", "hash": "ae85856265742b6237ed0cb67c4364af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/text_scaler.dart", "hash": "b6e992b1127f8376358e27027ea7a2ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/output_memory_stream.dart", "hash": "6b648a35d809f4d973f4100017b6f9ad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/sqflite_database_factory.dart", "hash": "b2b96fda3b5d147408ecb71c2bbe73a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/normalizer.dart", "hash": "5684bfb4916cd4ec19596b3fd6a7a93b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/page_transitions_theme.dart", "hash": "91f73f40856927e688e1707a923db3e2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_position_with_single_context.dart", "hash": "56a764067b45a1a7cb6b7f186f54e43a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/pattern.dart", "hash": "cd6d0f606129e54552c5fee950e32bfd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/utils.dart", "hash": "1eb2fe31f2f21cce619f672c25b1e43f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/database_file_system_io.dart", "hash": "35c142ea243059f941a4a896a8e053ae"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/image_stream.dart", "hash": "8f1d7bd8be5bc9a71d3131f835abdb80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/cross_file.dart", "hash": "b5c8f4dba868efb80ed69fcd5a7d3f07"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/isolate_snapshot_data", "hash": "a4578c9e939f9a7aec6e8897e055b6ec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/models/capture_modes.dart", "hash": "b710c8fc98c66e42c027d01eca5ff2f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image_transformers.dart", "hash": "10404d098f485bca549850751b3e93b9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/animated_scroll_view.dart", "hash": "62f6d0411965eefd191db935e6594f90"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/box_border.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/color/color_int16.dart", "hash": "77fe1f42001c0cd42a7d5b789c255492"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/predicate/predicate.dart", "hash": "ec001ba2712f91cadae858bfdfe622e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/factory.dart", "hash": "a79e2b9a182eb762fadaab05e9269edc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/utils.dart", "hash": "fe2489ea57393e2508d17e99b05f9c99"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zip_decoder.dart", "hash": "cde313114aac495f73ca0a12f4e90f91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/noise_cmd.dart", "hash": "5be3d224220b8754eed2dc1996e5015f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/util/output_buffer.dart", "hash": "46fab46542e6783a71c8f74cb4786047"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/editable_text.dart", "hash": "20b03effe92fdb82cb2b1efcf637be3e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/on_error_resume.dart", "hash": "f79083ce7919dc45b4d2c313bd37af7f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/jpeg/jpeg_marker.dart", "hash": "c0be37fae8ba291d288c8c3584177374"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/raw_keyboard_ios.dart", "hash": "1303bc77ad63625069f2d23afc73f523"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb2.dart", "hash": "f8fb1733ad7ae37b3d994f6f94750146"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/stopwatch.dart", "hash": "f38a99a51f4062e7861bb366f85265d5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scrollbar.dart", "hash": "8e7a6f654b6ef374af586747a3ea912b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/material.dart", "hash": "8ef67f192314481983c34c92a81ee5f2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/image_data_float32.dart", "hash": "7d57f633e2f50c6a160a79b6a8f04bf7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/http_parser.dart", "hash": "b76ebf453c4f7a78139f5c52af57fda3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/pixel_float64.dart", "hash": "c083ffceeec408027c2dfee10055e134"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/provider.dart", "hash": "7c0851720900806fa2a397a81c81875e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/tiff/tiff_bit_reader.dart", "hash": "0a94e35362cc22090606b9b182e62ff2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/validation.dart", "hash": "af69b927cad3da3ff26f5e278d151304"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/draw/draw_polygon_cmd.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_children.dart", "hash": "7c666bff17f2cfae821f93f0c5e66a64"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/exception.dart", "hash": "773da8c184ab316ec6998980a1448a1c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/text_painter.dart", "hash": "93576d7d8731bea65013886f9194df15"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/ico_encoder.dart", "hash": "494c0beebbf4bfc40637c743407f42e1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/constants.dart", "hash": "92e6028556e74c1dc297e332b473f78e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/debounce.dart", "hash": "1aea282ab07e82afe7a564125110e1fc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/annotations.dart", "hash": "b092b123c7d8046443429a9cd72baa9a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/shortcuts.dart", "hash": "721fe68e34a4747334faa11e91f93523"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/palette_int8.dart", "hash": "c103f890440652332d17b04293e349d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/drop_shadow_cmd.dart", "hash": "8f69ea75ed30912545af54d612d6fa96"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/delay_when.dart", "hash": "064ceadd31d0b29fc59188b2c4d45db1"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.bin", "hash": "455df56206ebbfee19040619d5e0d618"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/text_theme.dart", "hash": "ee36aadc3fac54d5659c94c6aadcd007"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/cupertino.dart", "hash": "9b83fabf1193bf4967b740dd7a2c8852"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/draw/fill_rect_cmd.dart", "hash": "9b0e5e0102a4f91e3f4e9b2dc0230b8e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/dialog_theme.dart", "hash": "8383986e94be1a258a59af29b9217876"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/sliding_segmented_control.dart", "hash": "2e074f4fb954a719546377c67cb54608"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/sliver_persistent_header.dart", "hash": "ffa4f7b2d5b1caccc05cf4b64021ae5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix3.dart", "hash": "447b270ddd29fa75f44c389fee5cadd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/psd/layer_data/psd_layer_section_divider.dart", "hash": "ec57013427b7353c242328df2c438928"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/open_options.dart", "hash": "296e60aee7732b001a79f3216058a381"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_queue.dart", "hash": "d6f045db9bd5b72180157d44fee9fbfc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/and.dart", "hash": "1e9ed9cdf00b9449d9b72dcd00add4d3"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/packages/camerawesome/assets/icons/4_3.png", "hash": "0091aca9a18eb33b968ec3abf62699a3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/constants.dart", "hash": "83df4f6e4084a06a4f98c27a524cc505"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/async_provider.dart", "hash": "3a2d20718f772fbb710aec7dc5e0bf80"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/font_loader.dart", "hash": "a29f0df228136549b7364fcae4093031"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system_entity.dart", "hash": "22f170a8dc9abfac2942555e83589e1a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/sliver_layout_builder.dart", "hash": "0c520a6b1ab38e0f294c3ddbc2ec9737"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/focus_manager.dart", "hash": "84589f907e3e4d8fc72e5c786a0530f2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_physics.dart", "hash": "f26f519ea124441ec71b37df7cfa1ee9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/dual_transition_builder.dart", "hash": "c06267b6c315a5e40f28feb6019de223"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/trimming.dart", "hash": "1db476563b55e241003667ca3669c0b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1/LICENSE", "hash": "6e15c47981e2e43ee17849a222e33b76"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/utils.dart", "hash": "caf148b76c44a3f0f1bd6055ddbb8f5e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/checkbox.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/sliver_list.dart", "hash": "03001d3ddae80bbf1f35c5e70e0d93e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/matcher.dart", "hash": "faa18ee55924a5c65995875c94338d98"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/lib/screens/camera_screen.dart", "hash": "8bad0ea47a2b5c39d8f9d00c405281a9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar.dart", "hash": "12120b49ba363d4c964cf1d043a0aa1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/env_utils.dart", "hash": "d75f62f03297d8fada84de77f3e92373"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/file/builder/capture_request_builder_io.dart", "hash": "f9f66f5da5e8d2c620fd9fef8cd10f4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/draw/draw_rect_cmd.dart", "hash": "5ba8061a49bc11219df86be87e9aeca9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/animation/curves.dart", "hash": "74a89d22aa9211b486963d7cae895aab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/config/config.dart", "hash": "6e1f276f9f7416f792db31fd51b3e3ea"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/tab_view.dart", "hash": "8b15d222f5742b46bf55a4ef4cbfd6e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/layout/awesome_top_actions.dart", "hash": "f6a26f19cd60dba202ef8023b86e9bbc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/uppercase.dart", "hash": "7061b91c27425c907020fe54e569b9db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/util/neural_quantizer.dart", "hash": "53c69f05c6b37b142d497ddf4332a5db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_visitor.dart", "hash": "61e938fe770ed7331e39f1dda1b64dd4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/float_formatter.dart", "hash": "9193766efadfc3e7be3c7794210972ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/lib/src/internal/plugin.dart", "hash": "79100746eb37d738bd31c162382f47f3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/gzip_decoder.dart", "hash": "bdcea16bdc5a229dc7f7cb80d9b38797"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/elevated_button_theme.dart", "hash": "484329e20b76c279413a7d6dc78b3223"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/image_data_uint4.dart", "hash": "02de7f7c86fb89b519423e4f6f9fde33"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/webp/webp_frame.dart", "hash": "9e4bc84c5baabb81bc512905a7521c0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image/fade_widget.dart", "hash": "e1d33f6f03e359759c131d64cf62c84f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/matrix_utils.dart", "hash": "59475498db21e2333db54d6478af7c94"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/licenses.dart", "hash": "c0cf85f80b79542d2b0e1a00547d7310"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/formats/gif_cmd.dart", "hash": "963a991ff829770f22636fbda4a7e08b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/colorfilter_generator-0.0.8/LICENSE", "hash": "c75a79d2dc611c4ff241b8db115bc0f3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_impl.dart", "hash": "8e1d2c37f506b65c7d8b3274456d8dfb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/exr/exr_rle_compressor.dart", "hash": "a7a7b51d19ba8c010e1b19fa495a4dbb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/arena.dart", "hash": "04f3f5a6ad35c823aef3b3033dc66c3c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/tooltip_theme.dart", "hash": "8fac1e5cad9ef06d9e55e6559c06b990"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/preview/awesome_camera_preview.dart", "hash": "b86de5d08a033a274af9ff43b0e808cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/forwarding_sink.dart", "hash": "89e6e01bd627dc1cc46b16ae027742dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zip/zip_file.dart", "hash": "e380149475f6c11ddc46dd1844ac0123"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/opengl.dart", "hash": "9e22ead5e19c7b5da6de0678c8c13dca"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/text_selection_toolbar_text_button.dart", "hash": "91bf94aea1db708a8378fa41de066d33"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/animation/tween_sequence.dart", "hash": "eabd3dc33b1a3a2966fa68f6efeb6bce"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/segmented_button.dart", "hash": "ad631d7cd122efc4862c1c084fbde716"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar.dart", "hash": "e8aae4779eccfdedd9c4b8cbce4ab952"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_controller.dart", "hash": "ec48414c6983150c30241ba7128634fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/subjects/subject.dart", "hash": "4b93fc559e6626b4d42e924b10c58678"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/score/score.dart", "hash": "58b9bc8a40fd3e2f7d9d380d0c2d420f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/monochrome.dart", "hash": "ba6e77fce2cc3b5e4e716de371ab8d66"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/contrast_cmd.dart", "hash": "f70540672167af77e37c4dcf0e490a3e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/transform/copy_resize_crop_square.dart", "hash": "17dfe5329f59e0653934aa38377b5c08"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix4.dart", "hash": "b5f0b0da99e8a07d58c21ae071800404"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/stadium_border.dart", "hash": "85814d14dae3bc1d159edd0a4bef48e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/src/typed_buffer.dart", "hash": "f64837679a1abb526e942b166db5c244"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/colorfilter_generator-0.0.8/lib/presets.dart", "hash": "265cebb86a097abac79f0607c2b3a0e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/normalizer.dart", "hash": "b6fde0bb78218226247a2173dbf96ea5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/file_access.dart", "hash": "ca4057bd5bba32dea4d553dfb7eb2f8b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/text_field.dart", "hash": "b0f444b219eafe3ec2bb9e8a09e545f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_gzip_decoder.dart", "hash": "f00a5b112709bef9fe0703b0fc91e2d1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/chip_theme.dart", "hash": "525e57b6ade38da2132c8ddb0ea78547"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/progress_indicator.dart", "hash": "4f3e0e3af33c5bdfbf1d32adeba91652"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/monodrag.dart", "hash": "8807672a31b470f53c5fcc2b36dcf509"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/color_halftone.dart", "hash": "9e77ebc6c61386a8c701dcdadec19aa7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/animation.dart", "hash": "29a29ed9169067da757990e05a1476ee"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/basic_types.dart", "hash": "785eedcc96fa6a4fcc7c81a8736a7427"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system.dart", "hash": "3120b9b427a566f796573ee37167c026"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/tiff/tiff_fax_decoder.dart", "hash": "370fe8c88c166958f40f48485817626c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/colors.dart", "hash": "c517fb54b3d66b22988ad7c8d07c6f53"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/animated_size.dart", "hash": "91d8303ca1ccc72eccc1ae636c7825ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/photofilters/rgba_model.dart", "hash": "9a424474f6a37c476450345b669d3094"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/archive/compression_type.dart", "hash": "4338dd80d43355def2a83eb7a8cfdd70"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/action_icons_theme.dart", "hash": "50dfb9886f462e2b3405f0f8d23f179b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/normalize.dart", "hash": "80484e8c4df9868c2a4d1d14295d9085"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/switch.dart", "hash": "329bc189be2701d02fb1b7975ecf329e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_aware_image_provider.dart", "hash": "d390b15ecef4289db88a4545e359bc8a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding/decoder.dart", "hash": "e6069a6342a49cdb410fbccfbe4e8557"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/models/analysis/analysis_image.dart", "hash": "19a4862841868396170389247c028f3a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/path_provider_windows_real.dart", "hash": "43f4676f21ce5a48daf4878201eb46bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/md5.dart", "hash": "0981c95a357b5cebc932250a5e6c988e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/display_feature_sub_screen.dart", "hash": "a6d730f196620dffe89ac987b96ef6c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/transform/flip_cmd.dart", "hash": "1cfacf2596a1e73d1405867d01b243db"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/pubspec.yaml", "hash": "4b4236531367b0a7a36b15e6c6e20dc7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/sqflite.dart", "hash": "f7be2d6ca06ea6e4deeee0e441b71d6a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/lib/src/managers/caching_manager.dart", "hash": "13f245e97396ec6377dff02babc9a096"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/material_button.dart", "hash": "c165bb259eb18a2dc493a0e7a1d1ebd9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/palette_int16.dart", "hash": "ed2c99f0d169034708d8466f25e445fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/photofilters/filters/image_filters.dart", "hash": "733edde68f7862a4b4ad1ec0f98f121f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/core/context.dart", "hash": "a07f8e10a45176e8210b1bbac38f3e1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/epsilon.dart", "hash": "b9283cabc57ae94b3c75f147903751fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/smooth.dart", "hash": "13dcb837fb8fe50a51444c541c2f8e9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/color/color_int32.dart", "hash": "4e875f645101557f3a1954af613fc3a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_zlib_decoder_base.dart", "hash": "2a811cc0f24a2d57992cb0da1ecd1ec3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/output_stream.dart", "hash": "7a177c5b215118520a084dddcaa026e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/util/input_buffer.dart", "hash": "70d36fc0e847a2e44012a1fc5919cd5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib/path_provider.dart", "hash": "e08429988b4639fb29cd66bfdc497d90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/lib/src/filter/custom/custom_filter.dart", "hash": "670568b14109d7e559ec60d5e2ddc39c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/grid_tile_bar.dart", "hash": "a340eddbf129cfd60e2c67db33c6003e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_zlib_encoder_base.dart", "hash": "43e54e569a35d13a3e93f4b586998401"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/src/cached_image_widget.dart", "hash": "4310ddfcafc039210f0221a343c43164"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/ellipsis_search.g.dart", "hash": "7018ea64a9aab18f27a10711285d7573"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/digest_sink.dart", "hash": "038a6fc8c86b9aab7ef668688a077234"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/hct.dart", "hash": "596fb2e55b1ff1662e4bd67461fdc89d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/platform.dart", "hash": "dd109d67b92b9fbe6e0051f0c890c903"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/image_resolution.dart", "hash": "0f2a1a61119c0bef3eaf52c47a2ebcf4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/crc32.dart", "hash": "da0c331fea0ba64b7b8ec585ca0414c5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/paint_utilities.dart", "hash": "853b1406f2756bef671f6d57135606f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/convolution_cmd.dart", "hash": "bf7bd8174eeecd8684c24ec364fb43ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/string_scanner.dart", "hash": "f158ffadca730ab601c60307ba31a5e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/characters_impl.dart", "hash": "6297da5be01fb7c0d5c4aaffe7a27a50"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/semantics/semantics_service.dart", "hash": "fbfdd6181c7ea8d5950c24b467debf31"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/image.dart", "hash": "4eede9144b4c0e4b14bd426654183174"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/scribe.dart", "hash": "d195153a8c01a0392b38e3b9adc672d8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/star_border.dart", "hash": "e324dd19cc02a1bf47bf7cc545dcca79"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/race.dart", "hash": "2164e0e3bc00d90bd03708ddfd475ad9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/draw/draw_circle_cmd.dart", "hash": "2c2a47661f014c6499741d21e5886ef6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector.dart", "hash": "6a67d38bafe568f1b4047286d586fbbc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/line_scanner.dart", "hash": "168bedc5b96bb6fea46c5b5aa43addd1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/asset_bundle.dart", "hash": "ef24f0630061f35a282b177d372c00d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/LICENSE", "hash": "6e15c47981e2e43ee17849a222e33b76"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/chromatic_aberration_cmd.dart", "hash": "4c6c2d2c82eb183ef9614a1da0228190"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/distinct_unique.dart", "hash": "32a430474c588e6a5dfb093a222e9f48"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/tweens.dart", "hash": "29befe23f841cf5dd2dc7df24c13d88d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/style.dart", "hash": "825ec1b2847bd00ad5cd840c7ddc4d6f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/_platform_io.dart", "hash": "bf6d84f8802d83e64fe83477c83752b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/image_cache_manager.dart", "hash": "69c7e246c8fb227cdabc8a3d9a8316dc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/jpeg/jpeg_adobe.dart", "hash": "f98905fdcc2b59417e62f5b22262b435"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v1.dart", "hash": "a22d810ba989505f23b6be0562a04911"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/view.dart", "hash": "15957b9d3eac4a2e1acaa24a3032afe7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/rxdart.dart", "hash": "6d2dba952020d690bfc0aaff3adbcd65"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/strut_style.dart", "hash": "ee62fb3be5d885d65054fac4b84cac6c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/constant.dart", "hash": "176c6b2c4f4e2d64cd55df2a0dabe5e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/hexagon_pixelate_cmd.dart", "hash": "a92043dffe7bd712903c461901585e49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/invert.dart", "hash": "27398bd18d96396b0f5bde0c86f34f68"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/multitap.dart", "hash": "578ff911d6e70b239fd629f5a0206fd8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/widget_preview.dart", "hash": "3208b2267d4d1b0d118b8fcdd774b753"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/pvr/pvr_color.dart", "hash": "393a8dc0547cc26221e7c98c1a4709b7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/camera_characteristics/camera_characteristics.dart", "hash": "6ba7af75caf7903302584a5a989aa67e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/colors.dart", "hash": "65c7fba34475056b1ca7d0ab2c855971"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/rounded_rectangle_border.dart", "hash": "0bc495ddf9b02a06a5fc6934847e8708"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_parent.dart", "hash": "7f47dda6ed10e33236d465680dc8c12b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/LICENSE", "hash": "753206f0b81e6116b384683823069537"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/psd_decoder.dart", "hash": "6d4074423c99283e7ae3d62fa5b6938d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_attributes.dart", "hash": "f1acbe1ea51585adf0a1ba560796bab1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/safe_area.dart", "hash": "7088cc45b21c93be6b42dc748fc3a29a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/map.dart", "hash": "822f0a79dfd6a3c997d2b898ec420b97"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/tween_animation_builder.dart", "hash": "107c33a245427bf0f05e21c250653dc6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/models/save_config.dart", "hash": "ad01f5ed66140a12e2538e47627f9b5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/defer.dart", "hash": "d06420fd88fb8f7cc3acc1643051178a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/lib/src/managers/photo_manager.dart", "hash": "41ce8e7263b148d65884f449b404f412"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/database_file_system.dart", "hash": "dac02dc6cb13c753a5f3ae19976b1540"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/undo_manager.dart", "hash": "0821fcdff89c96a505e2d37cf1b52686"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/matches.dart", "hash": "3459a8a962662899e6d1ed009af8ba45"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/binding.dart", "hash": "9c9f1e70fac06b3e87bb33ece047c4cf"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/event_add.g.dart", "hash": "7bd8137185bc07516a1869d2065efe0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/empty_unmodifiable_set.dart", "hash": "0949b8197a6069783a78f4bb0a373fb0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/color/color_uint32.dart", "hash": "c25b1dbed1c22dc01e6064648104c633"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/where_not_null.dart", "hash": "9b84f667016de96aa99b12338a4dfb57"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file_system.dart", "hash": "9007580fb76ae011692307f00e0a28f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/term_glyph.dart", "hash": "1adcc56e3affffb23739c7c9d8a5fca0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/service_extensions.dart", "hash": "920b63c794849c8a7a0f03f23314bbb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_gzip_decoder_io.dart", "hash": "b611f18199196cc12d618c765d2906bd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/lsq_solver.dart", "hash": "d0ab7f5e11e48788c09b0d28a0376d80"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/filters/awesome_filter_widget.dart", "hash": "a9a591038d6eaf3e609de7e09c2a24a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/plugin_platform_interface.dart", "hash": "8e49d86f5f9c801960f1d579ca210eab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/lib/cached_network_image_platform_interface.dart", "hash": "d9ccb5a0c8dcf64361a257c101d0e719"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/page_view.dart", "hash": "7150d31ecb453ea0d7516ebd2a56ff84"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/eager.dart", "hash": "07664903d8026f2514b29b786a27f318"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/start_element.dart", "hash": "e11fc9210b4438654c11893b98ac66fb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/color/color_float32.dart", "hash": "347cdf91f83c849aedd3076ea89d9921"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_inflate_buffer_io.dart", "hash": "fb84e07b5ecc0f027a487403597a6af7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/processing.dart", "hash": "0ca8410c364e97f0bd676f3c7c3c9e32"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/tar_encoder.dart", "hash": "e836c87b16c58c6e0c392617d62d0f13"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/view.dart", "hash": "e758d8d6b65597325bd35b5dc769c7a2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/draw/draw_line.dart", "hash": "e526625609c11440d57183705a774e32"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/int32.dart", "hash": "f6b2a03b8f3554a6b37f151f6a561fe9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/gif_decoder.dart", "hash": "36acfb0ea7bb85bdfaa5873684620556"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/utils/awesome_theme.dart", "hash": "c7c55ed8676596a51360f89e09a9b8e9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/byte_order.dart", "hash": "ead62f3d4f5132b7d2b9458601a48870"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/psd/psd_channel.dart", "hash": "9f11a3b408b7ee5da545f080d4c4e344"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/debug.dart", "hash": "dbb0bb20c79bcea9397c34e3620c56c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/matrix2d-1.0.4/lib/src/matrix2d.dart", "hash": "b7eeb6c9c0cc804aa66fcb1907f1b081"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/sql_builder.dart", "hash": "1c4127d99af22e5232df8132ae79beeb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/never.dart", "hash": "238c701652f1f5ad9ba928994a96e608"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/folders.dart", "hash": "4bd805daf5d0a52cb80a5ff67f37d1fd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/image_icon.dart", "hash": "2610f7ca2c31b37ad050671aafbccdd9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/sprintf.dart", "hash": "9c00cbf52bb0297fccad0b5c5b54d4e7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/sliver_padding.dart", "hash": "ddf1bde8f4b9706d5769690b7819e5d4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/stretch_distortion_cmd.dart", "hash": "d7099d2b955c17bc236ea865f75a4d5f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/util/image_exception.dart", "hash": "12af51700f3d0ae74af9f082f22a61cd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/multi_lock.dart", "hash": "2ac6fe0e9a4d7b15855dabd7468cc320"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/haptic_feedback.dart", "hash": "9ea1746a0f17f049b99a29f2f74e62ee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/LICENSE", "hash": "8f29b74ba6fa81721ca1cd98cd39ae4d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/src/grapheme_clusters/breaks.dart", "hash": "73189b511058625710f6e09c425c4278"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/context_menu.dart", "hash": "02f1d44813d6293a43e14af1986519ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/gzip_decoder_web.dart", "hash": "e244e8e84d2f26e11dc0b7f61c9bde37"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/photofilters/utils/convolution_kernels.dart", "hash": "c2704a99bcb48bfdc1afb15b7f74f8c3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/scrollbar.dart", "hash": "a2d1c7bec7b52901761f3d52a1ac02d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/image_data_uint2.dart", "hash": "6b8b689fa281c237c13116f3c3a80ae2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/file_service.dart", "hash": "857464ce6f576c4020591d501ebcaaa7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/archive/encryption_type.dart", "hash": "990c197185c1e608b8f489b1c0effa42"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/pop_scope.dart", "hash": "0ff55be19444856c892e701c475b20f6"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/packages/camerawesome/assets/icons/16_9.png", "hash": "8d1887c08fb8ab6abffa45a11c81b8dc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/util/clip_line.dart", "hash": "cc8eecbcb0a0d5a4341423695925eec5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/triangle.dart", "hash": "7d2bdb4801fc8b3a110f36d5e5fa59f5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/dropdown_menu.dart", "hash": "d110c5e3ee26058a3e9b4bba6440f15f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/remap_colors.dart", "hash": "5c6def89d80f917bd45d9866cf95f1cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/parser.dart", "hash": "505fb0828e4fe58e1a49ddab272d7b71"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/contrast.dart", "hash": "86620320d45249700382dab24d2ff796"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/refresh.dart", "hash": "7d5bd66d61c58afe63c6d33ee0e421c1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/sheet.dart", "hash": "e88cac3fc4dc6a17d2bd13549d433704"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/preview/awesome_preview_fit.dart", "hash": "c7c19b5b0a23f3a96891e92cd3171445"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/named_entities.dart", "hash": "c7e489fa5d00c1717fe499f3845c2abb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/utils.dart", "hash": "2d3b2846d7071fb93d36485c261040ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/transform/copy_rectify_cmd.dart", "hash": "6097817660fc7ed2cf1ee8d9e44719e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/context.dart", "hash": "daeb052f1089d4e84d8a22acf56c1da2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/lib/src/types/entity.dart", "hash": "f55721c24535a6c6371a148292eb7df4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/pvr_encoder.dart", "hash": "ebdbd94034a86652b26a61e0921c4e39"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/pvr_decoder.dart", "hash": "9dd452a7f541713193cbb423d0281963"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/drag_target.dart", "hash": "166147b7bee5919995e69f8ca3e69d17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/file/content/file_content_io.dart", "hash": "1d4b5a3cac077408c619fc421e42deee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/mixin/factory.dart", "hash": "63fa9307c55c93f4fde0e682e7da6503"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/box_decoration.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/debug_overflow_indicator.dart", "hash": "deedcf7ee9b4e76191202e61654f9dcb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/asset_manifest.dart", "hash": "a2587417bcfd04b614cac5d749f65180"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/pixel_uint16.dart", "hash": "b7fe471739b88b709bc596cec3fa0558"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/navigation_rail.dart", "hash": "2936420e0c8ddba21d283d969f5147d6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/material_color_utilities.dart", "hash": "11df661a909009a918e6eec82d13e3ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/string_scanner.dart", "hash": "184d3b79d275d28cd02745b455041ee6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/src/types/io.dart", "hash": "a45632c7d0440400b3f7a2ce615d21c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/find.dart", "hash": "9f4ec4b8e28d5bf94cbd385aa48eb91f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/cache_info_repository.dart", "hash": "d423d24bacc39262f492386b09a7ee7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/hct/viewing_conditions.dart", "hash": "cb0d5b80330326e301ab4d49952b2f34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/null_mapping.dart", "hash": "4bc463f9c4b5496d8918b58070c10515"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/bmp_encoder.dart", "hash": "554351777b55f23fb6d55eb0faaf9da8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/assets/icons/4_3.png", "hash": "0091aca9a18eb33b968ec3abf62699a3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/notification_listener.dart", "hash": "d3b949a1e7578291493af5fd28846314"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/linear_border.dart", "hash": "0fa4800227413041d2699ed47918c7f7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/time.dart", "hash": "872d879ea43b6b56c6feb519cc12d5a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/vector_math_64.dart", "hash": "bd1315cfa157d271f8a38242c2abd0d9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar_button.dart", "hash": "9a67635cfd2e047d996c4840d4cb18ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/utils/sequential.dart", "hash": "b5519514c9b9570c951c0da186030e29"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/async.dart", "hash": "3f9362642d37e0d97860181e8a1dd598"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/io_client.dart", "hash": "e4823f5eb1dffcf1cf47a9d667c5cb18"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/button.dart", "hash": "d7a239f8b80f844857527c2012e4fa1c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/tap_region.dart", "hash": "96b4be28e9cb48156c65de35d7ccefba"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/restoration.dart", "hash": "04c713cbc0ac5e15c7978a2e91b81488"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/reinhard_tonemap_cmd.dart", "hash": "edc653faf5777bbca80dc20dcedef70c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/pnm_decoder.dart", "hash": "7e9df0cd987d6de7f20e41c706269735"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/platform/platform.dart", "hash": "17488cbfc8b9ee2e6e5ba0229d7c21a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/transformers.dart", "hash": "5d2971806de340d9e970e21af445505b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/exr/exr_pxr24_compressor.dart", "hash": "404977823be4526f94c61df789f889ea"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/radio_theme.dart", "hash": "3f2a39352a1c6067566f8119aa021772"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/lib/main.dart", "hash": "557081a67e911ca5996ab143687c3732"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/matrix2d-1.0.4/LICENSE", "hash": "80ce67156db93f94851d231c5fc19dba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/newline.dart", "hash": "bdd138e5e3c721f9272da59c10d7c5fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/lib/src/internal/enums.dart", "hash": "3cc34134dd9129b6469b3cea6065cfff"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/material.dart", "hash": "76611c76bf37be8fc89798858b6c7685"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/vm_snapshot_data", "hash": "85aa53b038be894edc8ed4b952643c56"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/range_slider.dart", "hash": "2e0b7bb9c12ed9f989240a20a878badc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/physics/simulation.dart", "hash": "0fbec63144acf1cb9e5d3a3d462e244b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/sepia.dart", "hash": "ee5d0633d5c083504aa9340f994a4516"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/collection.dart", "hash": "4ba0a4163d73b3df00db62013fb0604e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/command.dart", "hash": "f1bae4d64ecbb4725cb7ee1923410f21"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/drop_shadow.dart", "hash": "54a6f1533b3667e0266ffe55df731001"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/widget_span.dart", "hash": "84e117adf104c68b0d8d94031212b328"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/retry.dart", "hash": "66c3d8022ecd26ac3b2f30fe28e4c475"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/grayscale_cmd.dart", "hash": "da419d79e399009ade81e78cb180544c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/common.dart", "hash": "33f949ceca0aa8895b2fa0ae289f42d0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/bottom_sheet_theme.dart", "hash": "be66f00d2c9bb816f4236dd0f92bff55"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/cache.dart", "hash": "e0cbefa359309715e5101bce98eb65e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/photofilters/filters/preset_filters.dart", "hash": "55302dc9993f3d3fa8a854e432b01475"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/gzip_flag.dart", "hash": "05b64f898b2f893af5ac79020dedb851"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/icon_data.dart", "hash": "eb9b3bf513b18ddaf0057f3877439d9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib/typed_data.dart", "hash": "b9abba31a48a9c2caee10ef52c5c1d0e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/team.dart", "hash": "f6c6b31745eec54a45d25ffe6e5d7816"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/preceding.dart", "hash": "9d5375413b37f738384990ebdd6c6285"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/webp/webp_alpha.dart", "hash": "cb76fcb559b187de097c11f86b8c817f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/color/channel.dart", "hash": "9f00aeda438fd22e0b50b10d0dab203f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/characters.dart", "hash": "43268fa3ac45f3c527c72fc3822b9cb2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/constant.dart", "hash": "84391293163d781c7715a32ce43b3c7f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/assets/icons/expanded.png", "hash": "b8bce8882199b9e134b7b2d102317d3a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/scheduler.dart", "hash": "95d8d1f6a859205f5203384e2d38173a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_5.dart", "hash": "a2f8f444e0f2d008fef980dd8df0bcac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/iterable.dart", "hash": "0ea87086ab38d0a0e292321e807293f8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/checkbox_theme.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/decorated_sliver.dart", "hash": "cd7f8dc942f5138db121aabbaba920ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/luminance_threshold.dart", "hash": "1047a0dae695908171ec13ee346c0617"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/pixel_uint32.dart", "hash": "605fe31857dbf59bd6ecf2b42653f9ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/jpeg/_component_data.dart", "hash": "baf4911922fe0f6c9233888aa94c1432"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/image_data_int16.dart", "hash": "9fd1464039c1825050216d34a43b286f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/image_data_uint1.dart", "hash": "51e90aee9581901477ec80a9b73504d7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/drawer_theme.dart", "hash": "62b4a318d3ec0d03d3dc78b84cf0458a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/node_list.dart", "hash": "4068e834e069179f5df23c7868664c19"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/icon.dart", "hash": "826b67d0d6c27e72e7b0f702d02afcec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/widgets.dart", "hash": "8fdc3fcf5f620edf499161761a2ea2fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/archive.dart", "hash": "8b7c7d224502ad3f94501844a82ba5df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/cache_info_repositories/json_cache_info_repository.dart", "hash": "f8113503c91cd843d744fa61b0b15ba6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/activity_indicator.dart", "hash": "0e3d746a279b7f41114247b80c34e841"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/app.dart", "hash": "aae059b82ff751f6e81487ef98668661"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/iterator.dart", "hash": "4d16182e94ac3ec4a2804eb97efe7842"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/permute.dart", "hash": "8171c3b0d66f560aad82b73d43393092"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/framework.dart", "hash": "f9963c0de15655f08d11298175dd45fc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/routes.dart", "hash": "33adcae8de663e2e8f8f410da7fc8023"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/database.dart", "hash": "66f280c66f95d03902082cdd2b4255e1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/widget_inspector.dart", "hash": "0bda807c0c8098d0ca933cde19f49516"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/lib/src/filter/base_filter.dart", "hash": "c9c69bda01b6993e970f366d2d029072"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system.dart", "hash": "06c73ad137e5db31d7e6ba4258ac13c7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/switch.dart", "hash": "1e840a2c03797a7468018e124b957d2f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/style/url.dart", "hash": "13c8dcc201f970674db72fbbd0505581"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/layout_helper.dart", "hash": "1fd7c932679011d491315ff136d13822"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/interpolation.dart", "hash": "44e818727a5e7253e7e35123766c6d69"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/LICENSE", "hash": "06d63878dac3459c0e43db2695de6807"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/utils/utils.dart", "hash": "6c479e0fd2351de96aa7368a1bf8f8ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/media_type.dart", "hash": "101ff6d49da9d3040faf0722153efee7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/equality.dart", "hash": "46e577ec532e21029e9cee153d7ca434"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/permission_handler_platform_interface.dart", "hash": "d3e01a9f299b192bb14b18062c49e562"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sql_builder.dart", "hash": "389352f8e1ecdf1332ad5bcb395bf9c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/webp/vp8l_transform.dart", "hash": "08186749649e0a18ab2fd791372aa62e"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/lib/providers/camera_provider.dart", "hash": "f28830733f671bce9121c9b731c90004"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/icon_theme.dart", "hash": "03d585dfc6055d74a4668e69263afa5a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/models/camera_physical_button.dart", "hash": "f46947ca7956d8b3f9c01af2e542390c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_arrow.g.dart", "hash": "b1bb8356cca8b86afca314ab4898a527"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/image_data.dart", "hash": "0f47efe2d58f61974afa895e54af24c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/pixel_float16.dart", "hash": "a96e0332aafc8c3bc2203db9ae2a9793"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quaternion.dart", "hash": "698a6fc4361dd42bae9034c9c2b6cf7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/intx.dart", "hash": "c3e3bdde1f486b799e08a1ed1b99c76a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/dynamic_color.dart", "hash": "7ffb6e525c28a185f737e3e6f198f694"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/menu_button_theme.dart", "hash": "e461dc9f79fcf6a9e4faf12c8182fb47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/file.dart", "hash": "dcef90946d14527736cde04a54d334db"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/single_child_scroll_view.dart", "hash": "6e22c7f1454c97560ef83096561678dc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/bin/cache/pkg/sky_engine/LICENSE", "hash": "10f2d960c7d6250bbc47fdf5c6875480"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/doctype.dart", "hash": "a0ff9321b483226cdbe4773e33779715"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/pixel_range_iterator.dart", "hash": "ff127e4afd0ccb77d77d5dc0fe73d876"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_exception.dart", "hash": "b062a8e2dade00779072d1c37846d161"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/slider.dart", "hash": "1ae1a412c9f9daff34b9dd63e60cec2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/database_mixin.dart", "hash": "c84b28531f935f9c194ae3a92e38199e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/inflate_buffer.dart", "hash": "5658bef229cf316c21e441181b7ea6be"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/persistent_hash_map.dart", "hash": "7e0e723348daf7abfd74287e07b76dd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/enums/node_type.dart", "hash": "57e5dc91c30bff1774eaaa45a798d0df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/data.dart", "hash": "e0b6567371b3d5f4cc62f768424e28c9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/_executor_io.dart", "hash": "2c69932978b8a76a9a4c22436f4a0454"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/bulge_distortion.dart", "hash": "378a544dca99fb9160d18b7826a80a53"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/shifted_box.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/app_bar_theme.dart", "hash": "62a38b21e9ef4b8a8d5ae1db1c355bd1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/named.dart", "hash": "c5f3b8d4c2e6f53c5fcbdde1e0f03f4b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/gif/gif_color_map.dart", "hash": "455d3c5dfa0230d7d8532e40629a159c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/context_menu_action.dart", "hash": "84f94e87e444ce4ebc562b2707348a8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/util/bit_utils.dart", "hash": "051555a132e400d82256dcab84f3bff7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/matrix2d-1.0.4/lib/matrix2d.dart", "hash": "f75a52eb9c9ec8000ff5ea1dc9ff2647"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/toggleable.dart", "hash": "33ce088a133276cbfd4a33ec49bdcb62"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/banner_theme.dart", "hash": "355538055d623505dfb5b9bae9481084"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/about.dart", "hash": "4bf9cb0fbb8b0236f0f9e554c7207a4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/color/color_float16.dart", "hash": "68c2fdd9ebfed9a49401408e193a4251"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/deferred_component.dart", "hash": "53b9028402187f878713225b48bdd5bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/annotator.dart", "hash": "2678ef31818710bda6610b84fc25d915"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/do.dart", "hash": "96594345ee8d89e2fd23dbca09121153"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/shader_warm_up.dart", "hash": "6d0b38802aff8cbe310e72f1a62750d6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/bzip2/bz2_bit_writer.dart", "hash": "23f783831a77900b3e835880f5b4a1b0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/divider.dart", "hash": "6189af9ddf633811ffb6414cb9d3f744"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/bump_to_normal_cmd.dart", "hash": "8fbf700b7eb25a5450216e0c574e23c4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/list_body.dart", "hash": "18223495a47aa96889552c9834042729"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/cache_managers.dart", "hash": "d4a2cc62bec6dff9fcdc94bc868ea014"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/spell_check.dart", "hash": "e3d917994e875601c2dadaf62de546f2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/button_style_button.dart", "hash": "6a7d9ee6c8fae5e9548911da897c6925"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/snapshot_widget.dart", "hash": "075310a7fe661b71e9a583aab7ed4869"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_expressive.dart", "hash": "be096140df774ec827218c6fe69b80e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/value_listenable_provider.dart", "hash": "bbd255fe46712b372dfe3b99cb340068"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/raw_keyboard_macos.dart", "hash": "f7b9c7a2d1589badb0b796029090d0d5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/text_editing_delta.dart", "hash": "270de9c98f9c1284da0a6af9176ee1f9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/draw/fill_polygon_cmd.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/dismissible.dart", "hash": "c98d71a32518e80bc7cf24b1da6c9c57"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/utils/awesome_circle_icon.dart", "hash": "6660b2771c551ed110be7136a73e2646"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/scale_rgba_cmd.dart", "hash": "4eb1d082220dc2ad1b468434564fe1e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/webp/vp8_bit_reader.dart", "hash": "86395aa7c451cc17d40c8812cf4ccbf7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fruit_salad.dart", "hash": "3c8d2d2b73f69d670141d376642e5252"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/draw/fill_polygon.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/utils.dart", "hash": "9d122acee9d1f43dcdb2ea88fd1fc95f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/tab_indicator.dart", "hash": "ecc072620f2a72e685360292690c8a68"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/photofilters/filters/subfilters.dart", "hash": "17c1e7d683ae9865052b17e4cafa6bcb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/slider.dart", "hash": "48a02b5ec3a8c6127b28927b5960d076"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/tiff/tiff_image.dart", "hash": "7f37ca1c5e766f434e986dbc13cf4a62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/basic_lock.dart", "hash": "25057894002e0442750b744411e90b9c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/contrast_curve.dart", "hash": "9a12cf2a3549924510006db4651a1743"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/navigator.dart", "hash": "047052ee1e98c394dd79f1ddf5983b4d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/_cast.dart", "hash": "c5d37a6208e5c133ef7bf15eccb9cd49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/dither_image.dart", "hash": "4acf8a865879d95ae1449ebf75ed880f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/interval.dart", "hash": "b2a2c73b0b7b528180181e9e4e3b4e92"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/banner.dart", "hash": "674ba42fbba2c018f6a1a5efd50ab83e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/animation/animation_style.dart", "hash": "6cf1ca324535366e2ea214049ffc9918"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/constants.dart", "hash": "be94b8f65e9d89867287dabe5ea1dff1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/exceptions/camera_states_exceptions.dart", "hash": "ca4d07159d699d110a8fd32202c56fbc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/filter_cmd.dart", "hash": "b9f29ff8165c1e0995c166e4cf798a3e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/draw/fill_circle_cmd.dart", "hash": "a2a878e70d5c7db0d25512234ceebdc3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/mergeable_material.dart", "hash": "4201a655a36b0362d1b9f946b10b5e5e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/psd/effect/psd_bevel_effect.dart", "hash": "793fe012ca3ce7197a3af84a51588723"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/error.dart", "hash": "6cae6900e82c94905cc2aaefd806f8eb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/lib/characters.dart", "hash": "fa2a57b3b873fb7db4b8b961735e4ca3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/ink_highlight.dart", "hash": "a9e3af96f170745db1c281777cb6bda9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/frustum.dart", "hash": "d975e51852aa1802c81c738dcb4c348d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/tar/tar_file.dart", "hash": "ebedf04f089c991b3403af4cd67cd66c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/color_offset_cmd.dart", "hash": "3b685472c40ff715205d6827ea715191"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/exr/exr_huffman.dart", "hash": "7ac9b9f90c754f87a8ca194094c64ed0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/paginated_data_table.dart", "hash": "865354d8941afe9359c093d59d7b282f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/debug.dart", "hash": "9f05403438068337dd8f3433d2757535"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/_web_image_info_io.dart", "hash": "e4da90bb20b3980a03665a080c87a098"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/image_data_float16.dart", "hash": "cac2c535ac0f8d7b1bcdae65234aada9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/delegate.dart", "hash": "5fc1a25f60cfa0a0280878377348c63c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/errors.dart", "hash": "2a7dd605fd24026f238835990b2af51c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/path_utils.dart", "hash": "e335de991d295627ccaabe152db13f68"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/dot_screen.dart", "hash": "2a735b45dc29e86d534ac3d86fa599ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/src/tone_delta_pair.dart", "hash": "f5b38c21bf580c89610a8b58c65aae00"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/testing/fake_platform.dart", "hash": "f1a57183b9d9b863c00fcad39308d4c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/permission_status.dart", "hash": "e644eae6cf851b3c46f83af266811a6e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/pixel_uint8.dart", "hash": "aa7fec2f950c6e9a24070a674fb47c01"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/buttons/awesome_flash_button.dart", "hash": "e177cf6193bebafec50010cffb76a221"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/webp/webp_info.dart", "hash": "d165d45cae08f2acdbb5181730bc320f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/isolates.dart", "hash": "1dab3723527db6a19410ed34b6acaeed"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/no_splash.dart", "hash": "9c053b0efcabd70996cc27e9d6c9303e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/timer.dart", "hash": "24a365985ef5e526e029d73522f4f2fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/ancestors.dart", "hash": "3f842dc9d82d8b21557bf598ff4ec83b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/semantics_debugger.dart", "hash": "2c5021ff8faa0330f66b1c501e8d4b22"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons.dart", "hash": "78ce7527fa364df47ba0e611f4531c2c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/transform/copy_expand_canvas.dart", "hash": "4b96e935e200f1b1bb3d3f1bebdc6391"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/basic_types.dart", "hash": "2346472ec1cfdb77f3b27d3b7af72d4c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/keyboard_inserted_content.dart", "hash": "5da306e7f2542e5fb61efff6b4824912"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/fixnum.dart", "hash": "ca96fbf1a27d4f30ff02bfc5812562a6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/input_decorator.dart", "hash": "952fb243dbdb00bfe11b0293238b115d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/_web_image_io.dart", "hash": "e88b0574946e5926fde7dd4de1ef3b0d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/color_offset.dart", "hash": "d012c14244c96dcd2ba4b0a6a7293612"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/string_utils.dart", "hash": "603b7b0647b2f77517d6e5cf1d073e5a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/autocomplete.dart", "hash": "aff0bd5981a82f881b4ac72a321ee9c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/chunked_coding.dart", "hash": "5f5c07df31f7d37780708976065ac8d3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/draw/draw_char.dart", "hash": "9c84571979860df2ddf19e6610f0419a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/physics/spring_simulation.dart", "hash": "2458910beb2b4f3b177a7db027cf7d34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/resolve.dart", "hash": "cbb8e1af9f1f0decfb6fc25a0725c51f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/material_state.dart", "hash": "245a31a30063b63cbfd631fdc2ddf0d8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/dropdown_menu_theme.dart", "hash": "93c17b2980fc5498f3ba266f24c6b93b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/settable.dart", "hash": "f5f653af8a150de004f1b3ca1633bceb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/text_selection.dart", "hash": "138038335aa2c209f231b2694d5aae3f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_anchors.dart", "hash": "3fa7a3bafbab98c305119475eb004a06"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/end_with.dart", "hash": "f95bd67282cf610843bb37b5784f3eae"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/scrollbar.dart", "hash": "85cf42bafb7c0646bd7a99379649da29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/models/filters/awesome_filters.dart", "hash": "9d5d7018efd04483bf9980c9cd0bfa4b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/decode_info.dart", "hash": "fd3473c360fdbe8bfa91c7cb6bfd9cd8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/navigation_rail_theme.dart", "hash": "e472fd233266592e97b3fb39bb1a11dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/authentication_challenge.dart", "hash": "395f07418a28b12b0ed665f32270d702"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/image.dart", "hash": "caad40ad1936874ea93473b300bb909c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/io_streamed_response.dart", "hash": "f179ed2f20226c436293849c724b2c4d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/default_if_empty.dart", "hash": "527ad391c229e34074a6d5c1aa656133"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/image/copy_image_cmd.dart", "hash": "b43f598d1493114e873cdc3113da0cea"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/theme.dart", "hash": "a02235e1a98989d6740067da46b4f73d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/lib/sqflite_platform_interface.dart", "hash": "beea47c079349d8e03b64a5a9dcbc7df"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/animation/animation_controller.dart", "hash": "01aec7b419ee4a50145b3ccdd2a85fa0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/modal_barrier.dart", "hash": "830b9f37313c1b493247c6e7f5f79481"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/animation/animation.dart", "hash": "c8564aa311746f4047cd02e26ff4df75"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/service_extensions.dart", "hash": "d7a6c07c0b77c6d7e5f71ff3d28b86bd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/node.dart", "hash": "a5d0509a39803ffb48cae2803cd4f4bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_map.dart", "hash": "9d273d5a3c1851b0313cd949e7f84355"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/reorderable_list.dart", "hash": "67241b28b6ab2188280fb614f1607b2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/file_handle.dart", "hash": "8401902afa693a8aad93afc52185e846"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/abstract_file_handle.dart", "hash": "576eef6e1ad485270c8041e5ff3a39c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/inherited_notifier.dart", "hash": "12143f732513790cd579481704256dcd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_view.dart", "hash": "3d5ecec2ff4236c99de1acef7a20a152"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/web/web_helper.dart", "hash": "60db0a181494c7db06a18464e2d6e796"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_directory.dart", "hash": "62da8696885bd25977675ac4f7f1aef9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/sqlite_api.dart", "hash": "78ed7ca4d6738b92d47f19b3e557afe8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/binary_messenger.dart", "hash": "056355e344c26558a3591f2f8574e4e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_gzip_encoder_web.dart", "hash": "bfe4571999417357305efc638e123bc3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/cast_list.dart", "hash": "87751ee02d315bd2d0c615bbf2803a3d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/range.dart", "hash": "a6e57cd7b87262b784eb2efe6875a329"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/selectable_text.dart", "hash": "130ada4ea6283eb536d5d8eb0786a631"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/print.dart", "hash": "458f3bf784829a083098291a97123e81"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/type_exception.dart", "hash": "abf77351ef7991f21d4f50727b72d4ad"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/fork_join.dart", "hash": "848f74750b2ecf581969c7a0dd4c2c36"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/palette_float64.dart", "hash": "ded5144bd7a8eeada00ce785f813a007"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/filter_chip.dart", "hash": "0e13760edcb9f90f659ba77c144a3461"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/raw_keyboard_fuchsia.dart", "hash": "a06bb87266e0bac30a263d7182aaf68c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/stepper.dart", "hash": "56198ea7cfc4930ad8bcfc81a2061b78"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/xml.dart", "hash": "2c4c36a5cc838977cf822b6db5d9200a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/string.dart", "hash": "1aaa0309ba77b0f57733e99543c455ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/dev_utils.dart", "hash": "667c5e3e357a840e3d3a6137458c0c34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/jpeg/_jpeg_quantize_io.dart", "hash": "7ad66b1d5ceea5499f559cbfe02d077c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/zip.dart", "hash": "636229be247a1ecd50a669eb2dc73206"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/crypto.dart", "hash": "3b0b3a91aa8c0be99a4bb314280a8f9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/error_and_stacktrace.dart", "hash": "491a33282b614f40bd0fbd3f3b3d45f1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/theme_data.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/adler32.dart", "hash": "23941d12ba026bec23a4801d44728d64"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/vignette.dart", "hash": "7b88fa94ce0390dcda7326498e132e2a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/psd/layer_data/psd_layer_additional_data.dart", "hash": "bf1dee711588963d426381de791ebf67"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/utils/failure_joiner.dart", "hash": "322037160bbd0d8f16bd4e77abfc61f4"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/lib/utils/permissions.dart", "hash": "837932eee3f7616347ddb8b1deee75fe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/draw/draw_char_cmd.dart", "hash": "4b15e20d94c6d6e271a91bbf09fa1988"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/formats/psd_cmd.dart", "hash": "8d27ef18583ac75c97b6eacf71b70a11"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/mouse_cursor.dart", "hash": "b0c6844b0af0cd0539060a0bfcbe3713"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/reentrant_lock.dart", "hash": "7cff949e3b7ac960b63441117b2f6734"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/consumer.dart", "hash": "38c2b67895c0418bce6750d3751a5b26"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/glyph_set.dart", "hash": "62d88517fa4f29f5f3bcec07ba6e1b62"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/image_data_uint16.dart", "hash": "e9b493d703c8bb6d0beb13856a04e26e"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/packages/camerawesome/assets/icons/expanded.png", "hash": "b8bce8882199b9e134b7b2d102317d3a"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/lib/widgets/watermark_control_panel.dart", "hash": "43a8cff07f01f088c6e24213b4fe13c9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/filled_button.dart", "hash": "3de98898d0fea89f0e609dcbf7b69783"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/jpeg/jpeg_info.dart", "hash": "21399ff23c070ac980b92408b5116ef2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/orientation_builder.dart", "hash": "177fda15fc10ed4219e7a5573576cd96"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/event.dart", "hash": "1a7fe7a35dbd168a7f2e10065f4a3158"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file_stat.dart", "hash": "1b430815bdc7bab3a240f27e745f8977"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/allocation.dart", "hash": "9d62f4f58e8d63a8e106a1158eb13a02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/exr/exr_part.dart", "hash": "e9acc90a25e9889101c777ad2b8b8888"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/ink_ripple.dart", "hash": "81fd3ef494f4443fb8565c98ba5a9ba2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/page.dart", "hash": "de67603c6b6c6f55fcd5f8b06423d29a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/physics.dart", "hash": "6e29d5e69c5745a45214fe14da377c1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/lib/src/filter/custom/order_by_item.dart", "hash": "536d9d739618239d21771fa09fca94e2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/separable_convolution.dart", "hash": "b449c24072bad855364cbc7b6d2b3fa6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/adaptive_text_selection_toolbar.dart", "hash": "9ec81b597c30280806033b70e953b14c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/webp/vp8l_bit_reader.dart", "hash": "800e67226fd7cde17e366b58e257fdc2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/raw_keyboard_linux.dart", "hash": "2936a409e1029ec52f7c0003f4db18c4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar_layout_delegate.dart", "hash": "3405e08e614528c3c17afc561d056964"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/platform_selectable_region_context_menu.dart", "hash": "aef544fef0ced7679e0edaf5f8d036b7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/descendants.dart", "hash": "ffaf08c52f141dda6e8be50b3e46ea50"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/lib/src/internal/editor.dart", "hash": "21082ead03d1889cccc7430019b9acea"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/grid_paper.dart", "hash": "6aad5f436704faf509d60ddb032f41b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/color/color_int8.dart", "hash": "a8e860f05e7c9dc72e75b1abeb37f8ab"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/expansible.dart", "hash": "43bc92e2816a78f5d5987930bc3e804d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/lib/sqflite_android.dart", "hash": "3d09396dae741c535c293314adc09565"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/separated.dart", "hash": "641f0dfad31a545ac6fa5515e83920fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/retry_when.dart", "hash": "add862853473647f3bae9dee0b365857"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/focus_traversal.dart", "hash": "fc0c77cc9957db2d82d3e8d56f8ef9d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/pixel_int32.dart", "hash": "e9fa7e20005e9cf3a68b6f3b72db44c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/location_mixin.dart", "hash": "6326660aedecbaed7a342070ba74de13"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/namespace.dart", "hash": "d7259aeee1602df30d051e8fc0523d91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/draw/fill_flood_cmd.dart", "hash": "bb2b1a68375320fcaec0b467657dae08"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/icon_theme_data.dart", "hash": "ae1f6fe977a287d316ee841eadf00c2b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/pattern/parser_pattern.dart", "hash": "79a5f25a1a9d4aa4689bf37171e1b615"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/streams/with_parent.dart", "hash": "cff1275e3376c28419f9b54215ec8b23"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/separable_kernel.dart", "hash": "ee583a481bb36c5c35c3e86da12daa44"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/colorfilter_generator-0.0.8/lib/addons.dart", "hash": "f00f7cbe857990e50be43ba76512e4a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_zlib_decoder_web.dart", "hash": "07076a20e944f2e53a72ab9539fd8327"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/gamma_cmd.dart", "hash": "ea2148f14e308ef3a5afc6d30c4208d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/path_provider_platform_interface.dart", "hash": "09b3f3b1ef14ce885c016f2eba98f3da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/pairwise.dart", "hash": "e8e03ace330da6d410583717e7e5f681"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/materialize.dart", "hash": "7787d9ce2aed834062cd38b022824d31"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/date_picker.dart", "hash": "15ee790ce6b1c0a29d38af8094ad1722"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/reorderable_list.dart", "hash": "a101af17dcc01da8f97ef55242f0f167"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/platform_menu_bar.dart", "hash": "44d59e37041b6305018f70012fef7d52"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/models/analysis/image_plane.dart", "hash": "596b95cf3f1c4586298deacfcddfca7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/transform/resize.dart", "hash": "afb4db9785252d149ec6a4eb919666f7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/two_dimensional_scroll_view.dart", "hash": "28e91fd9077820e2cb2eb981471636ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/formats/tiff_cmd.dart", "hash": "bbbe5dcb2e15583f4d9b3a9faa3e9ff6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/expansion_panel.dart", "hash": "5cedacfe2fd447a541cd599bfc1aef91"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/smooth_cmd.dart", "hash": "adebc51178537c892dff8dabfb1e36e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/xz_encoder.dart", "hash": "0038d8654407851bfe600112ce197113"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/deferred_inherited_provider.dart", "hash": "59ae3a059b0ba1677002bed66f3b8c2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/exr/exr_channel.dart", "hash": "5faf836b8b408bdc4e568206079514e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/src/image_provider/multi_image_stream_completer.dart", "hash": "4870aa3bcaa04ecc633da01dbd2c9560"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_celebi.dart", "hash": "f12f9a9b8bb504f4617bfd1c00d403f0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/buttons/awesome_aspect_ratio_button.dart", "hash": "5cf032c18f0030444f8e397c0d42a8fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/codec/event_codec.dart", "hash": "16d220671ba632751edb02e31809a2a1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/menu_style.dart", "hash": "e79db1a382e61436ed81f9f47dc06d7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/gaussian_blur.dart", "hash": "0f65a7a94a6e48bd964bf67b919dce29"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/file/builder/capture_request_builder.dart", "hash": "d403ac6b5922b9119fa0e2298ff79c4e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/ico/ico_info.dart", "hash": "b53a22ad6e5f3237883329417134dfc9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/has_parent.dart", "hash": "a7ac3293430577fa9c028b0df6607fa4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/utils.dart", "hash": "fab8d6d1b0e81315a3d78131394d31e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/format_exception.dart", "hash": "2128831f60d3870d6790e019887e77ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/comment.dart", "hash": "74fb000405fb96842a3ce15a519d8ae8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/event_decoder.dart", "hash": "d16fc08d820f892bacb508cc3e45935e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/tab_scaffold.dart", "hash": "9434ff8aa06e13d5981ed6ec15eceb64"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/autofill.dart", "hash": "4fa52a6cb3ac24b95e99a20d034f43c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/image_data_int32.dart", "hash": "08b958fba02dba98154840a2086ed75e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/zoom/zoom.dart", "hash": "a566129abefe810dba70dd403ed957ae"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/filled_button_theme.dart", "hash": "52beedf1f39de08817236aaa2a8d28c5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/icc_profile.dart", "hash": "cf82206d6aa3b436992b31383c6de82e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/models/sensor_config.dart", "hash": "8b7094d6d4ef19978fabf98f5503f425"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme.dart", "hash": "a6adbe3868e017441360895c35fd6aa2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/awesome_camera_mode_selector.dart", "hash": "a5140cc600eaf4a85f140fbf67ee5cc3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/byte_stream.dart", "hash": "c02d47d7f7e95654d3eb9b795e416dda"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/flavor.dart", "hash": "912b76b3e4d1ccf340ee3d2e911dfd28"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/models/sensor_data.dart", "hash": "178221c1d8f8b07188a0853d26720d23"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/foundation.dart", "hash": "b4a0affbd6f723dd36a2cc709535c192"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/services.dart", "hash": "0330f85971391a5f5457a20e933fe264"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/utils/utils.dart", "hash": "04f2a3236f9f0080d5571041a0cf3567"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/file_response.dart", "hash": "8a4e81e8fccc01dc69bbc847b75a31b5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/exceptions/tag_exception.dart", "hash": "d851ccbe29621b2c3cf5556211b35b23"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/debug.dart", "hash": "1286926784ce0908d414d696a6321e9f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/raw_menu_anchor.dart", "hash": "a749880c7b2c93609c79f05151beda3b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/floating_action_button_theme.dart", "hash": "08c3fd9ed1607d3a707ffe9b3532218a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/exif/ifd_container.dart", "hash": "fc0ed79bc0d2e7e2b2cb16eedf3139d5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/sliver_fill.dart", "hash": "123520ee3a48eebf4ba444e93436bb1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/permission_handler_platform_interface.dart", "hash": "54e3fc58f0992b887be63771a3d82202"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/mixin/import_mixin.dart", "hash": "50ef33e165498030b82cc4c8d8408597"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/priority_queue.dart", "hash": "34a4d340931147322eaddc77fdc65c22"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/autofill.dart", "hash": "3623c605586d2e37af23d6b746721bd7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/method_channel_path_provider.dart", "hash": "77ed8d7112753d0eeaa860ecd9fc5ba0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/models/filters/awesome_filter.dart", "hash": "6e84a299695fa749b5abf82064cec749"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/meta.dart", "hash": "aaace37762c25bcd679c2ab09129db12"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/edge_insets.dart", "hash": "4349dd08c33e677b65d9e00f13c35d2e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/sliver_floating_header.dart", "hash": "5ffb77551727a0b5c646196e7bf1e9bc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/context_menu_controller.dart", "hash": "c3ccb5b6cd3df44e6587a4f04dd6a4e7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/interface_level.dart", "hash": "1bdb47a9af4b0a5d759937da8ff04db0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/transform/copy_crop_circle_cmd.dart", "hash": "5639a88b45cea601f2da106753c18a72"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/font/arial_24.dart", "hash": "ea6af46b5e31a0681f0725f49a2d4548"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer.dart", "hash": "db799bf48af97b7c0edc93ad96b4a6da"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/search_bar_theme.dart", "hash": "055a5c4a10cb9bc9f1e77c2c00e4ef9a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/preview/preview.dart", "hash": "f1a5fb87e869c5b2a869b6f384a89456"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/psd/effect/psd_outer_glow_effect.dart", "hash": "e83d7b5424a3d75c6729a961b5c723e7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/draggable_scrollable_sheet.dart", "hash": "35e99597a2bc1839b114f890463b5dad"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/floating_action_button.dart", "hash": "55f7619e20765836d6d1c7001cb297fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/value_utils.dart", "hash": "91921fef1791885b747a338372bfdede"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/draw/fill_flood.dart", "hash": "d25e93cf0466c716749ea38f89965396"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/attribute.dart", "hash": "3a8ae5977fc932c86b4b61e92db7a275"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/formats/cur_cmd.dart", "hash": "bbb2078b9c330d5c1bcf8b7d27fe5a89"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/concat.dart", "hash": "eb8f82998d7328c46b04354df987a331"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/navigator_pop_handler.dart", "hash": "0d1b13fd16692571d5725f164d0964ef"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scrollable.dart", "hash": "c8260e372a7e6f788963210c83c55256"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/gif_encoder.dart", "hash": "266bf9974da83f6cf026357578e38975"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/exception.dart", "hash": "5275d424aba5c931a30e6bd3e467027d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/text_span.dart", "hash": "6fc640633e357a75291efec1c68b02ce"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/lib/meta_meta.dart", "hash": "0cf5ebf6593fabf6bb7dfb9d82db735b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/will_pop_scope.dart", "hash": "777aca422776ac8e4455ccc7958f7972"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/lib/providers/watermark_provider.dart", "hash": "8cd042982b42255124df8b4f2ae6e08e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/pixelate_cmd.dart", "hash": "2a5e46e3af695215ebea1c313aa6b3a5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/doctype.dart", "hash": "c2d76b78fb107e358b1ad967f15f1746"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/error_helpers.dart", "hash": "39221ca00f5f1e0af7767613695bb5d2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/list_wheel_scroll_view.dart", "hash": "f500fac00bc25f66e6f49f5ca6de723a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/image/image_cmd.dart", "hash": "9b6f35f54eadc5f90dc8d8e94f26c505"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/response.dart", "hash": "efbedb75be354b65520bce3f0855b8db"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/LICENSE", "hash": "a086f9770acbfc6a5e421b49411d9415"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/lib/src/default.dart", "hash": "7f30d05e05b047b274b1c4b45391d698"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons.dart", "hash": "97f7922aea45c38413930285b604bf18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/draw/composite_image_cmd.dart", "hash": "924162486246d817d582790058fd3284"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/tabs.dart", "hash": "ac902f2f74549f89e0be0f739d94f7f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/memory.dart", "hash": "647e49fd7e2b6707e82858420b630c46"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/binding.dart", "hash": "e40877daa15509fcbd3e465d246dbc09"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/union_set_controller.dart", "hash": "f301af2d0392296f456363085becbf47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_managers/base_cache_manager.dart", "hash": "53745062ff0e01e3d763823156d695da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/photofilters/filters/filters.dart", "hash": "9f51d552979573f90f13ff0e3d9ad1a8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/flow.dart", "hash": "34ebb85f7f2122d2e1265626cf252781"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/lib/src/internal/progress_handler.dart", "hash": "bceefd2c911b7a2cc081013e53cbf784"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/user_accounts_drawer_header.dart", "hash": "bda2eeb24233fd6f95dc5061b8bf3dd5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/browser_context_menu.dart", "hash": "db4a14227247e2524e46f6b0dd9da267"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/lookup.dart", "hash": "b76abc07781824bc4c06d11631188db0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/spell_check.dart", "hash": "24094ce9de1b9222a8d6548d3c01045a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/resampler.dart", "hash": "cad4582fa75bf25d887c787f8bb92d04"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/eof.dart", "hash": "6a083480a6cb878f98927a9271454bd0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/button_theme.dart", "hash": "7b0e6dd1794be4b575ecf8af6475f0e7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/preferred_size.dart", "hash": "dd518cb667f5a97b3456d53571512bba"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/image_decoder.dart", "hash": "eaf5aa7cf4fe19db30724f637b38257a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/material_state_mixin.dart", "hash": "62cbf59e5c816c224ef5eaf803fc877b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/list_tile.dart", "hash": "837da7ede58523b5aff0ccbb40da75ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_file.dart", "hash": "ad139ffd36c17bbb2c069eb50b2ec5af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/LICENSE", "hash": "7cd08032583ab0a8eca895b2365a4583"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/node_decoder.dart", "hash": "86727853a00a22df95e85607270896f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/flatten.dart", "hash": "b9f39f1eac6d7a0e9964cb4c7b2cd04a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_name.dart", "hash": "749e18efee29d6925d7c55e573d3eb2f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/gamma.dart", "hash": "5de7c97cc37ba6e80a1fb52296dc9d10"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/hit_test.dart", "hash": "2d3948bf5dd7b63d100270fce62fa2d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/result/result.dart", "hash": "08e7cd384cfc0214e088945638139ce9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/has_location.dart", "hash": "f91bd03132e9e671e87f0b9066647164"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/raw_keyboard_android.dart", "hash": "c9111e47389ee4b70aab720435a2a2df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/sepia_cmd.dart", "hash": "e9f6f4d931096461f9db81930306d6eb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/enums.dart", "hash": "f4b67c136a2189470329fd33ebe57cb3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/popup_menu.dart", "hash": "67d5620f72c33680625822432b60b613"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/stretch_distortion.dart", "hash": "426a8f505c54993083cd3e67d0d00ced"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/sketch_cmd.dart", "hash": "b0e9fca44e1aa009ccd3502172f9e228"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/exif/exif_data.dart", "hash": "6b5b94053f6ec6277c9aa07aeedc5af0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/sphere.dart", "hash": "63473e31f03ea66a38affa41fd783752"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/binding.dart", "hash": "f6345e2a49c93090bc2e068a0a808977"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/bleach_bypass_cmd.dart", "hash": "9fc88e2a1c0ec6efcc380ae6723f56a2"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/dialog.dart", "hash": "3f3682db58f83007aada4d5c36376b90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/pattern/pattern_iterable.dart", "hash": "f0ae0acd94eb48615e14f6c4d1f5b8e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/solarize.dart", "hash": "a60cca498a6d14a14e4ba60f06d0ffa1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/nested_scroll_view.dart", "hash": "d3b40ca9660164ac83b714d6e2df3843"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/filters/awesome_filter_button.dart", "hash": "b03667d72552b0426e498c3593af1482"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/reflection/iterable.dart", "hash": "763f95cfee7dc8f35f6557eab7e94312"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/utils/animated_clip_rect.dart", "hash": "fa5ba847bdc5927299ef0ac16c0ceac1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/thumb_painter.dart", "hash": "e37bb4fabbf2e61e9b7fbe06f5770679"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/not.dart", "hash": "be4332e6d8c10f4a290e2a412399e1cf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/messages.g.dart", "hash": "3e127bbafbce223b6d416d5cca517df7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/pause_play.g.dart", "hash": "2ad27cdee5e6fe69626594543bd0e7c4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/value_listenable_builder.dart", "hash": "68c724edcc385ae2764308632abb76b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/word.dart", "hash": "05e847132bc525d82c8f22363faaab59"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/switch_list_tile.dart", "hash": "d942bc7ece253c7918e1f60d35e233b0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/lib/src/int64.dart", "hash": "da07db909ae6174095f95d5ee019d46c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/psd/psd_image.dart", "hash": "809deb40be4d8bac164cd994dc6a6335"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib/src/scan.dart", "hash": "acfc0a55deec22276e085dae6197833a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/psd/psd_layer.dart", "hash": "0629948b270b762f155a1082b551a32d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_fidelity.dart", "hash": "553c5e7dc9700c1fa053cd78c1dcd60a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/_file_handle_io.dart", "hash": "1c0ff741d83da7eddfbe5008868c0705"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/color_scheme.dart", "hash": "7bbb6aab4e83fc272886a39c92157201"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/two_dimensional_viewport.dart", "hash": "7bdfcadf7dd131e95092d30909e5b11f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/custom_layout.dart", "hash": "dc552952c58db02409090792aeebbdd8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/lib/src/formatters/string_formatter.dart", "hash": "b5871241f47bc90693cb26fae0bb8616"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/highlighter.dart", "hash": "5265b4bdec5c90bfd2937f140f3ba8fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/color/color.dart", "hash": "cb93a10634ade1444de7395c5ca7b9c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_wu.dart", "hash": "c0da8171c63f0ab4e822dd094fc2c595"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/stack_frame.dart", "hash": "eb89408ce23b2abcd324ea5afb05a1ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/hmac.dart", "hash": "2b5fbc54f77ca9c1e5ac90eb3c242554"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/scrollbar_theme.dart", "hash": "b3019bcd49ebc4edd28b985af11a4292"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/lowercase.dart", "hash": "044ac7a861e88a6b5e7e2d2c59ccb7bd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/shared/annotations.dart", "hash": "9a469ff3de60c96cf2f9b0523b651782"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/image_data_uint8.dart", "hash": "99e8f0c160752708ba197ddd821d9198"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/card.dart", "hash": "90d9d45eef80ac53b194a71da4e10975"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/formats/decode_image_file_cmd.dart", "hash": "d369a8a40e0790ad450e8796abfa5170"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/quantizer_map.dart", "hash": "b6bcae6974bafba60ad95f20c12c72b9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/grid_tile.dart", "hash": "9c169d41e4740bbc21d0ce33bc753119"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/writer.dart", "hash": "21a6f7aab6021cd2c8c69f9cd78ae36d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/bottom_sheet.dart", "hash": "c442be28b905f64b74f6e9f8e5903820"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes_dart_io.dart", "hash": "9df03a340058a4e7792cd68745a4320c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/custom_paint.dart", "hash": "43ba6279385eca1e9d14a3e4d020a3ca"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/functions.dart", "hash": "41f7bdb7d1eb3c86c21489902221b859"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/with_latest_from.dart", "hash": "eab456316eb52f6f668d1dd2800c1085"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/velocity_tracker.dart", "hash": "be0a77cf3f0463f3dacd09ec596d9002"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_neutral.dart", "hash": "3ee18da390e16ca65f2ef168adb8a1ef"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/fractional_offset.dart", "hash": "e7b2de136a99cf5253477d4fb4138394"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/scaffold.dart", "hash": "498db9e29a08e6fdc8aee5eeb4d204ce"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/switch_theme.dart", "hash": "a88d8ea7c8c98dd1d35ad2853f04efe1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/primary_scroll_controller.dart", "hash": "58707cf455f97f907192b4ff92d36711"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/color/color_uint8.dart", "hash": "4448e0a7308af3a01f944c26cedbf356"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/carousel.dart", "hash": "7270419a025fdbf7840e542397db0c5b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/xz_decoder.dart", "hash": "9af7e9029f12f91f914a37459b228013"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/sliver_resizing_header.dart", "hash": "eca5aa939aa9722ead4b6c347fb4d11a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/utils/awesome_oriented_widget.dart", "hash": "4c8fca1e1ff0c5a9177dcb860aad6113"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/image/create_image_cmd.dart", "hash": "c2385aeb377707657af56bbf26382d8c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/position.dart", "hash": "faedea5895c9ddd2b2c270817c61d1f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/possessive.dart", "hash": "08e17247b131fb75466c336e9a11fcfe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_gzip_encoder.dart", "hash": "21693dd162df8a9a6807a23224f6fa61"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/models/camera_orientations.dart", "hash": "78bc351c305af1a5e6fea8924f5e19f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v8.dart", "hash": "e3d03ffb9ffa123af98df771a98759c0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/annotations/has_buffer.dart", "hash": "22acb270c1bb267ee16b3d64a3faa825"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/lib/services/camera_service.dart", "hash": "981e52b5cbbeb36d23421838662ff798"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/app.dart", "hash": "dec43cdc695f6ef4f0a33ae459c0e58c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/adaptive_text_selection_toolbar.dart", "hash": "5c96449c2a494ea8f3a50ecc3ba9af74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/states/photo_camera_state.dart", "hash": "a013f822f4a234c8abf297640c806ef6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/expression/builder.dart", "hash": "d2b684e31e63b6c876b2c0266705447a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/drag_boundary.dart", "hash": "1e0ea989110b1544dbaf1fdf3d9864cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/digit.dart", "hash": "ea08cf8b71713e3535d5a2164a8bc7e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/service_extensions.dart", "hash": "7abc7e5212374d29bfe5372de563f53c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/events.dart", "hash": "89aeee125822690cbd46b2ff43c76ec1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/platform_channel.dart", "hash": "78a0faeef5f0e801943acdca3f98393d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/pixel_int16.dart", "hash": "975b7410c40cd62434f367c5dc38c4e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/path_provider_windows.dart", "hash": "38dc31b8820f5fd36eedbf7d9c1bf8d9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/elevated_button.dart", "hash": "c2dcf2bcdc85d007f9729621d13cccf4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/flexible_space_bar.dart", "hash": "9d6f9dd391f828bccdbb47c5072c04c1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/dart_plugin_registrant.dart", "hash": "44b8efa69ec831d1a0ce74c20ecc27b4"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.json", "hash": "f55be2876353750f43cbdce61440a6ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/utils/math_utils.dart", "hash": "e4ee21048ab83cc50d61ac3784afa9f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/devtool.dart", "hash": "2d7d80b5c908559a133f8729b6e755c0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/debug.dart", "hash": "51fa10cf30bde630913ff4c6e40723ba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v5.dart", "hash": "cc8112e5daca3ae7caf3bd7beda5f39e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/notched_shapes.dart", "hash": "7821d01f98c559fcbec46a41b4df7ebf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/layer.dart", "hash": "659b88645890c6437ea5ce4928e8871e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/sqlite_api.dart", "hash": "9442b7f8efe89c42cd235c4047480ce4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/expression/group.dart", "hash": "f31a685ec42e95decf8c1937de3a5856"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/models/analysis/analysis_canvas.dart", "hash": "0166a5b45d8d41f9e8342573d923ff15"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/token.dart", "hash": "8006c8d72d7de5fbf9f6034104c30166"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/lib/src/listenable_provider.dart", "hash": "fe16b487322631b50c3cbb09de987315"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/jpeg/_jpeg_huffman.dart", "hash": "2aec3a3cf7bd1941b109079dbc526e00"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1/LICENSE", "hash": "4cb782b79f6fc5792728e331e81a3558"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/media_query.dart", "hash": "98cd866294c42f2faff3451e5ca74bfa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/inflate.dart", "hash": "bc1c29a675191d036c42a321c435e5a5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/visitors/visitor.dart", "hash": "87e0c94a0dd945f819a8bd24a9ac5e67"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/zoom/awesome_zoom_selector.dart", "hash": "0dd75a45d5ca68addb349af96438c0a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/remap_colors_cmd.dart", "hash": "862f55eac8d2b526f466a77cd4250ba6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zip/zip_file_header.dart", "hash": "c104b5911865a0de1f2005e74b5b3a8e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/sql_command.dart", "hash": "4e7b4cf98b7ea45960f7d79fffac5705"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/editable.dart", "hash": "eaed941ddb98b44c090d06e0be0a7562"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/physics/utils.dart", "hash": "727e4f662a828d4611c731f330a3d79a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/drawer_header.dart", "hash": "f996ce49eab57718350b84e11ea3192d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/transform/copy_rotate_cmd.dart", "hash": "79c8c29869d6a6ce759fd9146dd3e67a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/multipart_request.dart", "hash": "de670519e8f1f432d9f1a21fdd05b4b3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/pointer_router.dart", "hash": "8c1a2c1feaeb22027ba291f1d38c4890"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb3.dart", "hash": "257ca4608e7d75f1db8d4c3ab710ac70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/psd/effect/psd_drop_shadow_effect.dart", "hash": "23a1ce1bb6e470fe28cf713a127a4a6f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/internal/undefined.dart", "hash": "bb00c98e50d3c71d4ab7ac7c46122f3f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/form_row.dart", "hash": "5f64d37da991459694bce5c39f474e5f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/ico_decoder.dart", "hash": "aa564414000b3ddbdd63397c40d471d4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/text.dart", "hash": "f52860ffbd4c6858f092292d1589d556"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/repeater/repeating.dart", "hash": "282aa0046bbbfcbc30050f7fab282778"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib/src/eager_span_scanner.dart", "hash": "bdc22e9e77382045196b5aafd42b5e55"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/shaders/ink_sparkle.frag", "hash": "8af2b6b4632d42b4e1701ecda1a3448a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/union_set.dart", "hash": "0073f703be7f7ddbd7f04d1b740f35c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/formats.dart", "hash": "a4ade17b648f8cd60dec9c2cb0df992b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/lib/src/lock_extension.dart", "hash": "92197f660f809dbb94c7d3d67b9f24e0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib/path_provider_android.dart", "hash": "eb368258f0f9fe56110bdc238488af97"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_with_context.dart", "hash": "a8f2c6aa382890a1bb34572bd2d264aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/output_file_stream.dart", "hash": "d48cdb6d5fe440b51fad4b4832a9df57"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/search_ellipsis.g.dart", "hash": "c761b80666ae3a0a349cef1131f4413d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/image_data_float64.dart", "hash": "44e3a64584cfab58bdd1bca2e4ac1ad6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/autocomplete.dart", "hash": "4e8a70d478371e0d995f080a6eaa8120"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/builder.dart", "hash": "3131838b519fd1bcdc3486eb44d640d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/hexagon_pixelate.dart", "hash": "359e7677b7bfccace0046445b70bc916"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/font/arial_48.dart", "hash": "9d3237ab118a617bd2a70550c09565e4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/button_style.dart", "hash": "982099e580d09c961e693c63803f768d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/color/color_float64.dart", "hash": "474f54dba895d824a3e63729b5c616e5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/adjust_color_cmd.dart", "hash": "ed423710f0639e266e304e8cd06c3ce7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/desktop_text_selection_toolbar_layout_delegate.dart", "hash": "c679063104d2f24639459c8ab3eed77a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/generated/sequence_8.dart", "hash": "9dbdc6f759d68f1ba1b47b561de1e299"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/_capabilities_io.dart", "hash": "faf4d014b3617ede3150f80eba25e3b4"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/lib/utils/constants.dart", "hash": "8b35fcc90f7e70c9320d9bdf97aff62f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/flat_map.dart", "hash": "115640739fe47a728c4b1c3a4b4c3506"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/uuid_value.dart", "hash": "6edd9b910f41e28e574e1c5308ef8b74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/parsing.dart", "hash": "16d4d82628956a3b88ae5de8480aae49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/formats/webp_cmd.dart", "hash": "e0a54cd3ee5d3060f1720d631dcc2af3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/ascii_glyph_set.dart", "hash": "7050c8c94b55eb51260ca54708b460fa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha1.dart", "hash": "dfb8ebcfda08e6d9b294f49d74ad9f98"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_notification_observer.dart", "hash": "a309d8ca64c3efb3ad74b742ffb0e1dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/sql.dart", "hash": "9ab11d900c41a880b39e97693f383b5d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/misc/label.dart", "hash": "7de7aec8bf9b53488692403a3feb7672"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_zlib_decoder_io.dart", "hash": "91da029800f179c428186ec4fb7b4df7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/exception_impl.dart", "hash": "7b3fbf91245e315040bd120bc9bf51ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/colorfilter_generator-0.0.8/lib/colorfilter_generator.dart", "hash": "a94e370a9055445dd451880d2c7aa5c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system.dart", "hash": "c23a0415bdaf55efdf69ac495da2aa9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/image/add_frames_cmd.dart", "hash": "f3aadbf669371d9a1e1b0d2b84f9682f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/LICENSE", "hash": "5d89b1f468a243c2269dfaceb3d69801"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/widget_state.dart", "hash": "3c24303086312d7181ffa10d0521029a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/scale.dart", "hash": "abbe93b36782df11e43e348dadf52e94"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/gif/gif_info.dart", "hash": "02e8f1df71978a19f6f6c2ec90d460aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/placeholders.dart", "hash": "4ccaab1f2ffd61fd5998a2fe8a9be886"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/sqflite_logger.dart", "hash": "6745a4321f65340dc91faae80415984b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/filters/awesome_filter_name_indicator.dart", "hash": "7b0cd329329edbe5492e56dff453f427"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/storage/file_system/file_system_io.dart", "hash": "7caf4c65583e594208feee7e60872fea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/font/bitmap_font.dart", "hash": "9970fcedbc1fb233e6de79e1a1f2d025"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/rx.dart", "hash": "f222f3be7d9e176a7d8ba3252825c9f8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/utils.dart", "hash": "8a7e3b181572ed50e923e5dc05a7533d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/close_menu.g.dart", "hash": "a0816d2682f6a93a6bf602f6be7cebe1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_zlib_encoder_io.dart", "hash": "e6c8acbe554eee8c1a1ee7afafa19513"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/text_selection.dart", "hash": "0c46b12a4e0301a199ef98521f0ed3ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/core.dart", "hash": "b969cd0066fa07b8082edb76d2af77e1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/base_response.dart", "hash": "358495c0e2a26e0203cd810f7ca85795"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image/image_handler.dart", "hash": "a9ad1aa35c1b9117f15a379ef03480dd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/permissions.dart", "hash": "1addb41b1ec88a6b5674bd3486e9e225"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_huffman_table.dart", "hash": "d4f30e5344c82c8daa98c9d64564f7f7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/util/_file_access_io.dart", "hash": "8ca13e8c344e08569cbf799c09805f93"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/models/analysis/input_analysis.dart", "hash": "c29a309b17eb446138ebecb6ca62e934"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/tar_decoder.dart", "hash": "46621d446bf233db720f709328a20791"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/text_field.dart", "hash": "29d1f8b59096b4d11d693c4102a08499"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/range.dart", "hash": "57de88edbfb0d8951419051441a6de56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE", "hash": "2d0c70561d7f1d35b4ccc7df9158beed"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/checkbox.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/pixelate.dart", "hash": "a32e48fc728f0656344a1097e2114d33"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/gif/gif_image_desc.dart", "hash": "55ebbe91f54964f7e12c8d729b42da22"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/sliver_tree.dart", "hash": "d99e76320b224b4518e76f311ef4a804"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/combined_wrappers/combined_iterable.dart", "hash": "67d16e841606c4e5355211fe15a2dbfd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/exr/exr_wavelet.dart", "hash": "0478f9ce3a90e5edb569153a9f90b867"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/formats/pvr_cmd.dart", "hash": "3327c897cbc1b7ef72fedeb3e80e947e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/exr/exr_piz_compressor.dart", "hash": "54f5ce5a7288af15b19f041cc31cfe24"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/system_navigator.dart", "hash": "0db5f597f1cc6570937e6c88511af3a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/pixel_uint2.dart", "hash": "80e53852d350b1a1e124a5771229fdea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/dynamiccolor/variant.dart", "hash": "8dea906a9b8773920b6d1ccea59807bf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/sqflite_darwin.dart", "hash": "644e5e32abaad61eb192128f412424ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/webp_decoder.dart", "hash": "acedb4b74e1a388f3da923044147e20a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/sliver_persistent_header.dart", "hash": "2a374faf6587ee0a408c4097b5ed7a6e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/hdr_to_ldr_cmd.dart", "hash": "ca8171cdc86af1f67ed5260a2006d387"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/pointer_signal_resolver.dart", "hash": "28d3a26c44687480bac3f72c07233bf6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/outlined_button.dart", "hash": "438f80a3d5361329aa6113e3409440aa"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/rendering.dart", "hash": "4bd3950a0bf4a9f9b09f97594e363d36"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/arg_utils.dart", "hash": "9812b8e536c69068c0e5f3d3db20c140"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/billboard.dart", "hash": "15400e3dce9fd57aceb301e34b11b8a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/color_halftone_cmd.dart", "hash": "4dc86275b4c7a8c036c4fe00e83b43d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/filter/gaussian_blur_cmd.dart", "hash": "bdf7288f96a1cb63344dc970b2bb2958"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/grammar.dart", "hash": "467b2b8993363b1b27f034f6c1cca476"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/formats/decode_image_cmd.dart", "hash": "2975e3caaf921335c862760fcf807fa9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/camerawesome_plugin.dart", "hash": "c6517a7eb2daeece2870dbc311e00750"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/magnifier.dart", "hash": "52d0e96cbfe8e9c66aa40999df84bfa9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/switch_if_empty.dart", "hash": "f1236a5e582b3794b3fb2302d7297451"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/selectable_region.dart", "hash": "81036c1ed2827ac1db9fee5a900f568d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/sliver_fixed_extent_list.dart", "hash": "2adcbf9fb509dd8fe8864a702db29043"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/system_sound.dart", "hash": "39f5f34a4d3615c180c9de1bf4e8dde8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/calendar_date_picker.dart", "hash": "224c14ef0447e287cbae1b7aed416290"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/bin/cache/engine.stamp", "hash": "174cc783c9979e0b20985dbbd4f28bf6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/texture.dart", "hash": "7c07d5cc739ae29abcfbf6343ae84fdf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/exr/exr_attribute.dart", "hash": "bf6e3c3ff08b7565ff80f5dfd7cc5019"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/uuid.dart", "hash": "ebddd1b3c6af3141a7d2025fadf56ada"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/app_bar.dart", "hash": "7706f479f74f6076ef8113576fe54749"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/nodes.dart", "hash": "8608080cdfc143d462b0f9947dc0d7c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/ffi.dart", "hash": "ae66b0cbdfe2e2a5a99c5dfa48fd5399"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/badge_theme.dart", "hash": "e1a148a465b713a6366d5a22a1425926"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/code.dart", "hash": "2d312745691a82b398796ad2f38ac63c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/transform/copy_resize_crop_square_cmd.dart", "hash": "a5080649a7a2b90383d179db6474988a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/ignore_elements.dart", "hash": "908b86c4378330e5b303026c8c3e29aa"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/spacer.dart", "hash": "d2372e0fb5a584dcd1304d52e64d3f17"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_zlib_decoder.dart", "hash": "0d832054a2b5c2f650f959ee311fad4a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/sample.dart", "hash": "98ad95f9d48fa93a9cdc4a8fa0f69c73"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/utils.dart", "hash": "ebf21341320c02b09bfd8dcbfc683398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/converters/event_encoder.dart", "hash": "df916478c93bc4bc0bc9adb2cc286153"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/standard_component_type.dart", "hash": "09973ba0a94d2d819052c0544dcdce70"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/mixin/platform.dart", "hash": "b92ed901e8df2fde6d4739ed5e59051d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector3.dart", "hash": "478e1071c9f577b6cabb8d72c36de077"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/formats/write_to_file_cmd.dart", "hash": "ee713d46bc7b2e3cd7799c88a9f69d1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/using.dart", "hash": "f70fdb6ec3125a8d6f6fb5ea4cbac59a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/link.dart", "hash": "1f334b50f4df781bbbfab857581c3540"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/token.dart", "hash": "6c8afaf3db5be20a458530a92ff971d5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/webp/vp8.dart", "hash": "ce3413ef2b8dd7f11825b978400b2ce7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v8generic.dart", "hash": "00a661dfeb90c5dba43ec7e638141966"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/lib/src/types/thumbnail.dart", "hash": "7b0e66bc19420cf4636eab0aeba85ce4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/tab_bar_theme.dart", "hash": "a91b4b0d0d10b955e8973126cf288ea4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/palette_int32.dart", "hash": "93229401ae949ba90ee26ef0ea620e3c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/carousel_slider-5.1.1/lib/carousel_options.dart", "hash": "840941a2f7f538f3eb172d9681f41d58"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/formats/exr_cmd.dart", "hash": "1bf3f45c7239c35e80c3aabe9d9dbd36"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/exif/ifd_value.dart", "hash": "48a13dd2dbffcc1d2f82bf2ce2dba01b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/constants.dart", "hash": "aa4b5c0cdb6a66685350611b29ca9d38"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/color/color_uint4.dart", "hash": "9f2d66e38a84d256ada0a12babf454ec"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/reinhard_tone_map.dart", "hash": "43a353a93ba823dd0d891c6e45dedf3d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_tonal_spot.dart", "hash": "75f947f0ba87a0789a3ef91542bbc82c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/streams.dart", "hash": "25a929555febc01ae405a334b5ab9ce1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_close.g.dart", "hash": "ef5fc00d685cd2a36c4de80e1c7e3a8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/grayscale.dart", "hash": "70c336baa9e54360074a61a076eadab9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/entities/entity_mapping.dart", "hash": "5abb58e10e8ea85ea5990a97ee20ae4e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/intersection_result.dart", "hash": "789e79772bba1132b3efdb60636a3ccb"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/popup_menu_theme.dart", "hash": "384c15d93757a08ae124e6c2edeb4e9e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/zlib_encoder_web.dart", "hash": "bcd44ca46262b2c1f9c30b47b7279f74"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/platform.dart", "hash": "cbf041463d4a85115a79934eafe8e461"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/extensions/following.dart", "hash": "662638321f1933cdab78277f222b8aa5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons_data.dart", "hash": "ac08cb84358e3b08fc1edebf575d7f19"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/client.dart", "hash": "b16458199371a46aeb93979e747962a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/src/image_provider/_image_loader.dart", "hash": "af9339e8836ca91cbc9c8fd6b2de7cc6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/color/color_uint1.dart", "hash": "c50d012473e13e1de7e3a7b936455e5b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/psd/effect/psd_effect.dart", "hash": "32dc6c16dd28cf0df649467abef5695f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/png_decoder.dart", "hash": "66956764e06138abf737587b2edfbfa7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/command/transform/copy_flip_cmd.dart", "hash": "df53475b9cb0639068a168bd898d88f1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/util/point.dart", "hash": "f02acaa4c7427638a062be8d8c4b9c65"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/streamed_response.dart", "hash": "a004396fa64ff2163b438ad88d1003f4"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/lib/widgets/watermark_selector.dart", "hash": "b684f59eb3780f2578a2b13597de2f1d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/utils/event_attribute.dart", "hash": "304fc982848b57cf13da0ec511f05ed9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/crc64.dart", "hash": "5ea15c157f8f55f47d832255a3b73cc0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/text_layout_metrics.dart", "hash": "13be7153ef162d162d922f19eb99f341"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/drag_details.dart", "hash": "f350db07fdddbcfd71c7972bf3d13488"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/router.dart", "hash": "a89f6417642d57961ee87743be4a6a2b"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/kernel_blob.bin", "hash": "78fd97d6ebaee7b95e50734556f28955"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/awesome_sensor_type_selector.dart", "hash": "0bc9cd9c09fb859e38dc156780eddf88"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/path_set.dart", "hash": "1b20a6e406ca8e79675b2ebd9b362d10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/sequence_equal.dart", "hash": "e9e452fa340b489a49dba00eabefa3ba"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/_html_element_view_io.dart", "hash": "61d3c1705094ee0ea6c465e47b457198"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/message_codec.dart", "hash": "bf50f61746b9744a0e2d45a88815288f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/letter.dart", "hash": "35ae3adcf5e51919e36509ef828107a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/quantize/src/point_provider_lab.dart", "hash": "6566a35ff0dea9376debf257bdb08fba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/image.dart", "hash": "1e4c39e2cc3aebea95568a1608891e81"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/scroll_position.dart", "hash": "94c0c017ccb267b7cacc7c047ee5b9c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/png/png_info.dart", "hash": "02b5619e59c64a304ae2d32ab820439e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/skip.dart", "hash": "be231020db4ff03ccedf0cab8d50d12d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/digest.dart", "hash": "d623b1e2af43bcd9cde14c8c8b966a8b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/skip_until.dart", "hash": "22ce2f0be207fd716e4ae18e312f5cf0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/painting.dart", "hash": "4bd60bd8ede4b9dad954493d26d3e586"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/jpeg/jpeg_data.dart", "hash": "565e9b388f77a07466702a60acfcc9e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/parser.dart", "hash": "7aac958977c79edf01e6ad44a726b52b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/charcode.dart", "hash": "b2015570257a2a6579f231937e7dea0e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/draw/draw_circle.dart", "hash": "cca3136c0fc75f8079f0255edab35f51"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/chromatic_aberration.dart", "hash": "06fb76eb015104221778ccf5afa6b6d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/sqflite.dart", "hash": "5c96fe82a9bf2dc00db9d93c2c0a41a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/zlib_decoder_web.dart", "hash": "d9bdac65ce6a5202138f25423578265b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_file.dart", "hash": "d9343422c8a6829bd05698de67232591"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/_timeline_io.dart", "hash": "90f70ffdd26c85d735fbedd47d5ad80b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/definition/reference.dart", "hash": "e7a9dcfeea903e16ba7ddc8cc31960d8"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/table_border.dart", "hash": "bbc7eccdbd8472a2180e0dffce323bb9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/navigation_bar.dart", "hash": "9b52b890a7d94fe05f5f3ab8b7324b35"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/simple_name.dart", "hash": "208d1ef7a6cc2445551b3138139613bd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/sliver_group.dart", "hash": "630fe5f86ee37699c534f9c91f21f03c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/enums/attribute_type.dart", "hash": "a9d570114e5a6e733fb029f6b3cffad7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/transitions.dart", "hash": "22ad3e3602e0fc7a63682e56a5aeaac0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/core/token.dart", "hash": "595737cf044c5d483e4615a1b0e1db71"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/exif/exif_tag.dart", "hash": "e7ff6ad13ea7debda002ce2baab93713"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/semantics.dart", "hash": "4b784d6e4f290bd6d5a1f38bfb5701d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/archive/archive.dart", "hash": "5b422e1bbc3ac131bb7a1ad118ad5a3c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/copy_image_channels.dart", "hash": "fe56d7ccd08a3ad01a830defc14b2d78"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/collections.dart", "hash": "f209fe925dbbe18566facbfe882fdcb0"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/recognizer.dart", "hash": "990244fbee5d6f551e98a4bcce092389"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib/src/utf16.dart", "hash": "10969c23d56bc924ded3adedeb13ecff"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/_background_isolate_binary_messenger_io.dart", "hash": "991024814d51967a20be5851be93a8e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/image/palette_float32.dart", "hash": "e6fb4a12cc49f9ac79dfc8b1ac202674"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/src/image/image.dart", "hash": "e03a984efe74a058d3393aba7c55fe1f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/nodes/comment.dart", "hash": "87546066dfc566126ed9357805535e97"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/text.dart", "hash": "7217dd37b49bab8e0319d4fb26d14d8e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/util/file_access.dart", "hash": "b5306ef266e9679f00ff947704683618"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/contrast/contrast.dart", "hash": "0c9bd1af5747fd55e7488c731ad32dee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/LICENSE", "hash": "83076b20232b9e5a9963f667e438977b"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/motion.dart", "hash": "505f6c9750f9390c9e9e4d881092cef4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/dev_utils.dart", "hash": "9a4ee08ca541303a2aee95f83f548ce1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/iterable_zip.dart", "hash": "df699735e3bcd730f16ce377d562f787"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/utils.dart", "hash": "7a6fe2bde5e3bf653cd473308d8402c5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/border_radius.dart", "hash": "b75501071b7ff5d32ddab4c6ea5d2f84"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/arena.dart", "hash": "5486e2ea9b0b005e5d5295e6c41ad3c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/models/models.dart", "hash": "c14040322dee99ecfd4254d216db70de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/where.dart", "hash": "bab2294ec70ff137aca684dd19203943"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/transform/bake_orientation.dart", "hash": "f2df259387c84faa263c7367c3ddd652"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/performance_overlay.dart", "hash": "c5e44030289c2c25b26c5b3aa843b3cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/lib/src/request.dart", "hash": "817e03d87771f133aacbdef89c1e6fc9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/photofilters/filters/color_filters.dart", "hash": "98d973725577f0bf08909c6d8858b028"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/text_selection_toolbar.dart", "hash": "2553e163ea84c7207282c18b5d9e14c1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/interactive_viewer.dart", "hash": "bb7bcb463df2ae0f5f952d439fdb384e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/icon_button_theme.dart", "hash": "ac317f8ed3b04bec644817e6f60a28d7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/scheduler/debug.dart", "hash": "d72a4ddaf6162d8b897954e02b4a2a4c"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/debug.dart", "hash": "0575a78fbb39a292302737868752da77"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/lzma/range_decoder.dart", "hash": "29597979eb10c767329562fc72cde9df"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/shared/types.dart", "hash": "7e327134a49991d7ba65bbfe46bb8f4c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/repeat.dart", "hash": "57ef315bc7f35da7e489915ef8572118"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/cursor.dart", "hash": "5bde4f62a64276d44e1ef4ee3bf194f6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/matcher/pattern/pattern_iterator.dart", "hash": "accb24637ddbe55d7a3f76e4618bdd22"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/take_while_inclusive.dart", "hash": "389552e6852c3214ca6857ddadb7cd0b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/default_selection_style.dart", "hash": "bbc9542eb5e3c4701c24bc1268b8165c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/rng.dart", "hash": "d42791632fba8e51a8bc7535cee2d397"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/exhaust_map.dart", "hash": "4c61dffec4ef48c5b09f3009e7765657"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/models/analysis/analysis_image_ext.dart", "hash": "47010a78422ba4ef423b24009fd55cd7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/lib/platform_utils.dart", "hash": "7e0799a75731be4ed5439456fdbe4c5a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/lib/src/filter/custom/custom_columns.dart", "hash": "0ab8a902e51ebd7210bba95312c66692"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/character/char.dart", "hash": "00456c7fcfc11e9ae46af126277652d5"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/list_view.g.dart", "hash": "f8275b74f8f83272b8a8d1a79d5b2253"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/tiff_encoder.dart", "hash": "4df6f02ceff64deb47099290e20f52c9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/combinator/choice.dart", "hash": "253b43ba9075c77f9ce5267d91880da6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/jpeg/jpeg_component.dart", "hash": "af1c4f8cc873b4916559c6965a1a18d7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/lib/src/cache_manager.dart", "hash": "b188e0026dde1c7ef925b5efb80450ef"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib/src/span_mixin.dart", "hash": "89dc3f84db2cd1ea37e349fdb1de09bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/webp/webp_filters.dart", "hash": "917eb28a20e326d38fd911c2fafa187a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/inline_span.dart", "hash": "e3127548d819af5ec9ecb10b5732b28e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/cupertino/icon_theme_data.dart", "hash": "eca4f0ff81b2d3a801b6c61d80bc211c"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/FontManifest.json", "hash": "dc3d03800ccca4601324923c0b1d6d57"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/utils/subscription.dart", "hash": "8ab19033cc6a918c1e4f454495a9ab5f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/util/math_util.dart", "hash": "8be40c3f085a9e45cb579f3127209364"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml_events/events/processing.dart", "hash": "5a7bd956aa537e95be882d4809232c39"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/feedback.dart", "hash": "c8f69577793923bfda707dcbb48a08b1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/title.dart", "hash": "e556497953d1ee6cd5d7058d92d4e052"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/psd/psd_mask.dart", "hash": "7e4f9d47b668c76a73d548dc9f00beff"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter_tools/lib/src/build_system/targets/common.dart", "hash": "8b65a0312de1594ea0989e8ce1d4b257"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/checkbox_list_tile.dart", "hash": "********************************"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/table.dart", "hash": "9b98b196040f00fd2fbaf5f7a2309e6b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/streams/connectable_stream.dart", "hash": "fcbda87916b8b2c4f7b88460ac9bb415"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/pinned_header_sliver.dart", "hash": "4e04af41f89adf9231bad1579f5bb9a1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/draw/draw_polygon.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_gzip_decoder_web.dart", "hash": "4e6086182f043506862ee6a35d360bae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.3/LICENSE", "hash": "2a68e6b288e18606a93b3adf27dbf048"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/lib/src/types/base.dart", "hash": "86039b13313ad468f867bb5522411241"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/common.dart", "hash": "493b51476fc266d10a636f520fff01fc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/palettes/core_palette.dart", "hash": "d35b72b249d19f54a4cd6f22ff3299e9"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/radio_list_tile.dart", "hash": "cd7a7fd807697152dfdaeb3109e4f4f4"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/converter.dart", "hash": "ed5548873fcf5a0a5614fc52139600b8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/action/continuation.dart", "hash": "33717fbf3f4de35b5e494d284a252bb7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/mixins/has_writer.dart", "hash": "0537fb6d3d370f2fd868e2654361356a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/rendering/sliver_grid.dart", "hash": "b61a261e42de1512c8a95fd52ef6540d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/draw/composite_image.dart", "hash": "5c50a64acda94bfe72376e7117408b03"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_zlib_encoder.dart", "hash": "4e116d0c1f3080cec1492036827c7fcb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/unmodifiable_wrappers.dart", "hash": "ea7c9cbd710872ba6d1b93050936bea7"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/overscroll_indicator.dart", "hash": "247fd4320e1e277acc190092bf6d35ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/util/aes_decrypt.dart", "hash": "1301cb8ba7f61c2dd521e6c1cac7f78e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/arc.dart", "hash": "511ff5c6f0e454b22943906697db172f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zip_encoder.dart", "hash": "3f04e896c861aab2ca83e445e65b6d10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id.dart", "hash": "32f5f78e5648f98d8b602c6233aa4fc5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib/src/sha512.dart", "hash": "e4973bdb8ceac8b88cdefee5f56f0fa0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/skip_last.dart", "hash": "355616c9fb00a5e0ec803fffa8f33eff"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/lib/widgets/draggable_watermark.dart", "hash": "ce0162cd1aaf1ef274de583ba72f412a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/exr_decoder.dart", "hash": "00a403a0d267cc633f774b603f8c4e74"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_home.g.dart", "hash": "edbd68eb36df4f06299204439c771edd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/toggle_buttons_theme.dart", "hash": "262d1d2b1931deb30855b704092d3cb4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/lib/v4.dart", "hash": "916cd94d810ea5b86f0cdc685dc38001"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/lib/src/compat.dart", "hash": "8a31d0709de6865d3f49374ab6bb274a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/memory_allocations.dart", "hash": "c7c757e0bcbf3ae68b5c4a97007ec0b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/predicate/character.dart", "hash": "c1d88c6f9a0dbed4be35c285ffac4da6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/color/color_uint2.dart", "hash": "a5208ad62885a52102f29c6dbc98f01a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/image_provider.dart", "hash": "25b96e83b1368abc11d4aeae19e9f398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/widgets/layout/layout.dart", "hash": "245c681fa178a9f1c815e9a58eb2aef6"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/circle_avatar.dart", "hash": "3ad691d7f4e0dfc9bac177f56b288925"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/widgets/inherited_theme.dart", "hash": "7ebcf3ce26dea573af17627d822e9759"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/util/random.dart", "hash": "4ea17364c63a20be1bd949e9dccd826c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/encoder.dart", "hash": "90ee28167e6f72136356dd11183ed02e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/filter/emboss.dart", "hash": "8abfebe7ff4c36c34561b98531d3269d"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/bitfield.dart", "hash": "d33374c0857b9ee8927c22a5d269de9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/color/const_color_uint8.dart", "hash": "6d995268c2f76eca401c77c2569c0d60"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE", "hash": "3b954371d922e30c595d3f72f54bb6e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/gzip_encoder.dart", "hash": "1dde9259c663a0918f98357af72267a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib/src/service_status.dart", "hash": "5072fb1450640d8f46605ff67dafa147"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/exr/exr_zip_compressor.dart", "hash": "b99ae845bea75c29d80bab6d93924475"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/parser/utils/resolvable.dart", "hash": "f7329cc0811af555900320e49bd9686f"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/gestures/tap.dart", "hash": "2d638931b01747be8315be89cd473caa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/lib/src/xml/utils/character_data_parser.dart", "hash": "a3044567a5c6d8b0e52367af1a23d5e1"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/animation/animations.dart", "hash": "57d74766f36a3d72789bc7466ae44dba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/sqflite_debug.dart", "hash": "a2cdec29e909752629150b24b9b18407"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/src/expression/utils.dart", "hash": "8608f71f077e370ee14d37c711e6580e"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/icons.dart", "hash": "32b222420709e8e40d12f6ea9fc0041e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/zlib/_zlib_encoder_web.dart", "hash": "35048df4754d9702c3b749726a30a723"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib/src/generated/top_level.dart", "hash": "15439eaa12b927b0e9a42b9d168e3371"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/lib/src/transformers/backpressure/throttle.dart", "hash": "12faaaa2952e6917c271f5dbe9cd6bab"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/choice_chip.dart", "hash": "3cd5a71cfa881a4d3d6325d6b2c6d902"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib/scheme/scheme_monochrome.dart", "hash": "66272a6751b167051ba879724cfe5749"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/lib/src/internal/constants.dart", "hash": "03130eb6bf4721205710e16f4b77fcea"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/snack_bar.dart", "hash": "5c5a8f737a2cec1d969f4a9f8dc80a8d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/lib/src/codecs/bzip2/bzip2.dart", "hash": "71965b290e7074ee69604217832a69aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/win32_wrappers.dart", "hash": "af7270fd3861278053b1c45a7b66ece3"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/clipboard.dart", "hash": "61137458bbcab0dfb643d5d50a5ae80f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/lib/src/algorithms.dart", "hash": "0976264b99a1702a5d74e9acb841b775"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/.dart_tool/package_config_subset", "hash": "d752037828e418116f4bff4668a7089f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file.dart", "hash": "58edba46526a108c44da7a0d3ef3a6aa"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/foundation/change_notifier.dart", "hash": "ce666dc6b4d730d3cb07e6bfc64a8825"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/lib/widgets/vertical_zoom_slider.dart", "hash": "b81482b19e664976acd096f2410c2187"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/lib/src/formats/decoder.dart", "hash": "ba2600e9c8969508912c4d591981d1e9"}, {"path": "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/NativeAssetsManifest.json", "hash": "f3a664e105b4f792c6c7fe4e4d22c398"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/memory/memory_directory.dart", "hash": "96ef4798e4cf4560148762dd71bd180a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/services/flutter_version.dart", "hash": "ad5b018b42f4cfaf02739e10a48c3ca3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/petitparser.dart", "hash": "6cb32004f228090f1200484076254c7a"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/painting/gradient.dart", "hash": "2bc2f148be8fffe5f3a6a53fe8bc8333"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/shadows.dart", "hash": "36fc598c656490ab430ca1be5fb909e8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/states/states.dart", "hash": "83d9a6ac561d2717c23227c0cecf43b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib/expression.dart", "hash": "79503c7448238b77502c169788e26dbf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/lib/src/orchestrator/states/video_camera_recording_state.dart", "hash": "cd510a6b54111140bc9561c6635220aa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/factory_mixin.dart", "hash": "47258dc751a1217744986101e934f62c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/lib/src/batch.dart", "hash": "d88008fc349dd84def0654263c6d16be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/lib/cached_network_image.dart", "hash": "dd2d618db009ed3aa82488ca3b0e1261"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/lib/octo_image.dart", "hash": "66d6d10e44ad1e696a8e632a5c4883d2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib/messages.g.dart", "hash": "3f45d05cfb9a45bf524af2fa9e8fb6e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/lib/src/parsed_path.dart", "hash": "cb454929d7810d3ee5aa5fc28283d3fd"}, {"path": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/segmented_button_theme.dart", "hash": "b815d11a718e0a4d6dec5341e2af4c02"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}]}