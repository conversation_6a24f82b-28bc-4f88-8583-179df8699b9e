{"inputs": ["/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter_tools/lib/src/build_system/targets/native_assets.dart", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/.dart_tool/package_config_subset"], "outputs": ["/Users/<USER>/Desktop/apps/pailide/watermark_camera/.dart_tool/flutter_build/d4d4db9379df09af04f05ad89c774e35/native_assets.json", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/.dart_tool/flutter_build/d4d4db9379df09af04f05ad89c774e35/native_assets.json"]}