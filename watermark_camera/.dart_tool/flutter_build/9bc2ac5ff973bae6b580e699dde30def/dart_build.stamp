{"inputs": ["/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter_tools/lib/src/build_system/targets/native_assets.dart", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/.dart_tool/package_config_subset"], "outputs": ["/Users/<USER>/Desktop/apps/pailide/watermark_camera/.dart_tool/flutter_build/9bc2ac5ff973bae6b580e699dde30def/dart_build_result.json", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/.dart_tool/flutter_build/9bc2ac5ff973bae6b580e699dde30def/dart_build_result.json"]}