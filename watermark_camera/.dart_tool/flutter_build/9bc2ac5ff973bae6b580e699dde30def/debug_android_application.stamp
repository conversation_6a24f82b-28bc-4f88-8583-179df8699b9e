{"inputs": ["/Users/<USER>/Desktop/apps/pailide/watermark_camera/.dart_tool/flutter_build/9bc2ac5ff973bae6b580e699dde30def/app.dill", "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter_tools/lib/src/build_system/targets/icon_tree_shaker.dart", "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/bin/cache/engine.stamp", "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/bin/cache/engine.stamp", "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/bin/cache/engine.stamp", "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/bin/cache/engine.stamp", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/pubspec.yaml", "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/assets/icons/1_1.png", "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/assets/icons/16_9.png", "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/assets/icons/4_3.png", "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/assets/icons/expanded.png", "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/assets/icons/minimized.png", "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf", "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/.dart_tool/flutter_build/9bc2ac5ff973bae6b580e699dde30def/native_assets.json", "/Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/carousel_slider-5.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/colorfilter_generator-0.0.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/gallery_saver_plus-3.2.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/matrix2d-1.0.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/LICENSE", "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/bin/cache/pkg/sky_engine/LICENSE", "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter/LICENSE"], "outputs": ["/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/vm_snapshot_data", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/isolate_snapshot_data", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/kernel_blob.bin", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/packages/camerawesome/assets/icons/1_1.png", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/packages/camerawesome/assets/icons/16_9.png", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/packages/camerawesome/assets/icons/4_3.png", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/packages/camerawesome/assets/icons/expanded.png", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/packages/camerawesome/assets/icons/minimized.png", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/fonts/MaterialIcons-Regular.otf", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/shaders/ink_sparkle.frag", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.json", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.bin", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/FontManifest.json", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/NOTICES.Z", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/NativeAssetsManifest.json"]}