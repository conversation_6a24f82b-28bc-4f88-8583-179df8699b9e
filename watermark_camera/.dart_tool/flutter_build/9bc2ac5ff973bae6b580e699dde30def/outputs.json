["/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/vm_snapshot_data", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/isolate_snapshot_data", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/kernel_blob.bin", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/packages/camerawesome/assets/icons/1_1.png", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/packages/camerawesome/assets/icons/16_9.png", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/packages/camerawesome/assets/icons/4_3.png", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/packages/camerawesome/assets/icons/expanded.png", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/packages/camerawesome/assets/icons/minimized.png", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/fonts/MaterialIcons-Regular.otf", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/shaders/ink_sparkle.frag", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.json", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/AssetManifest.bin", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/FontManifest.json", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/NOTICES.Z", "/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/app/intermediates/flutter/debug/flutter_assets/NativeAssetsManifest.json"]