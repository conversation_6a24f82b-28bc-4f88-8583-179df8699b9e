{"configVersion": 2, "packages": [{"name": "archive", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/archive-4.0.7", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "boolean_selector", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "cached_network_image", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cached_network_image_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "cached_network_image_web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "camerawesome", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/camerawesome-2.5.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "carousel_slider", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/carousel_slider-5.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "characters", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "clock", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "collection", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "colorfilter_generator", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/colorfilter_generator-0.0.8", "packageUri": "lib/", "languageVersion": "2.15"}, {"name": "cross_file", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "crypto", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cupertino_icons", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "fake_async", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "ffi", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "file", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "fixnum", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter", "rootUri": "file:///opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_cache_manager", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter_lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_test", "rootUri": "file:///opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_web_plugins", "rootUri": "file:///opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "gallery_saver_plus", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/gallery_saver_plus-3.2.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "http", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "http_parser", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "image", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/image-4.5.4", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "leak_tracker", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "lints", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "matcher", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "material_color_utilities", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "matrix2d", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/matrix2d-1.0.4", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "meta", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "nested", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "octo_image", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "path_provider_foundation", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider_linux", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "permission_handler", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_apple", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "permission_handler_html", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "permission_handler_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "permission_handler_windows", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "petitparser", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "photo_manager", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1", "packageUri": "lib/", "languageVersion": "2.13"}, {"name": "platform", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "plugin_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "posix", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/posix-6.0.3", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "provider", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "rxdart", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.28.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sky_engine", "rootUri": "file:///opt/homebrew/Caskroom/flutter/3.32.4/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "source_span", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "sprintf", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "sqflite", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_android", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_common", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.6", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "sqflite_darwin", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "sqflite_platform_interface", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "stack_trace", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "stream_channel", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "string_scanner", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "synchronized", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0", "packageUri": "lib/", "languageVersion": "3.8"}, {"name": "term_glyph", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "test_api", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "typed_data", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "uuid", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "vector_math", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "vm_service", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "web", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "xdg_directories", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "xml", "rootUri": "file:///Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "watermark_camera", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.8"}], "generator": "pub", "generatorVersion": "3.8.1", "flutterRoot": "file:///opt/homebrew/Caskroom/flutter/3.32.4/flutter", "flutterVersion": "3.32.4", "pubCache": "file:///Users/<USER>/.pub-cache"}