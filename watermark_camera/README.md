# 水印相机 (Watermark Camera)

一个功能强大的Flutter水印相机应用，支持实时水印预览、多种水印样式和完整的相册管理功能。

## 功能特色

### 📸 相机功能
- **实时相机预览** - 使用CameraAwesome插件提供高质量相机体验
- **前后摄像头切换** - 一键切换前置和后置摄像头
- **闪光灯控制** - 支持自动、开启、关闭三种闪光灯模式
- **自动对焦和缩放** - 智能对焦和手势缩放支持
- **高清拍照** - 支持高分辨率照片拍摄

### 🎨 水印功能
- **13种预设水印** - 精选13个高质量水印样式
- **实时预览** - 拍照前即可看到水印效果
- **多水印支持** - 同时添加多个水印到同一张照片
- **自由拖拽** - 手指拖拽调整水印位置
- **缩放旋转** - 手势控制水印大小和角度
- **透明度调节** - 精确控制水印透明度
- **一键复制** - 快速复制已有水印
- **边界限制** - 智能边界检测防止水印超出画面

### 📱 交互体验
- **直观的控制面板** - 滑块控制透明度、大小、旋转角度
- **选中状态指示** - 清晰的视觉反馈显示当前选中的水印
- **手势操作** - 支持拖拽、缩放、旋转等多种手势
- **实时数值显示** - 实时显示当前调整参数的具体数值

### 🖼️ 相册管理
- **智能相册** - 自动管理应用拍摄的照片
- **网格视图** - 清晰的3列网格布局展示照片
- **多选删除** - 支持批量选择和删除照片
- **照片预览** - 点击查看照片详细信息
- **文件信息** - 显示照片尺寸、文件大小等详细信息
- **系统相册同步** - 照片自动保存到系统相册

## 技术架构

### 核心依赖
- **Flutter** - 跨平台UI框架
- **CameraAwesome 2.5.0** - 专业相机功能
- **Provider** - 状态管理
- **Image** - 图像处理和合成
- **CachedNetworkImage** - 网络图像缓存
- **PhotoManager** - 相册管理
- **GallerySaverPlus** - 照片保存
- **PermissionHandler** - 权限管理

### 架构设计
```
lib/
├── models/          # 数据模型
│   ├── watermark_model.dart
│   └── photo_model.dart
├── providers/       # 状态管理
│   ├── camera_provider.dart
│   ├── watermark_provider.dart
│   └── gallery_provider.dart
├── services/        # 业务服务
│   ├── camera_service.dart
│   └── image_service.dart
├── widgets/         # UI组件
│   ├── draggable_watermark.dart
│   ├── watermark_control_panel.dart
│   └── watermark_selector.dart
├── screens/         # 页面
│   ├── camera_screen.dart
│   └── gallery_screen.dart
├── utils/           # 工具类
│   ├── constants.dart
│   └── permissions.dart
└── main.dart        # 应用入口
```

## 安装和运行

### 环境要求
- Flutter SDK >= 3.0.0
- Dart SDK >= 3.0.0
- Android minSdkVersion 21
- iOS 11.0+

### 安装步骤
1. 克隆项目
```bash
git clone <repository-url>
cd watermark_camera
```

2. 安装依赖
```bash
flutter pub get
```

3. 运行应用
```bash
flutter run
```

### 权限配置
应用需要以下权限：
- **相机权限** - 拍照功能
- **麦克风权限** - 录制视频（如需要）
- **存储权限** - 保存照片到相册
- **网络权限** - 下载水印图片

权限已在以下文件中配置：
- `android/app/src/main/AndroidManifest.xml`
- `ios/Runner/Info.plist`

## 使用说明

### 基本操作
1. **启动应用** - 自动请求必要权限
2. **选择水印** - 点击左上角水印按钮选择喜欢的水印
3. **调整水印** - 拖拽移动位置，使用控制面板调整大小、透明度、旋转角度
4. **拍照** - 点击底部拍照按钮
5. **查看照片** - 点击左下角相册按钮查看已拍摄的照片

### 高级功能
- **多水印** - 可以同时添加多个不同的水印
- **水印复制** - 长按水印或使用控制面板复制按钮
- **批量删除** - 在相册中长按进入选择模式，批量删除照片
- **重置水印** - 使用控制面板重置按钮恢复水印默认状态

## 开发和测试

### 运行测试
```bash
flutter test
```

### 构建发布版本
```bash
# Android
flutter build apk --release

# iOS
flutter build ios --release
```

## 性能优化

- **图像缓存** - 网络水印图片自动缓存，减少重复下载
- **内存管理** - 智能图像压缩和内存释放
- **异步处理** - 图像合成和文件操作使用异步处理
- **懒加载** - 相册照片按需加载，支持分页

## 故障排除

### 常见问题
1. **权限被拒绝** - 检查系统设置中的应用权限
2. **相机无法启动** - 确保设备支持相机功能且未被其他应用占用
3. **水印加载失败** - 检查网络连接，水印图片需要网络下载
4. **照片保存失败** - 确保存储权限已授予且存储空间充足

### 调试模式
在开发模式下，应用会输出详细的日志信息，帮助定位问题。

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目采用MIT许可证。详见LICENSE文件。
