import 'package:flutter/material.dart';
import 'package:camerawesome/camerawesome_plugin.dart';

// 相机状态管理
class CameraProvider extends ChangeNotifier {
  // 相机状态
  CameraState? _cameraState;
  bool _isInitialized = false;
  bool _isRecording = false;

  // 相机设置
  Sensor _currentSensor = Sensor.position(SensorPosition.back);
  FlashMode _flashMode = FlashMode.auto;
  double _zoomLevel = 1.0; // 默认缩放倍率保持1.0
  double _exposureLevel = 0.0;
  bool _isFrontCameraMirrored = true;

  // 错误状态
  String? _errorMessage;

  // Getters
  CameraState? get cameraState => _cameraState;
  bool get isInitialized => _isInitialized;
  bool get isRecording => _isRecording;
  Sensor get currentSensor => _currentSensor;
  FlashMode get flashMode => _flashMode;
  double get zoomLevel => _zoomLevel;
  double get exposureLevel => _exposureLevel;
  bool get isFrontCameraMirrored => _isFrontCameraMirrored;
  String? get errorMessage => _errorMessage;

  // 是否为前置摄像头
  bool get isFrontCamera => _currentSensor.position == SensorPosition.front;

  // 是否为后置摄像头
  bool get isBackCamera => _currentSensor.position == SensorPosition.back;

  // 初始化相机
  void initializeCamera(CameraState state) {
    _cameraState = state;
    _isInitialized = true;
    _errorMessage = null;

    // 设置初始缩放级别为1.0
    _zoomLevel = 1.0;

    notifyListeners();
  }

  // 设置错误信息
  void setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  // 清除错误信息
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // 切换前后摄像头
  Future<void> switchCamera() async {
    if (_cameraState == null || !_isInitialized) {
      setError('相机未初始化');
      return;
    }

    try {
      await _cameraState!.switchCameraSensor();

      // 切换当前传感器状态
      _currentSensor = _currentSensor.position == SensorPosition.back
          ? Sensor.position(SensorPosition.front)
          : Sensor.position(SensorPosition.back);

      // 重置缩放级别为1.0
      _zoomLevel = 1.0;

      // 应用新的缩放级别
      await _cameraState!.sensorConfig.setZoom(_zoomLevel);

      notifyListeners();
    } catch (e) {
      setError('切换摄像头失败: $e');
    }
  }

  // 设置闪光灯模式
  Future<void> setFlashMode(FlashMode mode) async {
    if (_cameraState == null || !_isInitialized) {
      setError('相机未初始化');
      return;
    }

    try {
      await _cameraState!.sensorConfig.setFlashMode(mode);
      _flashMode = mode;
      notifyListeners();
    } catch (e) {
      setError('设置闪光灯失败: $e');
    }
  }

  // 切换闪光灯模式
  Future<void> toggleFlashMode() async {
    FlashMode newMode;
    switch (_flashMode) {
      case FlashMode.none:
        newMode = FlashMode.on;
        break;
      case FlashMode.on:
        newMode = FlashMode.auto;
        break;
      case FlashMode.auto:
        newMode = FlashMode.none;
        break;
      default:
        newMode = FlashMode.auto;
    }
    await setFlashMode(newMode);
  }

  // 设置缩放级别
  Future<void> setZoomLevel(double zoom) async {
    if (_cameraState == null || !_isInitialized) {
      setError('相机未初始化');
      return;
    }

    try {
      // 限制缩放范围
      final clampedZoom = zoom.clamp(1.0, 8.0);
      await _cameraState!.sensorConfig.setZoom(clampedZoom);
      _zoomLevel = clampedZoom;
      notifyListeners();
    } catch (e) {
      setError('设置缩放失败: $e');
    }
  }

  // 设置曝光级别
  Future<void> setExposureLevel(double exposure) async {
    if (_cameraState == null) return;

    try {
      // 限制曝光范围
      final clampedExposure = exposure.clamp(-2.0, 2.0);
      await _cameraState!.sensorConfig.setBrightness(clampedExposure);
      _exposureLevel = clampedExposure;
      notifyListeners();
    } catch (e) {
      setError('设置曝光失败: $e');
    }
  }

  // 设置前置摄像头镜像
  Future<void> setFrontCameraMirrored(bool mirrored) async {
    if (_cameraState == null) return;

    try {
      // await _cameraState!.sensorConfig.setMirrorFrontCamera(mirrored); // API已更改
      _isFrontCameraMirrored = mirrored;
      notifyListeners();
    } catch (e) {
      setError('设置镜像失败: $e');
    }
  }

  // 拍照
  Future<void> takePhoto() async {
    if (_cameraState == null || !_isInitialized) {
      setError('相机未初始化');
      return;
    }

    try {
      await _cameraState!.when(
        onPhotoMode: (photoState) => photoState.takePhoto(),
        onVideoMode: (videoState) {
          // 如果在视频模式，先切换到照片模式
          throw Exception('当前在视频模式，无法拍照');
        },
        onVideoRecordingMode: (videoRecordingState) {
          // 录制中不能拍照
          throw Exception('录制视频时无法拍照');
        },
      );
    } catch (e) {
      setError('拍照失败: $e');
    }
  }

  // 开始录制视频
  Future<void> startVideoRecording() async {
    if (_cameraState == null || _isRecording) return;

    try {
      await _cameraState!.when(
        onVideoMode: (videoState) => videoState.startRecording(),
        onPhotoMode: (photoState) {
          // 如果在照片模式，先切换到视频模式
          // return photoState.switchToVideoMode(); // API已更改
          throw Exception('当前在照片模式，无法录制视频');
        },
        onVideoRecordingMode: (videoRecordingState) {
          // 已在录制中
          return;
        },
      );
      _isRecording = true;
      notifyListeners();
    } catch (e) {
      setError('开始录制失败: $e');
    }
  }

  // 停止录制视频
  Future<void> stopVideoRecording() async {
    if (_cameraState == null || !_isRecording) return;

    try {
      await _cameraState!.when(
        onVideoRecordingMode: (videoRecordingState) =>
            videoRecordingState.stopRecording(),
        onVideoMode: (videoState) {
          // 未在录制中
          return;
        },
        onPhotoMode: (photoState) {
          // 不在视频模式
          return;
        },
      );
      _isRecording = false;
      notifyListeners();
    } catch (e) {
      setError('停止录制失败: $e');
    }
  }

  // 重置相机设置
  void resetSettings() {
    _zoomLevel = 1.0;
    _exposureLevel = 0.0;
    _flashMode = FlashMode.auto;
    _isFrontCameraMirrored = true;
    notifyListeners();
  }

  @override
  void dispose() {
    _cameraState = null;
    super.dispose();
  }
}
