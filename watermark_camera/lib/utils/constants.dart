import 'package:flutter/material.dart';

// 常量定义
class AppConstants {
  // 水印URL列表
  static const List<String> watermarkUrls = [
    'https://oss-hel.vup.tools/img/123-456-789.png',
    'https://oss-hel.vup.tools/img/987-654-321.png',
    'https://oss-hel.vup.tools/img/ROG12月透卡v2.png',
    'https://oss-hel.vup.tools/img/啵奇1月透卡 (1).png',
    'https://oss-hel.vup.tools/img/啵奇1月透卡.png',
    'https://oss-hel.vup.tools/img/啵奇_透卡.png',
    'https://oss-hel.vup.tools/img/月隐空夜透卡 (1).png',
    'https://oss-hel.vup.tools/img/月隐空夜透卡.png',
    'https://oss-hel.vup.tools/img/舰长-亚克力透卡.png',
    'https://oss-hel.vup.tools/img/舰长-透卡.png',
    'https://oss-hel.vup.tools/img/透卡A.png',
    'https://oss-hel.vup.tools/img/透卡.png',
    'https://oss-hel.vup.tools/img/鼠鼠透卡.png',
  ];

  // 应用设置
  static const String appName = '透卡相机';
  static const String appVersion = '1.0.0';

  // 相机设置
  static const double defaultZoom = 1.0;
  static const double minZoom = 1.0;
  static const double maxZoom = 8.0;

  // 水印设置
  static const double defaultWatermarkScale = 0.3;
  static const double minWatermarkScale = 0.1;
  static const double maxWatermarkScale = 1.0;
  static const double defaultWatermarkOpacity = 0.8;
  static const double defaultWatermarkSize = 100.0;

  // 文件路径
  static const String photoDirectory = 'WatermarkCamera';
  static const String tempDirectory = 'temp';

  // UI设置
  static const double buttonSize = 60.0;
  static const double smallButtonSize = 40.0;
  static const double iconSize = 24.0;
  static const double borderRadius = 12.0;

  // 动画时长
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration shortAnimationDuration = Duration(milliseconds: 150);
}

// 颜色常量
class AppColors {
  static const primaryColor = Color(0xFF2196F3);
  static const secondaryColor = Color(0xFF03DAC6);
  static const backgroundColor = Color(0xFF121212);
  static const surfaceColor = Color(0xFF1E1E1E);
  static const errorColor = Color(0xFFCF6679);

  // 别名
  static const primary = primaryColor;
  static const accent = secondaryColor;

  // 半透明颜色
  static const blackTransparent = Color(0x80000000);
  static const whiteTransparent = Color(0x80FFFFFF);

  // 按钮颜色
  static const buttonColor = Color(0xFF333333);
  static const buttonActiveColor = Color(0xFF555555);
}
