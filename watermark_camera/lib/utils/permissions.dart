import 'package:permission_handler/permission_handler.dart';
import 'package:flutter/material.dart';

// 权限处理工具类
class PermissionUtils {
  // 检查相机权限
  static Future<bool> checkCameraPermission() async {
    final status = await Permission.camera.status;
    return status.isGranted;
  }

  // 请求相机权限
  static Future<bool> requestCameraPermission() async {
    final status = await Permission.camera.request();
    return status.isGranted;
  }

  // 检查麦克风权限
  static Future<bool> checkMicrophonePermission() async {
    final status = await Permission.microphone.status;
    return status.isGranted;
  }

  // 请求麦克风权限
  static Future<bool> requestMicrophonePermission() async {
    final status = await Permission.microphone.request();
    return status.isGranted;
  }

  // 检查存储权限
  static Future<bool> checkStoragePermission() async {
    final status = await Permission.storage.status;
    return status.isGranted;
  }

  // 请求存储权限
  static Future<bool> requestStoragePermission() async {
    final status = await Permission.storage.request();
    return status.isGranted;
  }

  // 检查相册权限
  static Future<bool> checkPhotosPermission() async {
    final status = await Permission.photos.status;
    return status.isGranted;
  }

  // 请求相册权限
  static Future<bool> requestPhotosPermission() async {
    final status = await Permission.photos.request();
    return status.isGranted;
  }

  // 检查位置权限
  static Future<bool> checkLocationPermission() async {
    final status = await Permission.locationWhenInUse.status;
    return status.isGranted;
  }

  // 请求位置权限
  static Future<bool> requestLocationPermission() async {
    final status = await Permission.locationWhenInUse.request();
    return status.isGranted;
  }

  // 检查所有必需权限
  static Future<Map<String, bool>> checkAllPermissions() async {
    return {
      'camera': await checkCameraPermission(),
      'microphone': await checkMicrophonePermission(),
      'storage': await checkStoragePermission(),
      'photos': await checkPhotosPermission(),
      'location': await checkLocationPermission(),
    };
  }

  // 请求所有必需权限
  static Future<Map<String, bool>> requestAllPermissions() async {
    final results = <String, bool>{};

    results['camera'] = await requestCameraPermission();
    results['microphone'] = await requestMicrophonePermission();
    results['storage'] = await requestStoragePermission();
    results['photos'] = await requestPhotosPermission();
    results['location'] = await requestLocationPermission();

    return results;
  }

  // 检查权限是否被永久拒绝
  static Future<bool> isPermissionPermanentlyDenied(
    Permission permission,
  ) async {
    final status = await permission.status;
    return status.isPermanentlyDenied;
  }

  // 显示权限设置对话框
  static Future<void> showPermissionDialog(
    BuildContext context,
    String title,
    String message,
  ) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: <Widget>[
            TextButton(
              child: const Text('取消'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text('去设置'),
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
            ),
          ],
        );
      },
    );
  }

  // 显示相机权限对话框
  static Future<void> showCameraPermissionDialog(BuildContext context) async {
    await showPermissionDialog(
      context,
      '需要相机权限',
      '此应用需要访问相机来拍摄照片。请在设置中允许相机权限。',
    );
  }

  // 显示存储权限对话框
  static Future<void> showStoragePermissionDialog(BuildContext context) async {
    await showPermissionDialog(
      context,
      '需要存储权限',
      '此应用需要访问存储来保存照片。请在设置中允许存储权限。',
    );
  }

  // 显示相册权限对话框
  static Future<void> showPhotosPermissionDialog(BuildContext context) async {
    await showPermissionDialog(
      context,
      '需要相册权限',
      '此应用需要访问相册来管理照片。请在设置中允许相册权限。',
    );
  }
}
