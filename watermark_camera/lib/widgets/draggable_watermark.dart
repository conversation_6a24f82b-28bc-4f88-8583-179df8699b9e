import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import '../models/watermark_model.dart';
import '../providers/watermark_provider.dart';
import '../utils/constants.dart';

// 可拖拽水印组件
class DraggableWatermark extends StatefulWidget {
  final WatermarkModel watermark;
  final Size containerSize;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;

  const DraggableWatermark({
    super.key,
    required this.watermark,
    required this.containerSize,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
  });

  @override
  State<DraggableWatermark> createState() => _DraggableWatermarkState();
}

class _DraggableWatermarkState extends State<DraggableWatermark> {
  late Offset _position;
  late double _scale;
  late double _rotation;

  @override
  void initState() {
    super.initState();
    _position = widget.watermark.position;
    _scale = widget.watermark.scale;
    _rotation = _normalizeRotation(widget.watermark.rotation);
  }

  // 标准化旋转值到-π到π之间
  double _normalizeRotation(double rotation) {
    double normalized = rotation % (2 * 3.14159);
    if (normalized > 3.14159) normalized -= 2 * 3.14159;
    if (normalized < -3.14159) normalized += 2 * 3.14159;
    return normalized;
  }

  @override
  void didUpdateWidget(DraggableWatermark oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.watermark != widget.watermark) {
      _position = widget.watermark.position;
      _scale = widget.watermark.scale;
      _rotation = _normalizeRotation(widget.watermark.rotation);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: _position.dx,
      top: _position.dy,
      child: GestureDetector(
        onTap: () {
          widget.onTap?.call();
          // 选中当前水印
          context.read<WatermarkProvider>().selectActiveWatermark(
            widget.watermark.id,
          );
        },
        onDoubleTap: widget.onDoubleTap,
        onLongPress: widget.onLongPress,
        onScaleStart: (details) {
          // 选中当前水印
          context.read<WatermarkProvider>().selectActiveWatermark(
            widget.watermark.id,
          );
        },
        onScaleUpdate: (details) {
          setState(() {
            // 处理拖拽（当scale接近1.0时）
            if ((details.scale - 1.0).abs() < 0.1) {
              _position += details.focalPointDelta;

              // 限制在容器范围内
              _position = Offset(
                _position.dx.clamp(0, widget.containerSize.width - 100),
                _position.dy.clamp(0, widget.containerSize.height - 100),
              );
            }

            // 更新缩放
            _scale = (_scale * details.scale).clamp(
              AppConstants.minWatermarkScale,
              AppConstants.maxWatermarkScale,
            );

            // 更新旋转（限制在-π到π之间）
            _rotation = _normalizeRotation(_rotation + details.rotation);
          });

          // 更新Provider中的变换
          context.read<WatermarkProvider>().updateWatermarkPosition(
            widget.watermark.id,
            _position,
          );
          context.read<WatermarkProvider>().updateWatermarkScale(
            widget.watermark.id,
            _scale,
          );
          context.read<WatermarkProvider>().updateWatermarkRotation(
            widget.watermark.id,
            _rotation,
          );
        },
        onScaleEnd: (details) {
          // 缩放结束
        },
        child: Transform.scale(
          scale: _scale,
          child: Transform.rotate(
            angle: _rotation,
            child: Container(
              decoration: BoxDecoration(
                border: widget.watermark.isSelected
                    ? Border.all(color: AppColors.primary, width: 2.0)
                    : null,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Stack(
                children: [
                  // 水印图像
                  Opacity(
                    opacity: widget.watermark.opacity,
                    child: CachedNetworkImage(
                      imageUrl: widget.watermark.url,
                      width: AppConstants.defaultWatermarkSize,
                      height: AppConstants.defaultWatermarkSize,
                      fit: BoxFit.contain,
                      placeholder: (context, url) => Container(
                        width: AppConstants.defaultWatermarkSize,
                        height: AppConstants.defaultWatermarkSize,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Center(
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        width: AppConstants.defaultWatermarkSize,
                        height: AppConstants.defaultWatermarkSize,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Icon(Icons.error, color: Colors.red),
                      ),
                    ),
                  ),

                  // 选中状态的控制点
                  if (widget.watermark.isSelected) ...[
                    // 删除按钮
                    Positioned(
                      top: -8,
                      right: -8,
                      child: GestureDetector(
                        onTap: () {
                          context.read<WatermarkProvider>().removeWatermark(
                            widget.watermark.id,
                          );
                        },
                        child: Container(
                          width: 24,
                          height: 24,
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ),
                    ),

                    // 复制按钮
                    Positioned(
                      top: -8,
                      left: -8,
                      child: GestureDetector(
                        onTap: () {
                          context.read<WatermarkProvider>().duplicateWatermark(
                            widget.watermark.id,
                          );
                        },
                        child: Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.copy,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ),
                    ),

                    // 缩放/旋转控制点
                    Positioned(
                      bottom: -8,
                      right: -8,
                      child: GestureDetector(
                        onPanUpdate: (details) {
                          // 计算缩放和旋转
                          final center = Offset(
                            AppConstants.defaultWatermarkSize / 2,
                            AppConstants.defaultWatermarkSize / 2,
                          );
                          final currentVector = details.localPosition - center;
                          final previousVector =
                              (details.localPosition - details.delta) - center;

                          // 计算缩放变化
                          final currentDistance = currentVector.distance;
                          final previousDistance = previousVector.distance;
                          if (previousDistance > 0) {
                            final scaleChange =
                                currentDistance / previousDistance;
                            final newScale = (_scale * scaleChange).clamp(
                              AppConstants.minWatermarkScale,
                              AppConstants.maxWatermarkScale,
                            );

                            setState(() {
                              _scale = newScale;
                            });

                            context
                                .read<WatermarkProvider>()
                                .updateWatermarkScale(
                                  widget.watermark.id,
                                  _scale,
                                );
                          }

                          // 计算旋转变化
                          final currentAngle = currentVector.direction;
                          final previousAngle = previousVector.direction;
                          final rotationChange = currentAngle - previousAngle;

                          setState(() {
                            _rotation += rotationChange;
                          });

                          context
                              .read<WatermarkProvider>()
                              .updateWatermarkRotation(
                                widget.watermark.id,
                                _rotation,
                              );
                        },
                        child: Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: AppColors.accent,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.open_with,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
