import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import '../providers/watermark_provider.dart';
import '../models/watermark_model.dart';
import '../utils/constants.dart';

// 水印选择器组件
class WatermarkSelector extends StatelessWidget {
  const WatermarkSelector({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<WatermarkProvider>(
      builder: (context, watermarkProvider, child) {
        if (!watermarkProvider.isWatermarkSelectorVisible) {
          return const SizedBox.shrink();
        }

        return Positioned(
          left: 16,
          right: 16,
          bottom: 120,
          child: Container(
            height: 120,
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题栏
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        '选择水印',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Row(
                        children: [
                          // 清除所有水印按钮
                          if (watermarkProvider.hasActiveWatermarks)
                            IconButton(
                              onPressed: () {
                                _showClearAllDialog(context, watermarkProvider);
                              },
                              icon: const Icon(
                                Icons.clear_all,
                                color: Colors.red,
                                size: 20,
                              ),
                            ),

                          // 关闭按钮
                          IconButton(
                            onPressed: () {
                              watermarkProvider.hideWatermarkSelector();
                            },
                            icon: const Icon(
                              Icons.close,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // 水印列表
                Expanded(
                  child: watermarkProvider.isLoading
                      ? const Center(
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : watermarkProvider.errorMessage != null
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(
                                Icons.error,
                                color: Colors.red,
                                size: 32,
                              ),
                              const SizedBox(height: 8),
                              Text(
                                watermarkProvider.errorMessage!,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 8),
                              TextButton(
                                onPressed: () {
                                  watermarkProvider.initializeWatermarks();
                                },
                                child: const Text(
                                  '重试',
                                  style: TextStyle(color: Colors.blue),
                                ),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          scrollDirection: Axis.horizontal,
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount:
                              watermarkProvider.availableWatermarks.length,
                          itemBuilder: (context, index) {
                            final watermark =
                                watermarkProvider.availableWatermarks[index];
                            return _buildWatermarkItem(
                              context,
                              watermark,
                              watermarkProvider,
                            );
                          },
                        ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // 构建水印项目
  Widget _buildWatermarkItem(
    BuildContext context,
    WatermarkModel watermark,
    WatermarkProvider watermarkProvider,
  ) {
    // 检查是否已经添加到活动水印中
    final isActive = watermarkProvider.activeWatermarks.any(
      (w) => w.url == watermark.url,
    );

    return Container(
      width: 80,
      margin: const EdgeInsets.only(right: 12, bottom: 8),
      child: GestureDetector(
        onTap: () {
          watermarkProvider.selectWatermark(watermark);
          // 选择后自动隐藏选择器
          watermarkProvider.hideWatermarkSelector();
        },
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 水印图像
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isActive ? AppColors.primary : Colors.grey[600]!,
                  width: isActive ? 2 : 1,
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(6),
                child: Stack(
                  children: [
                    CachedNetworkImage(
                      imageUrl: watermark.url,
                      width: 50,
                      height: 50,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        width: 50,
                        height: 50,
                        color: Colors.grey[700],
                        child: const Center(
                          child: SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        width: 50,
                        height: 50,
                        color: Colors.grey[700],
                        child: const Icon(
                          Icons.error,
                          color: Colors.red,
                          size: 20,
                        ),
                      ),
                    ),

                    // 已添加标识
                    if (isActive)
                      Positioned(
                        top: 2,
                        right: 2,
                        child: Container(
                          width: 16,
                          height: 16,
                          decoration: BoxDecoration(
                            color: AppColors.primary,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 2),

            // 水印名称
            Flexible(
              child: Text(
                watermark.name,
                style: TextStyle(
                  color: isActive ? AppColors.primary : Colors.white,
                  fontSize: 9,
                  fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 显示清除所有水印的确认对话框
  void _showClearAllDialog(
    BuildContext context,
    WatermarkProvider watermarkProvider,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清除所有水印'),
        content: const Text('确定要清除所有已添加的水印吗？此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              watermarkProvider.clearAllWatermarks();
              Navigator.pop(context);
            },
            child: const Text('确定', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
