import 'package:flutter/material.dart';
import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:provider/provider.dart';
import '../providers/camera_provider.dart';
import '../providers/watermark_provider.dart';
import '../providers/gallery_provider.dart';
import '../services/camera_service.dart';
// import '../services/image_service.dart'; // 暂时不使用
import '../widgets/draggable_watermark.dart';
import '../widgets/watermark_control_panel.dart';
import '../widgets/watermark_selector.dart';
import '../widgets/vertical_zoom_slider.dart';
import '../utils/constants.dart';
import '../utils/permissions.dart';
import 'gallery_screen.dart';

// 相机主界面
class CameraScreen extends StatefulWidget {
  const CameraScreen({super.key});

  @override
  State<CameraScreen> createState() => _CameraScreenState();
}

class _CameraScreenState extends State<CameraScreen> {
  final CameraService _cameraService = CameraService();
  // final ImageService _imageService = ImageService(); // 暂时不使用
  final GlobalKey _previewKey = GlobalKey();

  bool _isInitialized = false;
  Size _previewSize = Size.zero;

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  // 初始化应用
  Future<void> _initializeApp() async {
    try {
      // 检查权限
      final permissions = await PermissionUtils.checkAllPermissions();
      if (!permissions['camera']! || !permissions['storage']!) {
        await _requestPermissions();
      }

      // 初始化水印列表
      context.read<WatermarkProvider>().initializeWatermarks();

      // 初始化相册
      await context.read<GalleryProvider>().initializeGallery();

      setState(() {
        _isInitialized = true;
      });
    } catch (e) {
      _showErrorDialog('初始化失败: $e');
    }
  }

  // 请求权限
  Future<void> _requestPermissions() async {
    final results = await PermissionUtils.requestAllPermissions();

    if (!results['camera']!) {
      await PermissionUtils.showCameraPermissionDialog(context);
      return;
    }

    if (!results['storage']!) {
      await PermissionUtils.showStoragePermissionDialog(context);
      return;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    return Scaffold(
      body: Stack(
        children: [
          // 相机预览
          _buildCameraPreview(),

          // 水印层
          _buildWatermarkLayer(),

          // 控制界面
          _buildControlsOverlay(),

          // 水印控制面板
          _buildWatermarkControlPanel(),

          // 水印选择器
          const WatermarkSelector(),

          // 缩放控制滑块
          _buildZoomControl(),
        ],
      ),
    );
  }

  // 构建相机预览
  Widget _buildCameraPreview() {
    return RepaintBoundary(
      key: _previewKey,
      child: LayoutBuilder(
        builder: (context, constraints) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            // 获取实际的预览尺寸
            final size = Size(constraints.maxWidth, constraints.maxHeight);
            if (_previewSize != size) {
              setState(() {
                _previewSize = size;
              });
            }
          });

          return CameraAwesomeBuilder.custom(
            saveConfig: _cameraService.getSaveConfig(),
            sensorConfig: _cameraService.getSensorConfig(),
            previewFit: CameraPreviewFit.cover,
            onMediaCaptureEvent: _cameraService.createMediaCaptureHandler(
              onPhotoTaken: _onPhotoTaken,
              onVideoRecorded: _onVideoRecorded,
              onError: _onCaptureError,
            ),
            builder: (cameraState, preview) => const SizedBox.shrink(),
          );
        },
      ),
    );
  }

  // 构建水印层
  Widget _buildWatermarkLayer() {
    return Consumer<WatermarkProvider>(
      builder: (context, watermarkProvider, child) {
        if (_previewSize == Size.zero) return const SizedBox.shrink();

        return SizedBox(
          width: _previewSize.width,
          height: _previewSize.height,
          child: Stack(
            children: watermarkProvider.activeWatermarks.map((watermark) {
              return DraggableWatermark(
                key: ValueKey(watermark.id),
                watermark: watermark,
                containerSize: _previewSize,
                onTap: () {
                  // 点击水印时的处理
                },
                onDoubleTap: () {
                  // 双击删除水印
                  watermarkProvider.removeWatermark(watermark.id);
                },
                onLongPress: () {
                  // 长按复制水印
                  watermarkProvider.duplicateWatermark(watermark.id);
                },
              );
            }).toList(),
          ),
        );
      },
    );
  }

  // 构建控制界面
  Widget _buildControlsOverlay() {
    return SafeArea(
      child: Column(
        children: [
          // 顶部控制栏
          _buildTopControls(),

          const Spacer(),

          // 底部控制栏
          _buildBottomControls(),
        ],
      ),
    );
  }

  // 构建顶部控制栏
  Widget _buildTopControls() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 水印按钮
          Consumer<WatermarkProvider>(
            builder: (context, watermarkProvider, child) {
              return IconButton(
                onPressed: () {
                  watermarkProvider.toggleWatermarkSelector();
                },
                icon: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    watermarkProvider.isWatermarkSelectorVisible
                        ? Icons.close
                        : Icons.add_photo_alternate,
                    color: Colors.white,
                  ),
                ),
              );
            },
          ),

          // 闪光灯控制
          Consumer<CameraProvider>(
            builder: (context, cameraProvider, child) {
              return IconButton(
                onPressed: () {
                  cameraProvider.toggleFlashMode();
                },
                icon: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    _getFlashIcon(cameraProvider.flashMode),
                    color: Colors.white,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  // 构建缩放控制
  Widget _buildZoomControl() {
    return Positioned(
      right: 20,
      bottom: 150, // 放在拍摄按钮上方
      child: Consumer<CameraProvider>(
        builder: (context, cameraProvider, child) {
          return VerticalZoomSlider(
            value: cameraProvider.zoomLevel,
            minValue: 1.0,
            maxValue: 8.0,
            height: 150,
            width: 40,
            activeColor: AppColors.primary,
            inactiveColor: Colors.grey,
            thumbColor: Colors.white,
            onChanged: (value) {
              cameraProvider.setZoomLevel(value);
            },
          );
        },
      ),
    );
  }

  // 构建底部控制栏
  Widget _buildBottomControls() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // 相册按钮
          GestureDetector(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const GalleryScreen()),
              );
            },
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(color: Colors.white, width: 2),
              ),
              child: const Icon(
                Icons.photo_library,
                color: Colors.white,
                size: 30,
              ),
            ),
          ),

          // 拍照按钮
          Consumer<CameraProvider>(
            builder: (context, cameraProvider, child) {
              return GestureDetector(
                onTap: () {
                  _takePhoto();
                },
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(40),
                    border: Border.all(color: AppColors.primary, width: 4),
                  ),
                  child: Icon(
                    Icons.camera_alt,
                    color: AppColors.primary,
                    size: 40,
                  ),
                ),
              );
            },
          ),

          // 切换摄像头按钮
          Consumer<CameraProvider>(
            builder: (context, cameraProvider, child) {
              return GestureDetector(
                onTap: () {
                  cameraProvider.switchCamera();
                },
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(30),
                    border: Border.all(color: Colors.white, width: 2),
                  ),
                  child: const Icon(
                    Icons.flip_camera_ios,
                    color: Colors.white,
                    size: 30,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  // 构建水印控制面板
  Widget _buildWatermarkControlPanel() {
    return Consumer<WatermarkProvider>(
      builder: (context, watermarkProvider, child) {
        if (watermarkProvider.selectedWatermark == null) {
          return const SizedBox.shrink();
        }

        return Positioned(
          right: 16,
          top: MediaQuery.of(context).size.height * 0.3,
          child: const WatermarkControlPanel(),
        );
      },
    );
  }

  // 获取闪光灯图标
  IconData _getFlashIcon(FlashMode flashMode) {
    switch (flashMode) {
      case FlashMode.none:
        return Icons.flash_off;
      case FlashMode.on:
        return Icons.flash_on;
      case FlashMode.auto:
        return Icons.flash_auto;
      default:
        return Icons.flash_auto;
    }
  }

  // 拍照处理
  Future<void> _takePhoto() async {
    try {
      final cameraProvider = context.read<CameraProvider>();
      await cameraProvider.takePhoto();
    } catch (e) {
      _showErrorDialog('拍照失败: $e');
    }
  }

  // 照片拍摄完成回调
  void _onPhotoTaken(photoModel) {
    // 添加到相册
    context.read<GalleryProvider>().addPhoto(photoModel);

    // 显示成功提示
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('照片保存成功'), duration: Duration(seconds: 2)),
    );
  }

  // 视频录制完成回调
  void _onVideoRecorded(String videoPath) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('视频保存成功'), duration: Duration(seconds: 2)),
    );
  }

  // 捕获错误回调
  void _onCaptureError(String error) {
    _showErrorDialog(error);
  }

  // 显示错误对话框
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('错误'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
