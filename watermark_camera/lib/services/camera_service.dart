import 'dart:io';
import 'package:flutter/material.dart';
import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';
import '../utils/constants.dart';
import '../models/photo_model.dart';

// 相机服务类
class CameraService {
  static final CameraService _instance = CameraService._internal();
  factory CameraService() => _instance;
  CameraService._internal();

  // 获取保存配置
  SaveConfig getSaveConfig() {
    return SaveConfig.photoAndVideo(
      initialCaptureMode: CaptureMode.photo,
      photoPathBuilder: _buildPhotoPath,
      videoPathBuilder: _buildVideoPath,
    );
  }

  // 构建照片保存路径
  Future<CaptureRequest> _buildPhotoPath(List<Sensor> sensors) async {
    final directory = await getApplicationDocumentsDirectory();
    final photoDir = Directory(
      '${directory.path}/${AppConstants.photoDirectory}',
    );

    // 确保目录存在
    if (!await photoDir.exists()) {
      await photoDir.create(recursive: true);
    }

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final uuid = const Uuid().v4().substring(0, 8);
    final fileName = 'watermark_photo_${timestamp}_$uuid.jpg';
    final filePath = '${photoDir.path}/$fileName';

    if (sensors.length == 1) {
      return SingleCaptureRequest(filePath, sensors.first);
    } else {
      return MultipleCaptureRequest({
        for (final sensor in sensors)
          sensor:
              '${photoDir.path}/${sensor.position == SensorPosition.front ? 'front_' : 'back_'}${timestamp}_$uuid.jpg',
      });
    }
  }

  // 构建视频保存路径
  Future<CaptureRequest> _buildVideoPath(List<Sensor> sensors) async {
    final directory = await getApplicationDocumentsDirectory();
    final videoDir = Directory(
      '${directory.path}/${AppConstants.photoDirectory}',
    );

    // 确保目录存在
    if (!await videoDir.exists()) {
      await videoDir.create(recursive: true);
    }

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final uuid = const Uuid().v4().substring(0, 8);
    final fileName = 'watermark_video_${timestamp}_$uuid.mp4';
    final filePath = '${videoDir.path}/$fileName';

    if (sensors.length == 1) {
      return SingleCaptureRequest(filePath, sensors.first);
    } else {
      return MultipleCaptureRequest({
        for (final sensor in sensors)
          sensor:
              '${videoDir.path}/${sensor.position == SensorPosition.front ? 'front_' : 'back_'}${timestamp}_$uuid.mp4',
      });
    }
  }

  // 获取传感器配置
  SensorConfig getSensorConfig() {
    return SensorConfig.single(
      sensor: Sensor.position(SensorPosition.back),
      flashMode: FlashMode.auto,
      aspectRatio: CameraAspectRatios.ratio_16_9, // 使用16:9获得更高分辨率
      zoom: 1.0,
    );
  }

  // 处理媒体捕获事件回调
  Function(dynamic) createMediaCaptureHandler({
    Function(PhotoModel)? onPhotoTaken,
    Function(String)? onVideoRecorded,
    Function(String)? onError,
  }) {
    return (dynamic event) {
      switch ((event.status, event.isPicture, event.isVideo)) {
        case (MediaCaptureStatus.capturing, true, false):
          debugPrint('正在拍照...');
          break;

        case (MediaCaptureStatus.success, true, false):
          event.captureRequest.when(
            single: (single) async {
              if (single.file != null) {
                debugPrint('照片保存成功: ${single.file!.path}');

                // 创建PhotoModel
                final photoModel = await _createPhotoModelFromFile(
                  single.file!,
                );
                onPhotoTaken?.call(photoModel);
              }
            },
            multiple: (multiple) {
              multiple.fileBySensor.forEach((key, value) {
                if (value != null) {
                  debugPrint('多摄像头照片保存: $key ${value.path}');
                }
              });
            },
          );
          break;

        case (MediaCaptureStatus.failure, true, false):
          debugPrint('拍照失败: ${event.exception}');
          onError?.call('拍照失败: ${event.exception}');
          break;

        case (MediaCaptureStatus.capturing, false, true):
          debugPrint('正在录制视频...');
          break;

        case (MediaCaptureStatus.success, false, true):
          event.captureRequest.when(
            single: (single) {
              if (single.file != null) {
                debugPrint('视频保存成功: ${single.file!.path}');
                onVideoRecorded?.call(single.file!.path);
              }
            },
            multiple: (multiple) {
              multiple.fileBySensor.forEach((key, value) {
                if (value != null) {
                  debugPrint('多摄像头视频保存: $key ${value.path}');
                }
              });
            },
          );
          break;

        case (MediaCaptureStatus.failure, false, true):
          debugPrint('录制视频失败: ${event.exception}');
          onError?.call('录制视频失败: ${event.exception}');
          break;

        default:
          debugPrint('未知媒体事件: $event');
      }
    };
  }

  // 从文件创建PhotoModel
  Future<PhotoModel> _createPhotoModelFromFile(File file) async {
    final stat = await file.stat();
    final fileName = file.path.split('/').last;

    // 这里可以添加获取图片尺寸的逻辑
    // 暂时使用默认值
    const defaultWidth = 1920;
    const defaultHeight = 1080;

    return PhotoModel(
      id: const Uuid().v4(),
      filePath: file.path,
      fileName: fileName,
      createdAt: stat.modified,
      fileSize: stat.size,
      width: defaultWidth,
      height: defaultHeight,
    );
  }

  // 获取相机权限状态
  Future<bool> checkCameraPermission() async {
    try {
      // CamerAwesome会自动处理权限请求
      return true;
    } catch (e) {
      debugPrint('检查相机权限失败: $e');
      return false;
    }
  }

  // 获取可用的传感器列表
  Future<List<Sensor>> getAvailableSensors() async {
    try {
      // 返回常用的传感器配置
      return [
        Sensor.position(SensorPosition.back),
        Sensor.position(SensorPosition.front),
      ];
    } catch (e) {
      debugPrint('获取传感器列表失败: $e');
      return [Sensor.position(SensorPosition.back)];
    }
  }

  // 检查设备是否支持闪光灯
  Future<bool> isFlashAvailable(Sensor sensor) async {
    try {
      // 通常后置摄像头支持闪光灯
      return sensor.position == SensorPosition.back;
    } catch (e) {
      debugPrint('检查闪光灯支持失败: $e');
      return false;
    }
  }

  // 检查设备是否支持缩放
  Future<bool> isZoomAvailable() async {
    try {
      // 大多数现代设备都支持缩放
      return true;
    } catch (e) {
      debugPrint('检查缩放支持失败: $e');
      return false;
    }
  }

  // 获取最大缩放级别
  Future<double> getMaxZoomLevel() async {
    try {
      // 返回默认最大缩放级别
      return AppConstants.maxZoom;
    } catch (e) {
      debugPrint('获取最大缩放级别失败: $e');
      return AppConstants.maxZoom;
    }
  }

  // 清理临时文件
  Future<void> cleanupTempFiles() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final tempDir = Directory(
        '${directory.path}/${AppConstants.tempDirectory}',
      );

      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
      }
    } catch (e) {
      debugPrint('清理临时文件失败: $e');
    }
  }

  // 获取存储空间信息
  Future<Map<String, int>> getStorageInfo() async {
    try {
      // final directory = await getApplicationDocumentsDirectory();
      // final stat = await directory.stat(); // 暂时不使用

      // 这里返回模拟数据，实际实现需要使用平台特定的API
      return {
        'totalSpace': 1024 * 1024 * 1024 * 64, // 64GB
        'freeSpace': 1024 * 1024 * 1024 * 32, // 32GB
        'usedSpace': 1024 * 1024 * 1024 * 32, // 32GB
      };
    } catch (e) {
      debugPrint('获取存储信息失败: $e');
      return {'totalSpace': 0, 'freeSpace': 0, 'usedSpace': 0};
    }
  }
}
