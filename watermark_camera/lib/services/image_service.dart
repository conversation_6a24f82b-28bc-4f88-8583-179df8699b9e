import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:image/image.dart' as img;
import 'package:cached_network_image/cached_network_image.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';
import '../models/watermark_model.dart';
// import '../models/photo_model.dart'; // 暂时不使用
import '../utils/constants.dart';

// 图像处理服务类
class ImageService {
  static final ImageService _instance = ImageService._internal();
  factory ImageService() => _instance;
  ImageService._internal();

  // 合成带水印的图像
  Future<File> compositeImageWithWatermarks({
    required File originalImage,
    required List<WatermarkModel> watermarks,
    required Size previewSize,
    required Size imageSize,
  }) async {
    try {
      // 读取原始图像
      final originalBytes = await originalImage.readAsBytes();
      final originalImg = img.decodeImage(originalBytes);

      if (originalImg == null) {
        throw Exception('无法解码原始图像');
      }

      // 创建合成图像
      final compositeImg = img.Image.from(originalImg);

      // 计算缩放比例
      final scaleX = imageSize.width / previewSize.width;
      final scaleY = imageSize.height / previewSize.height;

      // 添加每个水印
      for (final watermark in watermarks) {
        await _addWatermarkToImage(compositeImg, watermark, scaleX, scaleY);
      }

      // 保存合成图像
      final outputFile = await _saveCompositeImage(compositeImg);
      return outputFile;
    } catch (e) {
      throw Exception('图像合成失败: $e');
    }
  }

  // 添加水印到图像
  Future<void> _addWatermarkToImage(
    img.Image baseImage,
    WatermarkModel watermark,
    double scaleX,
    double scaleY,
  ) async {
    try {
      // 下载水印图像
      final watermarkBytes = await _downloadWatermarkImage(watermark.url);
      final watermarkImg = img.decodeImage(watermarkBytes);

      if (watermarkImg == null) {
        debugPrint('无法解码水印图像: ${watermark.url}');
        return;
      }

      // 计算水印在最终图像中的位置和大小
      final scaledX = (watermark.position.dx * scaleX).round();
      final scaledY = (watermark.position.dy * scaleY).round();
      final scaledWidth = (watermarkImg.width * watermark.scale * scaleX)
          .round();
      final scaledHeight = (watermarkImg.height * watermark.scale * scaleY)
          .round();

      // 调整水印大小
      final resizedWatermark = img.copyResize(
        watermarkImg,
        width: scaledWidth,
        height: scaledHeight,
      );

      // 应用旋转
      final rotatedWatermark = watermark.rotation != 0.0
          ? img.copyRotate(resizedWatermark, angle: watermark.rotation)
          : resizedWatermark;

      // 应用透明度
      final watermarkWithOpacity = _applyOpacity(
        rotatedWatermark,
        watermark.opacity,
      );

      // 合成到基础图像
      img.compositeImage(
        baseImage,
        watermarkWithOpacity,
        dstX: scaledX,
        dstY: scaledY,
      );
    } catch (e) {
      debugPrint('添加水印失败: $e');
    }
  }

  // 下载水印图像
  Future<Uint8List> _downloadWatermarkImage(String url) async {
    try {
      // 使用cached_network_image的缓存机制
      final imageProvider = CachedNetworkImageProvider(url);
      final imageStream = imageProvider.resolve(const ImageConfiguration());

      // 等待图像加载完成
      final completer = Completer<ui.Image>();
      late ImageStreamListener listener;

      listener = ImageStreamListener((ImageInfo info, bool synchronousCall) {
        completer.complete(info.image);
        imageStream.removeListener(listener);
      });

      imageStream.addListener(listener);
      final uiImage = await completer.future;

      // 转换为字节数据
      final byteData = await uiImage.toByteData(format: ui.ImageByteFormat.png);
      return byteData!.buffer.asUint8List();
    } catch (e) {
      throw Exception('下载水印图像失败: $e');
    }
  }

  // 应用透明度
  img.Image _applyOpacity(img.Image image, double opacity) {
    if (opacity >= 1.0) return image;

    // 简化处理：暂时跳过透明度处理，直接返回原图像
    // 在后续版本中可以添加更复杂的透明度处理
    return image;
  }

  // 保存合成图像
  Future<File> _saveCompositeImage(img.Image image) async {
    final directory = await getApplicationDocumentsDirectory();
    final photoDir = Directory(
      '${directory.path}/${AppConstants.photoDirectory}',
    );

    // 确保目录存在
    if (!await photoDir.exists()) {
      await photoDir.create(recursive: true);
    }

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final uuid = const Uuid().v4().substring(0, 8);
    final fileName = 'watermark_composite_${timestamp}_$uuid.jpg';
    final filePath = '${photoDir.path}/$fileName';

    // 编码为JPEG
    final jpegBytes = img.encodeJpg(image, quality: 95);

    // 写入文件
    final file = File(filePath);
    await file.writeAsBytes(jpegBytes);

    return file;
  }

  // 从Widget创建图像
  Future<ui.Image> captureWidgetAsImage(
    GlobalKey key, {
    double pixelRatio = 1.0,
  }) async {
    try {
      final RenderRepaintBoundary boundary =
          key.currentContext!.findRenderObject() as RenderRepaintBoundary;

      final ui.Image image = await boundary.toImage(pixelRatio: pixelRatio);
      return image;
    } catch (e) {
      throw Exception('捕获Widget图像失败: $e');
    }
  }

  // 将ui.Image转换为File
  Future<File> saveUiImageAsFile(
    ui.Image image, {
    String? fileName,
    int quality = 95,
  }) async {
    try {
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final bytes = byteData!.buffer.asUint8List();

      // 如果需要JPEG格式，进行转换
      final img.Image? decodedImage = img.decodePng(bytes);
      if (decodedImage == null) {
        throw Exception('无法解码图像');
      }

      final jpegBytes = img.encodeJpg(decodedImage, quality: quality);

      // 保存文件
      final directory = await getApplicationDocumentsDirectory();
      final photoDir = Directory(
        '${directory.path}/${AppConstants.photoDirectory}',
      );

      if (!await photoDir.exists()) {
        await photoDir.create(recursive: true);
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final uuid = const Uuid().v4().substring(0, 8);
      final finalFileName = fileName ?? 'captured_${timestamp}_$uuid.jpg';
      final filePath = '${photoDir.path}/$finalFileName';

      final file = File(filePath);
      await file.writeAsBytes(jpegBytes);

      return file;
    } catch (e) {
      throw Exception('保存图像文件失败: $e');
    }
  }

  // 获取图像尺寸
  Future<Size> getImageSize(File imageFile) async {
    try {
      final bytes = await imageFile.readAsBytes();
      final image = img.decodeImage(bytes);

      if (image == null) {
        throw Exception('无法解码图像');
      }

      return Size(image.width.toDouble(), image.height.toDouble());
    } catch (e) {
      throw Exception('获取图像尺寸失败: $e');
    }
  }

  // 压缩图像
  Future<File> compressImage(
    File imageFile, {
    int quality = 85,
    int? maxWidth,
    int? maxHeight,
  }) async {
    try {
      final bytes = await imageFile.readAsBytes();
      final image = img.decodeImage(bytes);

      if (image == null) {
        throw Exception('无法解码图像');
      }

      img.Image processedImage = image;

      // 调整尺寸
      if (maxWidth != null || maxHeight != null) {
        processedImage = img.copyResize(
          processedImage,
          width: maxWidth,
          height: maxHeight,
          maintainAspect: true,
        );
      }

      // 压缩
      final compressedBytes = img.encodeJpg(processedImage, quality: quality);

      // 保存压缩后的文件
      final directory = await getApplicationDocumentsDirectory();
      final tempDir = Directory(
        '${directory.path}/${AppConstants.tempDirectory}',
      );

      if (!await tempDir.exists()) {
        await tempDir.create(recursive: true);
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'compressed_$timestamp.jpg';
      final filePath = '${tempDir.path}/$fileName';

      final compressedFile = File(filePath);
      await compressedFile.writeAsBytes(compressedBytes);

      return compressedFile;
    } catch (e) {
      throw Exception('压缩图像失败: $e');
    }
  }

  // 创建缩略图
  Future<File> createThumbnail(
    File imageFile, {
    int size = 200,
    int quality = 80,
  }) async {
    try {
      final bytes = await imageFile.readAsBytes();
      final image = img.decodeImage(bytes);

      if (image == null) {
        throw Exception('无法解码图像');
      }

      // 创建正方形缩略图
      final thumbnail = img.copyResizeCropSquare(image, size: size);
      final thumbnailBytes = img.encodeJpg(thumbnail, quality: quality);

      // 保存缩略图
      final directory = await getApplicationDocumentsDirectory();
      final tempDir = Directory(
        '${directory.path}/${AppConstants.tempDirectory}',
      );

      if (!await tempDir.exists()) {
        await tempDir.create(recursive: true);
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'thumbnail_$timestamp.jpg';
      final filePath = '${tempDir.path}/$fileName';

      final thumbnailFile = File(filePath);
      await thumbnailFile.writeAsBytes(thumbnailBytes);

      return thumbnailFile;
    } catch (e) {
      throw Exception('创建缩略图失败: $e');
    }
  }
}
