// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'package:watermark_camera/main.dart';
import 'package:watermark_camera/providers/camera_provider.dart';
import 'package:watermark_camera/providers/watermark_provider.dart';
import 'package:watermark_camera/providers/gallery_provider.dart';

void main() {
  testWidgets('WatermarkCameraApp smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const WatermarkCameraApp());

    // Verify that the app loads without crashing
    expect(find.byType(MaterialApp), findsOneWidget);

    // Wait for initial frame only (avoid camera initialization timeout)
    await tester.pump();

    // The app should have a MaterialApp widget
    expect(find.byType(MaterialApp), findsOneWidget);
  });

  testWidgets('Provider initialization test', (WidgetTester tester) async {
    // Test that all providers are properly initialized
    await tester.pumpWidget(
      MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (_) => CameraProvider()),
          ChangeNotifierProvider(create: (_) => WatermarkProvider()),
          ChangeNotifierProvider(create: (_) => GalleryProvider()),
        ],
        child: MaterialApp(
          home: Consumer3<CameraProvider, WatermarkProvider, GalleryProvider>(
            builder: (context, camera, watermark, gallery, child) {
              return Scaffold(
                body: Column(
                  children: [
                    Text('Camera: ${camera.isInitialized}'),
                    Text('Watermarks: ${watermark.availableWatermarks.length}'),
                    Text('Photos: ${gallery.photos.length}'),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );

    await tester.pumpAndSettle();

    // Verify providers are accessible
    expect(find.text('Camera: false'), findsOneWidget);
    expect(find.text('Watermarks: 0'), findsOneWidget);
    expect(find.text('Photos: 0'), findsOneWidget);
  });
}
