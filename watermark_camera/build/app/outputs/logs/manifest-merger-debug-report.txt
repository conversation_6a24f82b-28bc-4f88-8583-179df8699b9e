-- Merging decision tree log ---
application
INJECTED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:22:5-53:19
INJECTED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/debug/AndroidManifest.xml
MERGED from [:camerawesome] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/camerawesome/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:5-11:19
MERGED from [:camerawesome] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/camerawesome/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:5-11:19
MERGED from [com.google.android.gms:play-services-location:21.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/566d820d62eceeef77e6fc5ea49a076e/transformed/jetified-play-services-location-21.3.0/AndroidManifest.xml:6:5-20
MERGED from [com.google.android.gms:play-services-location:21.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/566d820d62eceeef77e6fc5ea49a076e/transformed/jetified-play-services-location-21.3.0/AndroidManifest.xml:6:5-20
MERGED from [androidx.camera:camera-extensions:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/c35911c0c1bc62bf1abcc120055e0553/transformed/jetified-camera-extensions-1.4.2/AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-extensions:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/c35911c0c1bc62bf1abcc120055e0553/transformed/jetified-camera-extensions-1.4.2/AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/4970eabfec40bc56ab46f05b749e5bbe/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/4970eabfec40bc56ab46f05b749e5bbe/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/741f941a3daabd3a8cb13ef469df299a/transformed/jetified-camera-core-1.4.2/AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/741f941a3daabd3a8cb13ef469df299a/transformed/jetified-camera-core-1.4.2/AndroidManifest.xml:23:5-34:19
MERGED from [com.github.bumptech.glide:glide:4.15.1] /Users/<USER>/.gradle/caches/8.12/transforms/c4b220ba768dae0b99251529f1c253f8/transformed/jetified-glide-4.15.1/AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:glide:4.15.1] /Users/<USER>/.gradle/caches/8.12/transforms/c4b220ba768dae0b99251529f1c253f8/transformed/jetified-glide-4.15.1/AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/7795f05fbf8283409257f8d4d50c7a58/transformed/jetified-play-services-tasks-18.2.0/AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/7795f05fbf8283409257f8d4d50c7a58/transformed/jetified-play-services-tasks-18.2.0/AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/1186d146da5ef23629d7bf94e5a0d382/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/1186d146da5ef23629d7bf94e5a0d382/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:5:5-7:19
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.test:runner:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/3358342cf15b831dc97dd680aa63cdfa/transformed/runner-1.6.1/AndroidManifest.xml:30:5-20
MERGED from [androidx.test:runner:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/3358342cf15b831dc97dd680aa63cdfa/transformed/runner-1.6.1/AndroidManifest.xml:30:5-20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/f3b8632830106ae874bd20aa567485d8/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/f3b8632830106ae874bd20aa567485d8/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] /Users/<USER>/.gradle/caches/8.12/transforms/2e30f832adc76ccd477ca150ed813307/transformed/jetified-gifdecoder-4.15.1/AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] /Users/<USER>/.gradle/caches/8.12/transforms/2e30f832adc76ccd477ca150ed813307/transformed/jetified-gifdecoder-4.15.1/AndroidManifest.xml:9:5-20
	android:extractNativeLibs
		INJECTED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/debug/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
	android:name
		INJECTED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml
manifest
ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:1:1-65:12
MERGED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:1:1-65:12
INJECTED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/debug/AndroidManifest.xml:1:1-7:12
MERGED from [:camerawesome] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/camerawesome/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-13:12
MERGED from [:photo_manager] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/photo_manager/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.gms:play-services-location:21.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/566d820d62eceeef77e6fc5ea49a076e/transformed/jetified-play-services-location-21.3.0/AndroidManifest.xml:2:1-8:12
MERGED from [androidx.camera:camera-extensions:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/c35911c0c1bc62bf1abcc120055e0553/transformed/jetified-camera-extensions-1.4.2/AndroidManifest.xml:17:1-34:12
MERGED from [androidx.camera:camera-view:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/670600e90b4df63aacc70ec54c29060f/transformed/jetified-camera-view-1.4.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-video:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/82144679446efd520d61d3f9e737513b/transformed/jetified-camera-video-1.4.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/cb6959a550303410ecb8223e57c94fba/transformed/jetified-camera-lifecycle-1.4.2/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/4970eabfec40bc56ab46f05b749e5bbe/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/741f941a3daabd3a8cb13ef469df299a/transformed/jetified-camera-core-1.4.2/AndroidManifest.xml:17:1-36:12
MERGED from [:gallery_saver_plus] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/gallery_saver_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:path_provider_android] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:permission_handler_android] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/permission_handler_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite_android] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/sqflite_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/f96942f3d58d17244242eba2c1bcf26c/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:glide:4.15.1] /Users/<USER>/.gradle/caches/8.12/transforms/c4b220ba768dae0b99251529f1c253f8/transformed/jetified-glide-4.15.1/AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/7795f05fbf8283409257f8d4d50c7a58/transformed/jetified-play-services-tasks-18.2.0/AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/1186d146da5ef23629d7bf94e5a0d382/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/0d0ad2c9a7eee0ad2b557032bddebd70/transformed/appcompat-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/1c8746a36ac065afed39d95b2852a559/transformed/fragment-1.7.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media:media:1.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/86dbeb9e89d2997dabb91d940a369d9a/transformed/media-1.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/51d67b28f04358995f888f3317b40779/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:17:1-21:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/984bdeb02044daf662dc2d3e1fe07483/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d21df4d1a80ec9bf2502ed8e05d37297/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.8.1] /Users/<USER>/.gradle/caches/8.12/transforms/e60d7cc8f585e105683d15c0883739b4/transformed/jetified-activity-1.8.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/df95e149204d983cb4c11efbc84ab4c1/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d2f89760d0d7afc020c36efe677962c0/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc0590902d0fbba9efca7bc74a8bc4cb/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/84addddb59162e1cea52976d5f2c6cc1/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/1e1e86d9fc1ea8180a98a95859125403/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/e72f610bb8a20735f78a04c908b9b793/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/23f1459f3a17c3f297faa9e854d895db/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/e7b4e62af0008ea11d5619489212cc48/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/e93556932885008eff7df21847fbdad2/transformed/jetified-appcompat-resources-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/10acfa95459151f0abcb0437238b9ca7/transformed/drawerlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/28f988f0d4c2cc22199e4c3cefdd595e/transformed/coordinatorlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2478386b28aa149978ce84e3a3e3f34c/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/58a0f53fd92f6561a1dd451c29da9990/transformed/customview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/ccda8ddd57f5a835df89427c6970b69a/transformed/swiperefreshlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/106b34a8e64882148068274b889c0b9f/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/afec9dc0bcc11d087323dc11f5e0350a/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/ee817ed912dd87a9ffe7b0d8087b9e11/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/aa55b2079cbc673a6a445c1850daa153/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/7f734b899c9b5bcf473e5c8a79b68b93/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/683cbfde6a58705556f8fa87883a18e1/transformed/jetified-annotation-experimental-1.4.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.test:rules:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/f3ef19a73a91c62f9cf355f7eb25d496/transformed/rules-1.6.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test:runner:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/3358342cf15b831dc97dd680aa63cdfa/transformed/runner-1.6.1/AndroidManifest.xml:17:1-32:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.test.services:storage:1.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/15cb5a3bb72a8ee51a83df436255cd1c/transformed/jetified-storage-1.5.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.test:monitor:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/ed83fa2719c32a8733311169b0af00d3/transformed/monitor-1.7.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/27003765dae66b7dc3bf878451ba1684/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/7995343eeb8e0fb97f769a330ffeb5c4/transformed/jetified-tracing-ktx-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/fe841136d8d747d9452213196f0bbe06/transformed/exifinterface-1.4.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/f3b8632830106ae874bd20aa567485d8/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] /Users/<USER>/.gradle/caches/8.12/transforms/2e30f832adc76ccd477ca150ed813307/transformed/jetified-gifdecoder-4.15.1/AndroidManifest.xml:2:1-11:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1d33f966f1aab687e952d4b7cce6845e/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc56adcbf17b642cc8bc810bfcbda96d/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/4cfa7aabd0ff8beb21daa4d12f46b519/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a58c138301656e62a00a9163f21e3a54/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/cb263d6c3807b4994a64a61fe4ea2bcf/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/b7f63da0fad92ba134922d35b82d48c3/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/449958d8d573c37840f9e10ca78b3740/transformed/jetified-core-1.0.0/AndroidManifest.xml:2:1-7:12
MERGED from [io.reactivex.rxjava3:rxandroid:3.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/07760aae2c16d89994a0807b2c81fa20/transformed/jetified-rxandroid-3.0.0/AndroidManifest.xml:15:1-20:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.12/transforms/aa2b156f95f9eab66ccb02ea9eacfedd/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:2:1-7:12
	package
		INJECTED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/debug/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/debug/AndroidManifest.xml
	android:versionCode
		INJECTED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/debug/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.CAMERA
ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:3:5-65
MERGED from [:camerawesome] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/camerawesome/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-65
MERGED from [:camerawesome] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/camerawesome/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-65
	android:name
		ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:3:22-62
uses-permission#android.permission.RECORD_AUDIO
ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:5:5-71
	android:name
		ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:5:22-68
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:7:5-81
	android:name
		ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:7:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:8:5-80
MERGED from [:photo_manager] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/photo_manager/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-9:38
MERGED from [:photo_manager] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/photo_manager/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-9:38
	android:maxSdkVersion
		ADDED from [:photo_manager] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/photo_manager/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-35
	android:name
		ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:8:22-77
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:10:5-76
	android:name
		ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:10:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:11:5-75
	android:name
		ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:11:22-72
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:13:5-14:38
	android:maxSdkVersion
		ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:14:9-35
	android:name
		ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:13:22-79
uses-permission#android.permission.ACCESS_MEDIA_LOCATION
ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:15:5-80
	android:name
		ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:15:22-77
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:17:5-67
MERGED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:17:5-67
MERGED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:17:5-67
	android:name
		ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:17:22-64
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:19:5-79
	android:name
		ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:19:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:20:5-81
	android:name
		ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:20:22-78
queries
ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:59:5-64:15
MERGED from [androidx.camera:camera-extensions:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/c35911c0c1bc62bf1abcc120055e0553/transformed/jetified-camera-extensions-1.4.2/AndroidManifest.xml:22:5-26:15
MERGED from [androidx.camera:camera-extensions:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/c35911c0c1bc62bf1abcc120055e0553/transformed/jetified-camera-extensions-1.4.2/AndroidManifest.xml:22:5-26:15
MERGED from [androidx.test:runner:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/3358342cf15b831dc97dd680aa63cdfa/transformed/runner-1.6.1/AndroidManifest.xml:24:5-28:15
MERGED from [androidx.test:runner:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/3358342cf15b831dc97dd680aa63cdfa/transformed/runner-1.6.1/AndroidManifest.xml:24:5-28:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:60:9-63:18
action#android.intent.action.PROCESS_TEXT
ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:61:13-72
	android:name
		ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:61:21-70
data
ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:62:13-50
	android:mimeType
		ADDED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:62:19-48
uses-sdk
INJECTED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/debug/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/debug/AndroidManifest.xml
INJECTED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/debug/AndroidManifest.xml
MERGED from [:camerawesome] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/camerawesome/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:camerawesome] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/camerawesome/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:photo_manager] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/photo_manager/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:photo_manager] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/photo_manager/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/566d820d62eceeef77e6fc5ea49a076e/transformed/jetified-play-services-location-21.3.0/AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-location:21.3.0] /Users/<USER>/.gradle/caches/8.12/transforms/566d820d62eceeef77e6fc5ea49a076e/transformed/jetified-play-services-location-21.3.0/AndroidManifest.xml:4:5-44
MERGED from [androidx.camera:camera-extensions:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/c35911c0c1bc62bf1abcc120055e0553/transformed/jetified-camera-extensions-1.4.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-extensions:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/c35911c0c1bc62bf1abcc120055e0553/transformed/jetified-camera-extensions-1.4.2/AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-view:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/670600e90b4df63aacc70ec54c29060f/transformed/jetified-camera-view-1.4.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/670600e90b4df63aacc70ec54c29060f/transformed/jetified-camera-view-1.4.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/82144679446efd520d61d3f9e737513b/transformed/jetified-camera-video-1.4.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/82144679446efd520d61d3f9e737513b/transformed/jetified-camera-video-1.4.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/cb6959a550303410ecb8223e57c94fba/transformed/jetified-camera-lifecycle-1.4.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/cb6959a550303410ecb8223e57c94fba/transformed/jetified-camera-lifecycle-1.4.2/AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/4970eabfec40bc56ab46f05b749e5bbe/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/4970eabfec40bc56ab46f05b749e5bbe/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/741f941a3daabd3a8cb13ef469df299a/transformed/jetified-camera-core-1.4.2/AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/741f941a3daabd3a8cb13ef469df299a/transformed/jetified-camera-core-1.4.2/AndroidManifest.xml:21:5-44
MERGED from [:gallery_saver_plus] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/gallery_saver_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:gallery_saver_plus] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/gallery_saver_plus/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/path_provider_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/permission_handler_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/permission_handler_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/sqflite_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/sqflite_android/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/f96942f3d58d17244242eba2c1bcf26c/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/f96942f3d58d17244242eba2c1bcf26c/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.15.1] /Users/<USER>/.gradle/caches/8.12/transforms/c4b220ba768dae0b99251529f1c253f8/transformed/jetified-glide-4.15.1/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.15.1] /Users/<USER>/.gradle/caches/8.12/transforms/c4b220ba768dae0b99251529f1c253f8/transformed/jetified-glide-4.15.1/AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/7795f05fbf8283409257f8d4d50c7a58/transformed/jetified-play-services-tasks-18.2.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/7795f05fbf8283409257f8d4d50c7a58/transformed/jetified-play-services-tasks-18.2.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/1186d146da5ef23629d7bf94e5a0d382/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/1186d146da5ef23629d7bf94e5a0d382/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:3:5-44
MERGED from [androidx.appcompat:appcompat:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/0d0ad2c9a7eee0ad2b557032bddebd70/transformed/appcompat-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/0d0ad2c9a7eee0ad2b557032bddebd70/transformed/appcompat-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/1c8746a36ac065afed39d95b2852a559/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/1c8746a36ac065afed39d95b2852a559/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/86dbeb9e89d2997dabb91d940a369d9a/transformed/media-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/86dbeb9e89d2997dabb91d940a369d9a/transformed/media-1.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/51d67b28f04358995f888f3317b40779/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/51d67b28f04358995f888f3317b40779/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/984bdeb02044daf662dc2d3e1fe07483/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/984bdeb02044daf662dc2d3e1fe07483/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d21df4d1a80ec9bf2502ed8e05d37297/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d21df4d1a80ec9bf2502ed8e05d37297/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] /Users/<USER>/.gradle/caches/8.12/transforms/e60d7cc8f585e105683d15c0883739b4/transformed/jetified-activity-1.8.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] /Users/<USER>/.gradle/caches/8.12/transforms/e60d7cc8f585e105683d15c0883739b4/transformed/jetified-activity-1.8.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/df95e149204d983cb4c11efbc84ab4c1/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/df95e149204d983cb4c11efbc84ab4c1/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d2f89760d0d7afc020c36efe677962c0/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d2f89760d0d7afc020c36efe677962c0/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc0590902d0fbba9efca7bc74a8bc4cb/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc0590902d0fbba9efca7bc74a8bc4cb/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/84addddb59162e1cea52976d5f2c6cc1/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/84addddb59162e1cea52976d5f2c6cc1/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/1e1e86d9fc1ea8180a98a95859125403/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/1e1e86d9fc1ea8180a98a95859125403/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/e72f610bb8a20735f78a04c908b9b793/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/e72f610bb8a20735f78a04c908b9b793/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/23f1459f3a17c3f297faa9e854d895db/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/23f1459f3a17c3f297faa9e854d895db/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/e7b4e62af0008ea11d5619489212cc48/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/e7b4e62af0008ea11d5619489212cc48/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/e93556932885008eff7df21847fbdad2/transformed/jetified-appcompat-resources-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/e93556932885008eff7df21847fbdad2/transformed/jetified-appcompat-resources-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/10acfa95459151f0abcb0437238b9ca7/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/10acfa95459151f0abcb0437238b9ca7/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/28f988f0d4c2cc22199e4c3cefdd595e/transformed/coordinatorlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/28f988f0d4c2cc22199e4c3cefdd595e/transformed/coordinatorlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2478386b28aa149978ce84e3a3e3f34c/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2478386b28aa149978ce84e3a3e3f34c/transformed/slidingpanelayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/58a0f53fd92f6561a1dd451c29da9990/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/58a0f53fd92f6561a1dd451c29da9990/transformed/customview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/ccda8ddd57f5a835df89427c6970b69a/transformed/swiperefreshlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/ccda8ddd57f5a835df89427c6970b69a/transformed/swiperefreshlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/106b34a8e64882148068274b889c0b9f/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/106b34a8e64882148068274b889c0b9f/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/afec9dc0bcc11d087323dc11f5e0350a/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/afec9dc0bcc11d087323dc11f5e0350a/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/ee817ed912dd87a9ffe7b0d8087b9e11/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/ee817ed912dd87a9ffe7b0d8087b9e11/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/aa55b2079cbc673a6a445c1850daa153/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/aa55b2079cbc673a6a445c1850daa153/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/7f734b899c9b5bcf473e5c8a79b68b93/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/7f734b899c9b5bcf473e5c8a79b68b93/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/683cbfde6a58705556f8fa87883a18e1/transformed/jetified-annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/683cbfde6a58705556f8fa87883a18e1/transformed/jetified-annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.test:rules:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/f3ef19a73a91c62f9cf355f7eb25d496/transformed/rules-1.6.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:rules:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/f3ef19a73a91c62f9cf355f7eb25d496/transformed/rules-1.6.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:runner:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/3358342cf15b831dc97dd680aa63cdfa/transformed/runner-1.6.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:runner:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/3358342cf15b831dc97dd680aa63cdfa/transformed/runner-1.6.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.test.services:storage:1.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/15cb5a3bb72a8ee51a83df436255cd1c/transformed/jetified-storage-1.5.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test.services:storage:1.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/15cb5a3bb72a8ee51a83df436255cd1c/transformed/jetified-storage-1.5.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:monitor:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/ed83fa2719c32a8733311169b0af00d3/transformed/monitor-1.7.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.test:monitor:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/ed83fa2719c32a8733311169b0af00d3/transformed/monitor-1.7.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/27003765dae66b7dc3bf878451ba1684/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/27003765dae66b7dc3bf878451ba1684/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/7995343eeb8e0fb97f769a330ffeb5c4/transformed/jetified-tracing-ktx-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/7995343eeb8e0fb97f769a330ffeb5c4/transformed/jetified-tracing-ktx-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/fe841136d8d747d9452213196f0bbe06/transformed/exifinterface-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.exifinterface:exifinterface:1.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/fe841136d8d747d9452213196f0bbe06/transformed/exifinterface-1.4.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/f3b8632830106ae874bd20aa567485d8/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/f3b8632830106ae874bd20aa567485d8/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] /Users/<USER>/.gradle/caches/8.12/transforms/2e30f832adc76ccd477ca150ed813307/transformed/jetified-gifdecoder-4.15.1/AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] /Users/<USER>/.gradle/caches/8.12/transforms/2e30f832adc76ccd477ca150ed813307/transformed/jetified-gifdecoder-4.15.1/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1d33f966f1aab687e952d4b7cce6845e/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1d33f966f1aab687e952d4b7cce6845e/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc56adcbf17b642cc8bc810bfcbda96d/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc56adcbf17b642cc8bc810bfcbda96d/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/4cfa7aabd0ff8beb21daa4d12f46b519/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/4cfa7aabd0ff8beb21daa4d12f46b519/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a58c138301656e62a00a9163f21e3a54/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a58c138301656e62a00a9163f21e3a54/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/cb263d6c3807b4994a64a61fe4ea2bcf/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/cb263d6c3807b4994a64a61fe4ea2bcf/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/b7f63da0fad92ba134922d35b82d48c3/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/b7f63da0fad92ba134922d35b82d48c3/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/449958d8d573c37840f9e10ca78b3740/transformed/jetified-core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/449958d8d573c37840f9e10ca78b3740/transformed/jetified-core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [io.reactivex.rxjava3:rxandroid:3.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/07760aae2c16d89994a0807b2c81fa20/transformed/jetified-rxandroid-3.0.0/AndroidManifest.xml:18:5-43
MERGED from [io.reactivex.rxjava3:rxandroid:3.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/07760aae2c16d89994a0807b2c81fa20/transformed/jetified-rxandroid-3.0.0/AndroidManifest.xml:18:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.12/transforms/aa2b156f95f9eab66ccb02ea9eacfedd/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.12/transforms/aa2b156f95f9eab66ccb02ea9eacfedd/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:5:5-43
	android:targetSdkVersion
		INJECTED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/debug/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/debug/AndroidManifest.xml
service#io.apparence.camerawesome.buttons.PlayerService
ADDED from [:camerawesome] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/camerawesome/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:9-83
	android:name
		ADDED from [:camerawesome] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/camerawesome/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:18-80
intent#action:name:androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/c35911c0c1bc62bf1abcc120055e0553/transformed/jetified-camera-extensions-1.4.2/AndroidManifest.xml:23:9-25:18
action#androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/c35911c0c1bc62bf1abcc120055e0553/transformed/jetified-camera-extensions-1.4.2/AndroidManifest.xml:24:13-86
	android:name
		ADDED from [androidx.camera:camera-extensions:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/c35911c0c1bc62bf1abcc120055e0553/transformed/jetified-camera-extensions-1.4.2/AndroidManifest.xml:24:21-83
uses-library#androidx.camera.extensions.impl
ADDED from [androidx.camera:camera-extensions:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/c35911c0c1bc62bf1abcc120055e0553/transformed/jetified-camera-extensions-1.4.2/AndroidManifest.xml:29:9-31:40
	android:required
		ADDED from [androidx.camera:camera-extensions:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/c35911c0c1bc62bf1abcc120055e0553/transformed/jetified-camera-extensions-1.4.2/AndroidManifest.xml:31:13-37
	android:name
		ADDED from [androidx.camera:camera-extensions:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/c35911c0c1bc62bf1abcc120055e0553/transformed/jetified-camera-extensions-1.4.2/AndroidManifest.xml:30:13-59
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/4970eabfec40bc56ab46f05b749e5bbe/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/741f941a3daabd3a8cb13ef469df299a/transformed/jetified-camera-core-1.4.2/AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/741f941a3daabd3a8cb13ef469df299a/transformed/jetified-camera-core-1.4.2/AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/4970eabfec40bc56ab46f05b749e5bbe/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/4970eabfec40bc56ab46f05b749e5bbe/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/4970eabfec40bc56ab46f05b749e5bbe/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/4970eabfec40bc56ab46f05b749e5bbe/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/4970eabfec40bc56ab46f05b749e5bbe/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/4970eabfec40bc56ab46f05b749e5bbe/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/4970eabfec40bc56ab46f05b749e5bbe/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/4970eabfec40bc56ab46f05b749e5bbe/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:31:17-103
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:19-85
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/1186d146da5ef23629d7bf94e5a0d382/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/1186d146da5ef23629d7bf94e5a0d382/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/1186d146da5ef23629d7bf94e5a0d382/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:6:20-65
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
permission#com.example.watermarkcamera.watermark_camera.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
uses-permission#com.example.watermarkcamera.watermark_camera.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
package#androidx.test.orchestrator
ADDED from [androidx.test:runner:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/3358342cf15b831dc97dd680aa63cdfa/transformed/runner-1.6.1/AndroidManifest.xml:25:9-62
	android:name
		ADDED from [androidx.test:runner:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/3358342cf15b831dc97dd680aa63cdfa/transformed/runner-1.6.1/AndroidManifest.xml:25:18-59
package#androidx.test.services
ADDED from [androidx.test:runner:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/3358342cf15b831dc97dd680aa63cdfa/transformed/runner-1.6.1/AndroidManifest.xml:26:9-58
	android:name
		ADDED from [androidx.test:runner:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/3358342cf15b831dc97dd680aa63cdfa/transformed/runner-1.6.1/AndroidManifest.xml:26:18-55
package#com.google.android.apps.common.testing.services
ADDED from [androidx.test:runner:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/3358342cf15b831dc97dd680aa63cdfa/transformed/runner-1.6.1/AndroidManifest.xml:27:9-83
	android:name
		ADDED from [androidx.test:runner:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/3358342cf15b831dc97dd680aa63cdfa/transformed/runner-1.6.1/AndroidManifest.xml:27:18-80
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
