1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.watermarkcamera.watermark_camera"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:17:5-67
15-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:17:22-64
16    <!-- 相机权限 -->
17    <uses-permission android:name="android.permission.CAMERA" /> <!-- 录音权限 -->
17-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:3:5-65
17-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:3:22-62
18    <uses-permission android:name="android.permission.RECORD_AUDIO" /> <!-- 存储权限 -->
18-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:5:5-71
18-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:5:22-68
19    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
19-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:7:5-81
19-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:7:22-78
20    <uses-permission
20-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:8:5-80
21        android:name="android.permission.READ_EXTERNAL_STORAGE"
21-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:8:22-77
22        android:maxSdkVersion="32" /> <!-- Android 13+ 媒体权限 -->
22-->[:photo_manager] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/photo_manager/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-35
23    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
23-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:10:5-76
23-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:10:22-73
24    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" /> <!-- 相册访问权限 -->
24-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:11:5-75
24-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:11:22-72
25    <uses-permission
25-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:13:5-14:38
26        android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
26-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:13:22-79
27        android:maxSdkVersion="32" />
27-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:14:9-35
28    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" /> <!-- 位置权限（用于照片EXIF数据） -->
28-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:15:5-80
28-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:15:22-77
29    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
29-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:19:5-79
29-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:19:22-76
30    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
30-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:20:5-81
30-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:20:22-78
31    <!--
32 Required to query activities that can process text, see:
33         https://developer.android.com/training/package-visibility and
34         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
35
36         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
37    -->
38    <queries>
38-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:59:5-64:15
39        <intent>
39-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:60:9-63:18
40            <action android:name="android.intent.action.PROCESS_TEXT" />
40-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:61:13-72
40-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:61:21-70
41
42            <data android:mimeType="text/plain" />
42-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:62:13-50
42-->/Users/<USER>/Desktop/apps/pailide/watermark_camera/android/app/src/main/AndroidManifest.xml:62:19-48
43        </intent>
44        <intent>
44-->[androidx.camera:camera-extensions:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/c35911c0c1bc62bf1abcc120055e0553/transformed/jetified-camera-extensions-1.4.2/AndroidManifest.xml:23:9-25:18
45            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
45-->[androidx.camera:camera-extensions:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/c35911c0c1bc62bf1abcc120055e0553/transformed/jetified-camera-extensions-1.4.2/AndroidManifest.xml:24:13-86
45-->[androidx.camera:camera-extensions:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/c35911c0c1bc62bf1abcc120055e0553/transformed/jetified-camera-extensions-1.4.2/AndroidManifest.xml:24:21-83
46        </intent>
47
48        <package android:name="androidx.test.orchestrator" />
48-->[androidx.test:runner:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/3358342cf15b831dc97dd680aa63cdfa/transformed/runner-1.6.1/AndroidManifest.xml:25:9-62
48-->[androidx.test:runner:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/3358342cf15b831dc97dd680aa63cdfa/transformed/runner-1.6.1/AndroidManifest.xml:25:18-59
49        <package android:name="androidx.test.services" />
49-->[androidx.test:runner:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/3358342cf15b831dc97dd680aa63cdfa/transformed/runner-1.6.1/AndroidManifest.xml:26:9-58
49-->[androidx.test:runner:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/3358342cf15b831dc97dd680aa63cdfa/transformed/runner-1.6.1/AndroidManifest.xml:26:18-55
50        <package android:name="com.google.android.apps.common.testing.services" />
50-->[androidx.test:runner:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/3358342cf15b831dc97dd680aa63cdfa/transformed/runner-1.6.1/AndroidManifest.xml:27:9-83
50-->[androidx.test:runner:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/3358342cf15b831dc97dd680aa63cdfa/transformed/runner-1.6.1/AndroidManifest.xml:27:18-80
51    </queries>
52
53    <permission
53-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
54        android:name="com.example.watermarkcamera.watermark_camera.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
54-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
55        android:protectionLevel="signature" />
55-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
56
57    <uses-permission android:name="com.example.watermarkcamera.watermark_camera.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
57-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
57-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
58
59    <application
60        android:name="android.app.Application"
61        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
61-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
62        android:debuggable="true"
63        android:extractNativeLibs="false"
64        android:icon="@mipmap/ic_launcher"
65        android:label="watermark_camera" >
66        <activity
67            android:name="com.example.watermarkcamera.watermark_camera.MainActivity"
68            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
69            android:exported="true"
70            android:hardwareAccelerated="true"
71            android:launchMode="singleTop"
72            android:taskAffinity=""
73            android:theme="@style/LaunchTheme"
74            android:windowSoftInputMode="adjustResize" >
75
76            <!--
77                 Specifies an Android theme to apply to this Activity as soon as
78                 the Android process has started. This theme is visible to the user
79                 while the Flutter UI initializes. After that, this theme continues
80                 to determine the Window background behind the Flutter UI.
81            -->
82            <meta-data
83                android:name="io.flutter.embedding.android.NormalTheme"
84                android:resource="@style/NormalTheme" />
85
86            <intent-filter>
87                <action android:name="android.intent.action.MAIN" />
88
89                <category android:name="android.intent.category.LAUNCHER" />
90            </intent-filter>
91        </activity>
92        <!--
93             Don't delete the meta-data below.
94             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
95        -->
96        <meta-data
97            android:name="flutterEmbedding"
98            android:value="2" />
99
100        <service android:name="io.apparence.camerawesome.buttons.PlayerService" />
100-->[:camerawesome] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/camerawesome/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:9-83
100-->[:camerawesome] /Users/<USER>/Desktop/apps/pailide/watermark_camera/build/camerawesome/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:18-80
101
102        <uses-library
102-->[androidx.camera:camera-extensions:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/c35911c0c1bc62bf1abcc120055e0553/transformed/jetified-camera-extensions-1.4.2/AndroidManifest.xml:29:9-31:40
103            android:name="androidx.camera.extensions.impl"
103-->[androidx.camera:camera-extensions:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/c35911c0c1bc62bf1abcc120055e0553/transformed/jetified-camera-extensions-1.4.2/AndroidManifest.xml:30:13-59
104            android:required="false" />
104-->[androidx.camera:camera-extensions:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/c35911c0c1bc62bf1abcc120055e0553/transformed/jetified-camera-extensions-1.4.2/AndroidManifest.xml:31:13-37
105
106        <service
106-->[androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/4970eabfec40bc56ab46f05b749e5bbe/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:24:9-33:19
107            android:name="androidx.camera.core.impl.MetadataHolderService"
107-->[androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/4970eabfec40bc56ab46f05b749e5bbe/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:25:13-75
108            android:enabled="false"
108-->[androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/4970eabfec40bc56ab46f05b749e5bbe/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:26:13-36
109            android:exported="false" >
109-->[androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/4970eabfec40bc56ab46f05b749e5bbe/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:27:13-37
110            <meta-data
110-->[androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/4970eabfec40bc56ab46f05b749e5bbe/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:30:13-32:89
111                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
111-->[androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/4970eabfec40bc56ab46f05b749e5bbe/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:31:17-103
112                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
112-->[androidx.camera:camera-camera2:1.4.2] /Users/<USER>/.gradle/caches/8.12/transforms/4970eabfec40bc56ab46f05b749e5bbe/transformed/jetified-camera-camera2-1.4.2/AndroidManifest.xml:32:17-86
113        </service>
114
115        <activity
115-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:9-173
116            android:name="com.google.android.gms.common.api.GoogleApiActivity"
116-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:19-85
117            android:exported="false"
117-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:146-170
118            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
118-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:86-145
119
120        <meta-data
120-->[com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/1186d146da5ef23629d7bf94e5a0d382/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:6:9-122
121            android:name="com.google.android.gms.version"
121-->[com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/1186d146da5ef23629d7bf94e5a0d382/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:6:20-65
122            android:value="@integer/google_play_services_version" />
122-->[com.google.android.gms:play-services-basement:18.4.0] /Users/<USER>/.gradle/caches/8.12/transforms/1186d146da5ef23629d7bf94e5a0d382/transformed/jetified-play-services-basement-18.4.0/AndroidManifest.xml:6:66-119
123
124        <uses-library
124-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
125            android:name="androidx.window.extensions"
125-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
126            android:required="false" />
126-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
127        <uses-library
127-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
128            android:name="androidx.window.sidecar"
128-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
129            android:required="false" />
129-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
130
131        <provider
131-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
132            android:name="androidx.startup.InitializationProvider"
132-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:25:13-67
133            android:authorities="com.example.watermarkcamera.watermark_camera.androidx-startup"
133-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:26:13-68
134            android:exported="false" >
134-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:27:13-37
135            <meta-data
135-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
136                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
136-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
137                android:value="androidx.startup" />
137-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
138            <meta-data
138-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
139                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
139-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
140                android:value="androidx.startup" />
140-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
141        </provider>
142
143        <receiver
143-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
144            android:name="androidx.profileinstaller.ProfileInstallReceiver"
144-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
145            android:directBootAware="false"
145-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
146            android:enabled="true"
146-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
147            android:exported="true"
147-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
148            android:permission="android.permission.DUMP" >
148-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
149            <intent-filter>
149-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
150                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
150-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
150-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
151            </intent-filter>
152            <intent-filter>
152-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
153                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
153-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
153-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
154            </intent-filter>
155            <intent-filter>
155-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
156                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
156-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
156-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
157            </intent-filter>
158            <intent-filter>
158-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
159                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
159-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
159-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
160            </intent-filter>
161        </receiver>
162    </application>
163
164</manifest>
