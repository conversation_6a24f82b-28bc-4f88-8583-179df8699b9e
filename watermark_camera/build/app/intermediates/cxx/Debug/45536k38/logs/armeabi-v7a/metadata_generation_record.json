[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: armeabi-v7a", "file_": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON '/Users/<USER>/Desktop/apps/pailide/watermark_camera/build/.cxx/Debug/45536k38/armeabi-v7a/android_gradle_build.json' was up-to-date", "file_": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "/opt/homebrew/Caskroom/flutter/3.32.4/flutter/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]