9com.apparence.camerawesome.buttons.PhysicalButtonsHandler?com.apparence.camerawesome.buttons.PhysicalButtonMessageHandler0com.apparence.camerawesome.buttons.PlayerService9com.apparence.camerawesome.cameraX.AnalysisImageConverter/com.apparence.camerawesome.cameraX.CaptureModes1com.apparence.camerawesome.cameraX.CameraAwesomeX4com.apparence.camerawesome.cameraX.CameraPermissions/com.apparence.camerawesome.cameraX.CameraXState4com.apparence.camerawesome.cameraX.OutputImageFormat/com.apparence.camerawesome.cameraX.FlutterError7com.apparence.camerawesome.cameraX.PigeonSensorPosition8com.apparence.camerawesome.cameraX.VideoRecordingQuality:com.apparence.camerawesome.cameraX.QualityFallbackStrategy4com.apparence.camerawesome.cameraX.CupertinoFileType5com.apparence.camerawesome.cameraX.CupertinoCodecType3com.apparence.camerawesome.cameraX.PigeonSensorType9com.apparence.camerawesome.cameraX.CamerAwesomePermission6com.apparence.camerawesome.cameraX.AnalysisImageFormat3com.apparence.camerawesome.cameraX.AnalysisRotation:com.apparence.camerawesome.cameraX.AnalysisImageUtilsCodec7com.apparence.camerawesome.cameraX.CameraInterfaceCodecDcom.apparence.camerawesome.exceptions.PermissionNotDeclaredException<com.apparence.camerawesome.sensors.SensorOrientationListener>com.apparence.camerawesome.utils.ResettableCountDownLatch.Sync/com.apparence.camerawesome_example.MainActivity                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           