3io.flutter.plugin.common.EventChannel.StreamHandlerandroid.os.Handlerandroid.app.Service5com.apparence.camerawesome.cameraX.AnalysisImageUtilskotlin.Enum2com.apparence.camerawesome.cameraX.CameraInterface1io.flutter.embedding.engine.plugins.FlutterPlugin:io.flutter.embedding.engine.plugins.activity.ActivityAwareHio.flutter.plugin.common.PluginRegistry.RequestPermissionsResultListener4com.apparence.camerawesome.sensors.SensorOrientationkotlin.Throwable-io.flutter.plugin.common.StandardMessageCodecjava.lang.Exception5java.util.concurrent.locks.AbstractQueuedSynchronizer,io.flutter.embedding.android.FlutterActivity                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               