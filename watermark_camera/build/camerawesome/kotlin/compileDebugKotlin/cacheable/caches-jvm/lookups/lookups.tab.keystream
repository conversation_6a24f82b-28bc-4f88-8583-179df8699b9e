  Manifest android  ACCESS_COARSE_LOCATION android.Manifest.permission  ACCESS_FINE_LOCATION android.Manifest.permission  CAMERA android.Manifest.permission  RECORD_AUDIO android.Manifest.permission  WRITE_EXTERNAL_STORAGE android.Manifest.permission  SuppressLint android.annotation  Activity android.app  Service android.app  applicationContext android.app.Activity  startService android.app.Activity  stopService android.app.Activity  Build android.app.Service  Int android.app.Service  MediaSessionCompat android.app.Service  Message android.app.Service  	Messenger android.app.Service  PhysicalButtonsHandler android.app.Service  PlaybackStateCompat android.app.Service  VolumeProviderCompat android.app.Service  apply android.app.Service  java android.app.Service  	messenger android.app.Service  onCreate android.app.Service  	onDestroy android.app.Service  onStartCommand android.app.Service  
ComponentName android.content  Context android.content  Intent android.content  Build android.content.Context  DISPLAY_SERVICE android.content.Context  Int android.content.Context  MediaSessionCompat android.content.Context  Message android.content.Context  	Messenger android.content.Context  PhysicalButtonsHandler android.content.Context  PlaybackStateCompat android.content.Context  VolumeProviderCompat android.content.Context  apply android.content.Context  getSystemService android.content.Context  java android.content.Context  	messenger android.content.Context  packageManager android.content.Context  packageName android.content.Context  Build android.content.ContextWrapper  Int android.content.ContextWrapper  MediaSessionCompat android.content.ContextWrapper  Message android.content.ContextWrapper  	Messenger android.content.ContextWrapper  PhysicalButtonsHandler android.content.ContextWrapper  PlaybackStateCompat android.content.ContextWrapper  VolumeProviderCompat android.content.ContextWrapper  applicationContext android.content.ContextWrapper  apply android.content.ContextWrapper  java android.content.ContextWrapper  	messenger android.content.ContextWrapper  startService android.content.ContextWrapper  stopService android.content.ContextWrapper  extras android.content.Intent  putExtra android.content.Intent  PackageInfo android.content.pm  PackageManager android.content.pm  requestedPermissions android.content.pm.PackageInfo  GET_PERMISSIONS !android.content.pm.PackageManager  PERMISSION_GRANTED !android.content.pm.PackageManager  getPackageInfo !android.content.pm.PackageManager  Activity android.graphics  
ActivityAware android.graphics  ActivityCompat android.graphics  ActivityPluginBinding android.graphics  AnalysisImageConverter android.graphics  AnalysisImageUtils android.graphics  AndroidFocusSettings android.graphics  AspectRatio android.graphics  BehaviorSubject android.graphics  Bitmap android.graphics  
BitmapFactory android.graphics  Boolean android.graphics  Build android.graphics  CamerAwesomePermission android.graphics  
Camera2Config android.graphics  CameraCapabilities android.graphics  CameraCharacteristics android.graphics  CameraInfoUnavailableException android.graphics  CameraInterface android.graphics  CameraPermissions android.graphics  CameraSelector android.graphics  
CameraXConfig android.graphics  CameraXState android.graphics  CamerawesomePlugin android.graphics  CancellationTokenSource android.graphics  Canvas android.graphics  CaptureModes android.graphics  ColorMatrixColorFilter android.graphics  Consumer android.graphics  
ContextCompat android.graphics  CoroutineScope android.graphics  CountDownTimer android.graphics  
Deprecated android.graphics  Dispatchers android.graphics  
Disposable android.graphics  Double android.graphics  EventChannel android.graphics  	Exception android.graphics  
ExifInterface android.graphics  ExifPreferences android.graphics  ExperimentalCamera2Interop android.graphics  File android.graphics  FileOutputOptions android.graphics  FileOutputStream android.graphics  	FlashMode android.graphics  
FlutterPlugin android.graphics  FlutterPluginBinding android.graphics  FocusMeteringAction android.graphics  FusedLocationProviderClient android.graphics  Handler android.graphics  IOException android.graphics  IllegalStateException android.graphics  ImageAnalysisBuilder android.graphics  ImageCapture android.graphics  ImageCaptureException android.graphics  ImageFormat android.graphics  Int android.graphics  Intent android.graphics  List android.graphics  Location android.graphics  LocationServices android.graphics  Log android.graphics  Long android.graphics  Looper android.graphics  Manifest android.graphics  Matrix android.graphics  	Messenger android.graphics  MeteringPointFactory android.graphics  MutableList android.graphics  
MutableMap android.graphics  OrientationStreamListener android.graphics  OutputImageFormat android.graphics  PackageManager android.graphics  Paint android.graphics  PhysicalButtonMessageHandler android.graphics  PhysicalButtonsHandler android.graphics  PigeonSensor android.graphics  PigeonSensorPosition android.graphics  PigeonSensorTypeDevice android.graphics  
PlayerService android.graphics  PreviewSize android.graphics  Priority android.graphics  ProcessCameraProvider android.graphics  Rational android.graphics  Rect android.graphics  Result android.graphics  SensorOrientationListener android.graphics  Size android.graphics  String android.graphics  SuppressLint android.graphics  #SurfaceOrientedMeteringPointFactory android.graphics  SurfaceTexture android.graphics  TODO android.graphics  TextureRegistry android.graphics  TimeUnit android.graphics  Unit android.graphics  VideoOptions android.graphics  VideoRecordEvent android.graphics  YuvImage android.graphics  activity android.graphics  all android.graphics  apply android.graphics  	buildList android.graphics  cameraPermissions android.graphics  cameraState android.graphics  colorMatrix android.graphics  	configure android.graphics  configureInstance android.graphics  exifPreferences android.graphics  first android.graphics  firstOrNull android.graphics  forEach android.graphics  getCameraLevel android.graphics  getInstance android.graphics  	getOrNull android.graphics  getOrientedSize android.graphics  isMultiCamSupported android.graphics  
isNotEmpty android.graphics  java android.graphics  lastRecordedVideoSubscriptions android.graphics  lastRecordedVideos android.graphics  launch android.graphics  listOf android.graphics  	lowercase android.graphics  map android.graphics  
mapIndexed android.graphics  
mapNotNull android.graphics  	mapValues android.graphics  
mutableListOf android.graphics  
noneFilter android.graphics  orientationStreamListener android.graphics  resume android.graphics  retrieveLocation android.graphics  
roundToInt android.graphics  run android.graphics  set android.graphics  setUp android.graphics  success android.graphics  suspendCancellableCoroutine android.graphics  
takePhotoWith android.graphics  to android.graphics  toFloatArray android.graphics  toMap android.graphics  toMutableMap android.graphics  until android.graphics  	uppercase android.graphics  use android.graphics  	withIndex android.graphics  CompressFormat android.graphics.Bitmap  Config android.graphics.Bitmap  compress android.graphics.Bitmap  createBitmap android.graphics.Bitmap  height android.graphics.Bitmap  recycle android.graphics.Bitmap  	setPixels android.graphics.Bitmap  width android.graphics.Bitmap  JPEG &android.graphics.Bitmap.CompressFormat  	ARGB_8888 android.graphics.Bitmap.Config  
decodeFile android.graphics.BitmapFactory  
drawBitmap android.graphics.Canvas  OnImageSavedCallback android.graphics.ImageCapture  OutputFileResults android.graphics.ImageCapture  NV21 android.graphics.ImageFormat  
postRotate android.graphics.Matrix  preScale android.graphics.Matrix  ColorMatrixColorFilter android.graphics.Paint  apply android.graphics.Paint  colorFilter android.graphics.Paint  colorMatrix android.graphics.Paint  map android.graphics.Paint  toFloatArray android.graphics.Paint  bottom android.graphics.Rect  left android.graphics.Rect  right android.graphics.Rect  top android.graphics.Rect  setDefaultBufferSize android.graphics.SurfaceTexture  Finalize !android.graphics.VideoRecordEvent  Start !android.graphics.VideoRecordEvent  compressToJpeg android.graphics.YuvImage  CameraCharacteristics android.hardware.camera2  INFO_SUPPORTED_HARDWARE_LEVEL .android.hardware.camera2.CameraCharacteristics  INFO_SUPPORTED_HARDWARE_LEVEL_3 .android.hardware.camera2.CameraCharacteristics  $INFO_SUPPORTED_HARDWARE_LEVEL_LEGACY .android.hardware.camera2.CameraCharacteristics  %INFO_SUPPORTED_HARDWARE_LEVEL_LIMITED .android.hardware.camera2.CameraCharacteristics  Key .android.hardware.camera2.CameraCharacteristics  LENS_FACING .android.hardware.camera2.CameraCharacteristics  !LENS_INFO_AVAILABLE_FOCAL_LENGTHS .android.hardware.camera2.CameraCharacteristics  SENSOR_INFO_PHYSICAL_SIZE .android.hardware.camera2.CameraCharacteristics  INFO_SUPPORTED_HARDWARE_LEVEL_3 'android.hardware.camera2.CameraMetadata  $INFO_SUPPORTED_HARDWARE_LEVEL_LEGACY 'android.hardware.camera2.CameraMetadata  %INFO_SUPPORTED_HARDWARE_LEVEL_LIMITED 'android.hardware.camera2.CameraMetadata  DisplayManager android.hardware.display  DisplayListener 'android.hardware.display.DisplayManager  
getDisplay 'android.hardware.display.DisplayManager  registerDisplayListener 'android.hardware.display.DisplayManager  unregisterDisplayListener 'android.hardware.display.DisplayManager  Location android.location  Image 
android.media  height android.media.Image  planes android.media.Image  width android.media.Image  buffer android.media.Image.Plane  pixelStride android.media.Image.Plane  	rowStride android.media.Image.Plane  Uri android.net  path android.net.Uri  Activity 
android.os  
ActivityAware 
android.os  ActivityCompat 
android.os  ActivityPluginBinding 
android.os  AnalysisImageConverter 
android.os  AnalysisImageUtils 
android.os  AndroidFocusSettings 
android.os  AspectRatio 
android.os  BehaviorSubject 
android.os  Bitmap 
android.os  
BitmapFactory 
android.os  Boolean 
android.os  Build 
android.os  Bundle 
android.os  CamerAwesomePermission 
android.os  
Camera2Config 
android.os  CameraCapabilities 
android.os  CameraCharacteristics 
android.os  CameraInfoUnavailableException 
android.os  CameraInterface 
android.os  CameraPermissions 
android.os  CameraSelector 
android.os  
CameraXConfig 
android.os  CameraXState 
android.os  CamerawesomePlugin 
android.os  CancellationTokenSource 
android.os  Canvas 
android.os  CaptureModes 
android.os  ColorMatrixColorFilter 
android.os  Consumer 
android.os  
ContextCompat 
android.os  CoroutineScope 
android.os  CountDownTimer 
android.os  
Deprecated 
android.os  Dispatchers 
android.os  
Disposable 
android.os  Double 
android.os  EventChannel 
android.os  	Exception 
android.os  
ExifInterface 
android.os  ExifPreferences 
android.os  ExperimentalCamera2Interop 
android.os  File 
android.os  FileOutputOptions 
android.os  FileOutputStream 
android.os  	FlashMode 
android.os  
FlutterPlugin 
android.os  FlutterPluginBinding 
android.os  FocusMeteringAction 
android.os  FusedLocationProviderClient 
android.os  Handler 
android.os  IBinder 
android.os  IOException 
android.os  IllegalStateException 
android.os  ImageAnalysisBuilder 
android.os  ImageCapture 
android.os  ImageCaptureException 
android.os  Int 
android.os  Intent 
android.os  List 
android.os  Location 
android.os  LocationServices 
android.os  Log 
android.os  Long 
android.os  Looper 
android.os  Manifest 
android.os  Message 
android.os  	Messenger 
android.os  MeteringPointFactory 
android.os  MutableList 
android.os  
MutableMap 
android.os  OrientationStreamListener 
android.os  OutputImageFormat 
android.os  PackageManager 
android.os  Paint 
android.os  PhysicalButtonMessageHandler 
android.os  PhysicalButtonsHandler 
android.os  PigeonSensor 
android.os  PigeonSensorPosition 
android.os  PigeonSensorTypeDevice 
android.os  
PlayerService 
android.os  PreviewSize 
android.os  Priority 
android.os  ProcessCameraProvider 
android.os  Rational 
android.os  Result 
android.os  SensorOrientationListener 
android.os  Size 
android.os  String 
android.os  SuppressLint 
android.os  #SurfaceOrientedMeteringPointFactory 
android.os  TODO 
android.os  TextureRegistry 
android.os  TimeUnit 
android.os  Unit 
android.os  VideoOptions 
android.os  VideoRecordEvent 
android.os  activity 
android.os  all 
android.os  apply 
android.os  	buildList 
android.os  cameraPermissions 
android.os  cameraState 
android.os  colorMatrix 
android.os  	configure 
android.os  configureInstance 
android.os  exifPreferences 
android.os  first 
android.os  firstOrNull 
android.os  forEach 
android.os  getCameraLevel 
android.os  getInstance 
android.os  	getOrNull 
android.os  getOrientedSize 
android.os  isMultiCamSupported 
android.os  
isNotEmpty 
android.os  java 
android.os  lastRecordedVideoSubscriptions 
android.os  lastRecordedVideos 
android.os  launch 
android.os  listOf 
android.os  	lowercase 
android.os  map 
android.os  
mapIndexed 
android.os  
mapNotNull 
android.os  	mapValues 
android.os  
mutableListOf 
android.os  
noneFilter 
android.os  orientationStreamListener 
android.os  resume 
android.os  retrieveLocation 
android.os  
roundToInt 
android.os  run 
android.os  set 
android.os  setUp 
android.os  success 
android.os  suspendCancellableCoroutine 
android.os  
takePhotoWith 
android.os  to 
android.os  toFloatArray 
android.os  toMap 
android.os  toMutableMap 
android.os  until 
android.os  	uppercase 
android.os  use 
android.os  	withIndex 
android.os  SDK_INT android.os.Build.VERSION  N android.os.Build.VERSION_CODES  TIRAMISU android.os.Build.VERSION_CODES  
getParcelable android.os.Bundle  Result android.os.CountDownTimer  cancel android.os.CountDownTimer  start android.os.CountDownTimer  success android.os.CountDownTimer  Looper android.os.Handler  postDelayed android.os.Handler  OnImageSavedCallback android.os.ImageCapture  OutputFileResults android.os.ImageCapture  
getMainLooper android.os.Looper  arg1 android.os.Message  obtain android.os.Message  send android.os.Messenger  Finalize android.os.VideoRecordEvent  Start android.os.VideoRecordEvent  MediaSessionCompat  android.support.v4.media.session  PlaybackStateCompat  android.support.v4.media.session  isActive 3android.support.v4.media.session.MediaSessionCompat  release 3android.support.v4.media.session.MediaSessionCompat  setPlaybackState 3android.support.v4.media.session.MediaSessionCompat  setPlaybackToRemote 3android.support.v4.media.session.MediaSessionCompat  Builder 4android.support.v4.media.session.PlaybackStateCompat  
STATE_PLAYING 4android.support.v4.media.session.PlaybackStateCompat  build <android.support.v4.media.session.PlaybackStateCompat.Builder  setState <android.support.v4.media.session.PlaybackStateCompat.Builder  Log android.util  Range android.util  Rational android.util  Size android.util  SizeF android.util  ERROR android.util.Log  d android.util.Log  e android.util.Log  getStackTraceString android.util.Log  w android.util.Log  lower android.util.Range  upper android.util.Range  denominator android.util.Rational  	numerator android.util.Rational  bigger android.util.Size  height android.util.Size  max android.util.Size  min android.util.Size  width android.util.Size  bigger android.util.SizeF  height android.util.SizeF  max android.util.SizeF  min android.util.SizeF  width android.util.SizeF  Display android.view  OrientationEventListener android.view  Surface android.view  TextureView android.view  View android.view  	displayId android.view.Display  rotation android.view.Display  ORIENTATION_UNKNOWN %android.view.OrientationEventListener  currentOrientation %android.view.OrientationEventListener  disable %android.view.OrientationEventListener  enable %android.view.OrientationEventListener  	listeners %android.view.OrientationEventListener  
ROTATION_0 android.view.Surface  ROTATION_180 android.view.Surface  ROTATION_270 android.view.Surface  ROTATION_90 android.view.Surface  release android.view.Surface  addOnAttachStateChangeListener android.view.TextureView  addOnLayoutChangeListener android.view.TextureView  context android.view.TextureView  display android.view.TextureView  setTransform android.view.TextureView  OnAttachStateChangeListener android.view.View  OnLayoutChangeListener android.view.View  addOnAttachStateChangeListener android.view.View  addOnLayoutChangeListener android.view.View  context android.view.View  display android.view.View  <SAM-CONSTRUCTOR> (android.view.View.OnLayoutChangeListener  OptIn androidx.annotation  
Camera2Config androidx.camera.camera2  
defaultConfig %androidx.camera.camera2.Camera2Config  CameraCharacteristicsCompat 'androidx.camera.camera2.internal.compat  toCameraCharacteristicsCompat Candroidx.camera.camera2.internal.compat.CameraCharacteristicsCompat  CamcorderProfileResolutionQuirk -androidx.camera.camera2.internal.compat.quirk  supportedResolutions Mandroidx.camera.camera2.internal.compat.quirk.CamcorderProfileResolutionQuirk  Camera2CameraInfo androidx.camera.camera2.interop  ExperimentalCamera2Interop androidx.camera.camera2.interop  CameraCharacteristics 1androidx.camera.camera2.interop.Camera2CameraInfo  LENS_FACING_BACK 1androidx.camera.camera2.interop.Camera2CameraInfo  PigeonSensorPosition 1androidx.camera.camera2.interop.Camera2CameraInfo  PigeonSensorType 1androidx.camera.camera2.interop.Camera2CameraInfo  Size35mm 1androidx.camera.camera2.interop.Camera2CameraInfo  any 1androidx.camera.camera2.interop.Camera2CameraInfo  bigger 1androidx.camera.camera2.interop.Camera2CameraInfo  cameraId 1androidx.camera.camera2.interop.Camera2CameraInfo  extractCameraCharacteristics 1androidx.camera.camera2.interop.Camera2CameraInfo  from 1androidx.camera.camera2.interop.Camera2CameraInfo  getCameraCharacteristic 1androidx.camera.camera2.interop.Camera2CameraInfo  Activity androidx.camera.core  
ActivityAware androidx.camera.core  ActivityCompat androidx.camera.core  ActivityPluginBinding androidx.camera.core  AnalysisImageConverter androidx.camera.core  AnalysisImageUtils androidx.camera.core  AndroidFocusSettings androidx.camera.core  AndroidVideoOptions androidx.camera.core  Any androidx.camera.core  AspectRatio androidx.camera.core  AspectRatioStrategy androidx.camera.core  BehaviorSubject androidx.camera.core  Bitmap androidx.camera.core  
BitmapFactory androidx.camera.core  Boolean androidx.camera.core  Build androidx.camera.core  CamcorderProfileResolutionQuirk androidx.camera.core  CamerAwesomePermission androidx.camera.core  Camera androidx.camera.core  Camera2CameraInfo androidx.camera.core  
Camera2Config androidx.camera.core  CameraCapabilities androidx.camera.core  CameraCharacteristics androidx.camera.core  CameraCharacteristicsCompat androidx.camera.core  
CameraControl androidx.camera.core  
CameraInfo androidx.camera.core  CameraInfoUnavailableException androidx.camera.core  CameraInterface androidx.camera.core  CameraPermissions androidx.camera.core  CameraSelector androidx.camera.core  
CameraXConfig androidx.camera.core  CameraXState androidx.camera.core  CamerawesomePlugin androidx.camera.core  CancellationTokenSource androidx.camera.core  Canvas androidx.camera.core  CaptureModes androidx.camera.core  ColorMatrixColorFilter androidx.camera.core  ConcurrentCamera androidx.camera.core  Consumer androidx.camera.core  
ContextCompat androidx.camera.core  CoroutineScope androidx.camera.core  CountDownTimer androidx.camera.core  
Deprecated androidx.camera.core  Dispatchers androidx.camera.core  
Disposable androidx.camera.core  Double androidx.camera.core  EventChannel androidx.camera.core  	Exception androidx.camera.core  Executor androidx.camera.core  
ExifInterface androidx.camera.core  ExifPreferences androidx.camera.core  ExperimentalCamera2Interop androidx.camera.core  
ExposureState androidx.camera.core  FallbackStrategy androidx.camera.core  File androidx.camera.core  FileOutputOptions androidx.camera.core  FileOutputStream androidx.camera.core  	FlashMode androidx.camera.core  Float androidx.camera.core  
FlutterPlugin androidx.camera.core  FlutterPluginBinding androidx.camera.core  FocusMeteringAction androidx.camera.core  FusedLocationProviderClient androidx.camera.core  Handler androidx.camera.core  IOException androidx.camera.core  IllegalStateException androidx.camera.core  
ImageAnalysis androidx.camera.core  ImageAnalysisBuilder androidx.camera.core  ImageCapture androidx.camera.core  ImageCaptureException androidx.camera.core  	ImageInfo androidx.camera.core  
ImageProxy androidx.camera.core  Int androidx.camera.core  Intent androidx.camera.core  LifecycleOwner androidx.camera.core  List androidx.camera.core  Location androidx.camera.core  LocationServices androidx.camera.core  Log androidx.camera.core  Long androidx.camera.core  Looper androidx.camera.core  Manifest androidx.camera.core  Map androidx.camera.core  	Messenger androidx.camera.core  
MeteringPoint androidx.camera.core  MeteringPointFactory androidx.camera.core  
MirrorMode androidx.camera.core  MutableList androidx.camera.core  
MutableMap androidx.camera.core  OrientationStreamListener androidx.camera.core  OutputImageFormat androidx.camera.core  PackageManager androidx.camera.core  Paint androidx.camera.core  PhysicalButtonMessageHandler androidx.camera.core  PhysicalButtonsHandler androidx.camera.core  PigeonSensor androidx.camera.core  PigeonSensorPosition androidx.camera.core  PigeonSensorTypeDevice androidx.camera.core  
PlayerService androidx.camera.core  Preview androidx.camera.core  PreviewSize androidx.camera.core  Priority androidx.camera.core  ProcessCameraProvider androidx.camera.core  Quality androidx.camera.core  QualityFallbackStrategy androidx.camera.core  QualitySelector androidx.camera.core  Rational androidx.camera.core  Recorder androidx.camera.core  	Recording androidx.camera.core  ResolutionInfo androidx.camera.core  ResolutionSelector androidx.camera.core  ResolutionStrategy androidx.camera.core  Result androidx.camera.core  SensorOrientation androidx.camera.core  SensorOrientationListener androidx.camera.core  Size androidx.camera.core  String androidx.camera.core  SuppressLint androidx.camera.core  Surface androidx.camera.core  #SurfaceOrientedMeteringPointFactory androidx.camera.core  SurfaceRequest androidx.camera.core  TODO androidx.camera.core  TextureRegistry androidx.camera.core  TimeUnit androidx.camera.core  Unit androidx.camera.core  UseCaseGroup androidx.camera.core  VideoCapture androidx.camera.core  VideoOptions androidx.camera.core  VideoRecordEvent androidx.camera.core  VideoRecordingQuality androidx.camera.core  ViewPort androidx.camera.core  	ZoomState androidx.camera.core  activity androidx.camera.core  all androidx.camera.core  apply androidx.camera.core  	buildList androidx.camera.core  cameraPermissions androidx.camera.core  cameraState androidx.camera.core  colorMatrix androidx.camera.core  	configure androidx.camera.core  configureInstance androidx.camera.core  exifPreferences androidx.camera.core  first androidx.camera.core  firstOrNull androidx.camera.core  	flashMode androidx.camera.core  forEach androidx.camera.core  getCameraLevel androidx.camera.core  getInstance androidx.camera.core  	getOrNull androidx.camera.core  getOrientedSize androidx.camera.core  isMultiCamSupported androidx.camera.core  
isNotEmpty androidx.camera.core  java androidx.camera.core  lastRecordedVideoSubscriptions androidx.camera.core  lastRecordedVideos androidx.camera.core  launch androidx.camera.core  listOf androidx.camera.core  	lowercase androidx.camera.core  map androidx.camera.core  
mapIndexed androidx.camera.core  
mapNotNull androidx.camera.core  	mapValues androidx.camera.core  
mutableListOf androidx.camera.core  mutableMapOf androidx.camera.core  
noneFilter androidx.camera.core  orientationStreamListener androidx.camera.core  rational androidx.camera.core  resume androidx.camera.core  retrieveLocation androidx.camera.core  
roundToInt androidx.camera.core  run androidx.camera.core  set androidx.camera.core  setUp androidx.camera.core  success androidx.camera.core  suspendCancellableCoroutine androidx.camera.core  
takePhotoWith androidx.camera.core  to androidx.camera.core  toFloatArray androidx.camera.core  toMap androidx.camera.core  toMutableMap androidx.camera.core  until androidx.camera.core  	uppercase androidx.camera.core  use androidx.camera.core  	withIndex androidx.camera.core  
RATIO_16_9  androidx.camera.core.AspectRatio  	RATIO_4_3  androidx.camera.core.AspectRatio  
cameraControl androidx.camera.core.Camera  
cameraInfo androidx.camera.core.Camera  enableTorch "androidx.camera.core.CameraControl  setExposureCompensationIndex "androidx.camera.core.CameraControl  
setLinearZoom "androidx.camera.core.CameraControl  startFocusAndMetering "androidx.camera.core.CameraControl  
exposureState androidx.camera.core.CameraInfo  let androidx.camera.core.CameraInfo  sensorRotationDegrees androidx.camera.core.CameraInfo  	zoomState androidx.camera.core.CameraInfo  DEFAULT_BACK_CAMERA #androidx.camera.core.CameraSelector  DEFAULT_FRONT_CAMERA #androidx.camera.core.CameraSelector  LENS_FACING_BACK #androidx.camera.core.CameraSelector  filter #androidx.camera.core.CameraSelector  Builder "androidx.camera.core.CameraXConfig  build *androidx.camera.core.CameraXConfig.Builder  
fromConfig *androidx.camera.core.CameraXConfig.Builder  setMinimumLoggingLevel *androidx.camera.core.CameraXConfig.Builder  SingleCameraConfig %androidx.camera.core.ConcurrentCamera  cameras %androidx.camera.core.ConcurrentCamera  	EventSink !androidx.camera.core.EventChannel  
StreamHandler !androidx.camera.core.EventChannel  exposureCompensationRange "androidx.camera.core.ExposureState  Builder (androidx.camera.core.FocusMeteringAction  FLAG_AE (androidx.camera.core.FocusMeteringAction  FLAG_AF (androidx.camera.core.FocusMeteringAction  FLAG_AWB (androidx.camera.core.FocusMeteringAction  TimeUnit 0androidx.camera.core.FocusMeteringAction.Builder  apply 0androidx.camera.core.FocusMeteringAction.Builder  build 0androidx.camera.core.FocusMeteringAction.Builder  disableAutoCancel 0androidx.camera.core.FocusMeteringAction.Builder  setAutoCancelDuration 0androidx.camera.core.FocusMeteringAction.Builder  Analyzer "androidx.camera.core.ImageAnalysis  Builder "androidx.camera.core.ImageAnalysis  OUTPUT_IMAGE_FORMAT_RGBA_8888 "androidx.camera.core.ImageAnalysis  OUTPUT_IMAGE_FORMAT_YUV_420_888 "androidx.camera.core.ImageAnalysis  STRATEGY_KEEP_ONLY_LATEST "androidx.camera.core.ImageAnalysis  setAnalyzer "androidx.camera.core.ImageAnalysis  targetRotation "androidx.camera.core.ImageAnalysis  <SAM-CONSTRUCTOR> +androidx.camera.core.ImageAnalysis.Analyzer  build *androidx.camera.core.ImageAnalysis.Builder  setBackpressureStrategy *androidx.camera.core.ImageAnalysis.Builder  setOutputImageFormat *androidx.camera.core.ImageAnalysis.Builder  setTargetResolution *androidx.camera.core.ImageAnalysis.Builder  Builder !androidx.camera.core.ImageCapture  FLASH_MODE_AUTO !androidx.camera.core.ImageCapture  FLASH_MODE_OFF !androidx.camera.core.ImageCapture  
FLASH_MODE_ON !androidx.camera.core.ImageCapture  Metadata !androidx.camera.core.ImageCapture  OnImageSavedCallback !androidx.camera.core.ImageCapture  OutputFileOptions !androidx.camera.core.ImageCapture  OutputFileResults !androidx.camera.core.ImageCapture  	flashMode !androidx.camera.core.ImageCapture  takePicture !androidx.camera.core.ImageCapture  targetRotation !androidx.camera.core.ImageCapture  	FlashMode )androidx.camera.core.ImageCapture.Builder  ImageCapture )androidx.camera.core.ImageCapture.Builder  apply )androidx.camera.core.ImageCapture.Builder  build )androidx.camera.core.ImageCapture.Builder  	flashMode )androidx.camera.core.ImageCapture.Builder  rational )androidx.camera.core.ImageCapture.Builder  setFlashMode )androidx.camera.core.ImageCapture.Builder  setResolutionSelector )androidx.camera.core.ImageCapture.Builder  isReversedHorizontal *androidx.camera.core.ImageCapture.Metadata  location *androidx.camera.core.ImageCapture.Metadata  Builder 3androidx.camera.core.ImageCapture.OutputFileOptions  metadata 3androidx.camera.core.ImageCapture.OutputFileOptions  build ;androidx.camera.core.ImageCapture.OutputFileOptions.Builder  setMetadata ;androidx.camera.core.ImageCapture.OutputFileOptions.Builder  savedUri 3androidx.camera.core.ImageCapture.OutputFileResults  rotationDegrees androidx.camera.core.ImageInfo  close androidx.camera.core.ImageProxy  cropRect androidx.camera.core.ImageProxy  height androidx.camera.core.ImageProxy  image androidx.camera.core.ImageProxy  	imageInfo androidx.camera.core.ImageProxy  width androidx.camera.core.ImageProxy  createPoint )androidx.camera.core.MeteringPointFactory  MIRROR_MODE_OFF androidx.camera.core.MirrorMode  MIRROR_MODE_ON_FRONT_ONLY androidx.camera.core.MirrorMode  Builder androidx.camera.core.Preview  SurfaceProvider androidx.camera.core.Preview  resolutionInfo androidx.camera.core.Preview  setSurfaceProvider androidx.camera.core.Preview  build $androidx.camera.core.Preview.Builder  
fromConfig $androidx.camera.core.Preview.Builder  setResolutionSelector $androidx.camera.core.Preview.Builder  setTargetAspectRatio $androidx.camera.core.Preview.Builder  
resolution #androidx.camera.core.ResolutionInfo  rotationDegrees #androidx.camera.core.ResolutionInfo  createPoint 8androidx.camera.core.SurfaceOrientedMeteringPointFactory  provideSurface #androidx.camera.core.SurfaceRequest  
resolution #androidx.camera.core.SurfaceRequest  SurfaceTextureEntry $androidx.camera.core.TextureRegistry  Builder !androidx.camera.core.UseCaseGroup  
addUseCase )androidx.camera.core.UseCaseGroup.Builder  build )androidx.camera.core.UseCaseGroup.Builder  setViewPort )androidx.camera.core.UseCaseGroup.Builder  Finalize %androidx.camera.core.VideoRecordEvent  Start %androidx.camera.core.VideoRecordEvent  Builder androidx.camera.core.ViewPort  build %androidx.camera.core.ViewPort.Builder  maxZoomRatio androidx.camera.core.ZoomState  minZoomRatio androidx.camera.core.ZoomState  
PreviewConfig androidx.camera.core.impl  	ImageUtil #androidx.camera.core.internal.utils  yuvImageToJpegByteArray -androidx.camera.core.internal.utils.ImageUtil  yuv_420_888toNv21 -androidx.camera.core.internal.utils.ImageUtil  AspectRatioStrategy 'androidx.camera.core.resolutionselector  ResolutionSelector 'androidx.camera.core.resolutionselector  ResolutionStrategy 'androidx.camera.core.resolutionselector  !RATIO_16_9_FALLBACK_AUTO_STRATEGY ;androidx.camera.core.resolutionselector.AspectRatioStrategy   RATIO_4_3_FALLBACK_AUTO_STRATEGY ;androidx.camera.core.resolutionselector.AspectRatioStrategy  Builder :androidx.camera.core.resolutionselector.ResolutionSelector  build Bandroidx.camera.core.resolutionselector.ResolutionSelector.Builder  setAspectRatioStrategy Bandroidx.camera.core.resolutionselector.ResolutionSelector.Builder  setResolutionStrategy Bandroidx.camera.core.resolutionselector.ResolutionSelector.Builder  HIGHEST_AVAILABLE_STRATEGY :androidx.camera.core.resolutionselector.ResolutionStrategy  ProcessCameraProvider androidx.camera.lifecycle  	Companion /androidx.camera.lifecycle.ProcessCameraProvider  availableCameraInfos /androidx.camera.lifecycle.ProcessCameraProvider  availableConcurrentCameraInfos /androidx.camera.lifecycle.ProcessCameraProvider  bindToLifecycle /androidx.camera.lifecycle.ProcessCameraProvider  configureInstance /androidx.camera.lifecycle.ProcessCameraProvider  getInstance /androidx.camera.lifecycle.ProcessCameraProvider  isMultiCamSupported /androidx.camera.lifecycle.ProcessCameraProvider  	unbindAll /androidx.camera.lifecycle.ProcessCameraProvider  configureInstance 9androidx.camera.lifecycle.ProcessCameraProvider.Companion  getInstance 9androidx.camera.lifecycle.ProcessCameraProvider.Companion  Activity androidx.camera.video  AndroidVideoOptions androidx.camera.video  Any androidx.camera.video  AspectRatio androidx.camera.video  AspectRatioStrategy androidx.camera.video  Boolean androidx.camera.video  CamcorderProfileResolutionQuirk androidx.camera.video  Camera androidx.camera.video  Camera2CameraInfo androidx.camera.video  CameraCapabilities androidx.camera.video  CameraCharacteristics androidx.camera.video  CameraCharacteristicsCompat androidx.camera.video  
CameraControl androidx.camera.video  
CameraInfo androidx.camera.video  CameraSelector androidx.camera.video  CameraXState androidx.camera.video  CamerawesomePlugin androidx.camera.video  CaptureModes androidx.camera.video  ConcurrentCamera androidx.camera.video  
ContextCompat androidx.camera.video  Double androidx.camera.video  EventChannel androidx.camera.video  	Exception androidx.camera.video  Executor androidx.camera.video  FallbackStrategy androidx.camera.video  FileOutputOptions androidx.camera.video  	FlashMode androidx.camera.video  Float androidx.camera.video  FocusMeteringAction androidx.camera.video  
ImageAnalysis androidx.camera.video  ImageAnalysisBuilder androidx.camera.video  ImageCapture androidx.camera.video  Int androidx.camera.video  LifecycleOwner androidx.camera.video  List androidx.camera.video  Log androidx.camera.video  Map androidx.camera.video  
MirrorMode androidx.camera.video  MutableList androidx.camera.video  
MutableMap androidx.camera.video  
OutputResults androidx.camera.video  PendingRecording androidx.camera.video  PigeonSensor androidx.camera.video  PigeonSensorPosition androidx.camera.video  Preview androidx.camera.video  ProcessCameraProvider androidx.camera.video  Quality androidx.camera.video  QualityFallbackStrategy androidx.camera.video  QualitySelector androidx.camera.video  Rational androidx.camera.video  Recorder androidx.camera.video  	Recording androidx.camera.video  ResolutionSelector androidx.camera.video  ResolutionStrategy androidx.camera.video  SensorOrientation androidx.camera.video  Size androidx.camera.video  String androidx.camera.video  SuppressLint androidx.camera.video  Surface androidx.camera.video  SurfaceRequest androidx.camera.video  TextureRegistry androidx.camera.video  Unit androidx.camera.video  UseCaseGroup androidx.camera.video  VideoCapture androidx.camera.video  VideoRecordEvent androidx.camera.video  VideoRecordingQuality androidx.camera.video  ViewPort androidx.camera.video  apply androidx.camera.video  first androidx.camera.video  	flashMode androidx.camera.video  forEach androidx.camera.video  getCameraLevel androidx.camera.video  isMultiCamSupported androidx.camera.video  map androidx.camera.video  
mutableListOf androidx.camera.video  mutableMapOf androidx.camera.video  rational androidx.camera.video  set androidx.camera.video  until androidx.camera.video  	withIndex androidx.camera.video  SingleCameraConfig &androidx.camera.video.ConcurrentCamera  	EventSink "androidx.camera.video.EventChannel  
StreamHandler "androidx.camera.video.EventChannel  higherQualityOrLowerThan &androidx.camera.video.FallbackStrategy  lowerQualityOrHigherThan &androidx.camera.video.FallbackStrategy  Builder 'androidx.camera.video.FileOutputOptions  build /androidx.camera.video.FileOutputOptions.Builder  	outputUri #androidx.camera.video.OutputResults  apply &androidx.camera.video.PendingRecording  cameraState &androidx.camera.video.PendingRecording  start &androidx.camera.video.PendingRecording  withAudioEnabled &androidx.camera.video.PendingRecording  SurfaceProvider androidx.camera.video.Preview  FHD androidx.camera.video.Quality  HD androidx.camera.video.Quality  HIGHEST androidx.camera.video.Quality  LOWEST androidx.camera.video.Quality  SD androidx.camera.video.Quality  UHD androidx.camera.video.Quality  from %androidx.camera.video.QualitySelector  getSupportedQualities %androidx.camera.video.QualitySelector  Builder androidx.camera.video.Recorder  prepareRecording androidx.camera.video.Recorder  build &androidx.camera.video.Recorder.Builder  setQualitySelector &androidx.camera.video.Recorder.Builder  setTargetVideoEncodingBitRate &androidx.camera.video.Recorder.Builder  close androidx.camera.video.Recording  isClosed androidx.camera.video.Recording  pause androidx.camera.video.Recording  resume androidx.camera.video.Recording  stop androidx.camera.video.Recording  SurfaceTextureEntry %androidx.camera.video.TextureRegistry  Builder "androidx.camera.video.VideoCapture  output "androidx.camera.video.VideoCapture  targetRotation "androidx.camera.video.VideoCapture  build *androidx.camera.video.VideoCapture.Builder  
setMirrorMode *androidx.camera.video.VideoCapture.Builder  Finalize &androidx.camera.video.VideoRecordEvent  Start &androidx.camera.video.VideoRecordEvent  error /androidx.camera.video.VideoRecordEvent.Finalize  hasError /androidx.camera.video.VideoRecordEvent.Finalize  
outputResults /androidx.camera.video.VideoRecordEvent.Finalize  ActivityCompat androidx.core.app  checkSelfPermission  androidx.core.app.ActivityCompat  requestPermissions  androidx.core.app.ActivityCompat  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  getMainExecutor #androidx.core.content.ContextCompat  Consumer androidx.core.util  <SAM-CONSTRUCTOR> androidx.core.util.Consumer  
ExifInterface androidx.exifinterface.media  saveAttributes *androidx.exifinterface.media.ExifInterface  
setGpsInfo *androidx.exifinterface.media.ExifInterface  LifecycleOwner androidx.lifecycle  LiveData androidx.lifecycle  value androidx.lifecycle.LiveData  VolumeProviderCompat androidx.media  Message #androidx.media.VolumeProviderCompat  PhysicalButtonsHandler #androidx.media.VolumeProviderCompat  VOLUME_CONTROL_RELATIVE #androidx.media.VolumeProviderCompat  apply #androidx.media.VolumeProviderCompat  	messenger #androidx.media.VolumeProviderCompat  Activity com.apparence.camerawesome  
ActivityAware com.apparence.camerawesome  ActivityCompat com.apparence.camerawesome  ActivityPluginBinding com.apparence.camerawesome  AnalysisImageConverter com.apparence.camerawesome  AnalysisImageUtils com.apparence.camerawesome  AndroidFocusSettings com.apparence.camerawesome  AspectRatio com.apparence.camerawesome  BehaviorSubject com.apparence.camerawesome  Bitmap com.apparence.camerawesome  
BitmapFactory com.apparence.camerawesome  Boolean com.apparence.camerawesome  Build com.apparence.camerawesome  CamerAwesomePermission com.apparence.camerawesome  
Camera2Config com.apparence.camerawesome  CameraCapabilities com.apparence.camerawesome  CameraCharacteristics com.apparence.camerawesome  CameraInfoUnavailableException com.apparence.camerawesome  CameraInterface com.apparence.camerawesome  CameraPermissions com.apparence.camerawesome  CameraSelector com.apparence.camerawesome  
CameraXConfig com.apparence.camerawesome  CameraXState com.apparence.camerawesome  CamerawesomePlugin com.apparence.camerawesome  CancellationTokenSource com.apparence.camerawesome  Canvas com.apparence.camerawesome  CaptureModes com.apparence.camerawesome  ColorMatrixColorFilter com.apparence.camerawesome  Consumer com.apparence.camerawesome  
ContextCompat com.apparence.camerawesome  CoroutineScope com.apparence.camerawesome  CountDownTimer com.apparence.camerawesome  
Deprecated com.apparence.camerawesome  Dispatchers com.apparence.camerawesome  
Disposable com.apparence.camerawesome  Double com.apparence.camerawesome  EventChannel com.apparence.camerawesome  	Exception com.apparence.camerawesome  
ExifInterface com.apparence.camerawesome  ExifPreferences com.apparence.camerawesome  ExperimentalCamera2Interop com.apparence.camerawesome  File com.apparence.camerawesome  FileOutputOptions com.apparence.camerawesome  FileOutputStream com.apparence.camerawesome  	FlashMode com.apparence.camerawesome  
FlutterPlugin com.apparence.camerawesome  FlutterPluginBinding com.apparence.camerawesome  FocusMeteringAction com.apparence.camerawesome  FusedLocationProviderClient com.apparence.camerawesome  Handler com.apparence.camerawesome  IOException com.apparence.camerawesome  IllegalStateException com.apparence.camerawesome  ImageAnalysisBuilder com.apparence.camerawesome  ImageCapture com.apparence.camerawesome  ImageCaptureException com.apparence.camerawesome  Int com.apparence.camerawesome  Intent com.apparence.camerawesome  List com.apparence.camerawesome  Location com.apparence.camerawesome  LocationServices com.apparence.camerawesome  Log com.apparence.camerawesome  Long com.apparence.camerawesome  Looper com.apparence.camerawesome  Manifest com.apparence.camerawesome  	Messenger com.apparence.camerawesome  MeteringPointFactory com.apparence.camerawesome  MutableList com.apparence.camerawesome  
MutableMap com.apparence.camerawesome  OrientationStreamListener com.apparence.camerawesome  OutputImageFormat com.apparence.camerawesome  PackageManager com.apparence.camerawesome  Paint com.apparence.camerawesome  PhysicalButtonMessageHandler com.apparence.camerawesome  PhysicalButtonsHandler com.apparence.camerawesome  PigeonSensor com.apparence.camerawesome  PigeonSensorPosition com.apparence.camerawesome  PigeonSensorTypeDevice com.apparence.camerawesome  
PlayerService com.apparence.camerawesome  PreviewSize com.apparence.camerawesome  Priority com.apparence.camerawesome  ProcessCameraProvider com.apparence.camerawesome  Rational com.apparence.camerawesome  Result com.apparence.camerawesome  SensorOrientationListener com.apparence.camerawesome  Size com.apparence.camerawesome  String com.apparence.camerawesome  SuppressLint com.apparence.camerawesome  #SurfaceOrientedMeteringPointFactory com.apparence.camerawesome  TODO com.apparence.camerawesome  TextureRegistry com.apparence.camerawesome  TimeUnit com.apparence.camerawesome  Unit com.apparence.camerawesome  VideoOptions com.apparence.camerawesome  VideoRecordEvent com.apparence.camerawesome  activity com.apparence.camerawesome  all com.apparence.camerawesome  apply com.apparence.camerawesome  	buildList com.apparence.camerawesome  cameraPermissions com.apparence.camerawesome  cameraState com.apparence.camerawesome  colorMatrix com.apparence.camerawesome  	configure com.apparence.camerawesome  configureInstance com.apparence.camerawesome  exifPreferences com.apparence.camerawesome  first com.apparence.camerawesome  firstOrNull com.apparence.camerawesome  forEach com.apparence.camerawesome  getCameraLevel com.apparence.camerawesome  getInstance com.apparence.camerawesome  	getOrNull com.apparence.camerawesome  getOrientedSize com.apparence.camerawesome  isMultiCamSupported com.apparence.camerawesome  
isNotEmpty com.apparence.camerawesome  java com.apparence.camerawesome  lastRecordedVideoSubscriptions com.apparence.camerawesome  lastRecordedVideos com.apparence.camerawesome  launch com.apparence.camerawesome  listOf com.apparence.camerawesome  	lowercase com.apparence.camerawesome  map com.apparence.camerawesome  
mapIndexed com.apparence.camerawesome  
mapNotNull com.apparence.camerawesome  	mapValues com.apparence.camerawesome  
mutableListOf com.apparence.camerawesome  
noneFilter com.apparence.camerawesome  orientationStreamListener com.apparence.camerawesome  resume com.apparence.camerawesome  retrieveLocation com.apparence.camerawesome  
roundToInt com.apparence.camerawesome  run com.apparence.camerawesome  set com.apparence.camerawesome  setUp com.apparence.camerawesome  success com.apparence.camerawesome  suspendCancellableCoroutine com.apparence.camerawesome  
takePhotoWith com.apparence.camerawesome  to com.apparence.camerawesome  toFloatArray com.apparence.camerawesome  toMap com.apparence.camerawesome  toMutableMap com.apparence.camerawesome  until com.apparence.camerawesome  	uppercase com.apparence.camerawesome  use com.apparence.camerawesome  	withIndex com.apparence.camerawesome  TAG -com.apparence.camerawesome.CamerawesomePlugin  OnImageSavedCallback 'com.apparence.camerawesome.ImageCapture  OutputFileResults 'com.apparence.camerawesome.ImageCapture  Finalize +com.apparence.camerawesome.VideoRecordEvent  Start +com.apparence.camerawesome.VideoRecordEvent  Any "com.apparence.camerawesome.buttons  Build "com.apparence.camerawesome.buttons  EventChannel "com.apparence.camerawesome.buttons  Handler "com.apparence.camerawesome.buttons  IBinder "com.apparence.camerawesome.buttons  Int "com.apparence.camerawesome.buttons  Intent "com.apparence.camerawesome.buttons  Looper "com.apparence.camerawesome.buttons  MediaSessionCompat "com.apparence.camerawesome.buttons  Message "com.apparence.camerawesome.buttons  	Messenger "com.apparence.camerawesome.buttons  PhysicalButtonMessageHandler "com.apparence.camerawesome.buttons  PhysicalButtonsHandler "com.apparence.camerawesome.buttons  PlaybackStateCompat "com.apparence.camerawesome.buttons  
PlayerService "com.apparence.camerawesome.buttons  Service "com.apparence.camerawesome.buttons  VOLUME_DOWN "com.apparence.camerawesome.buttons  	VOLUME_UP "com.apparence.camerawesome.buttons  VolumeProviderCompat "com.apparence.camerawesome.buttons  apply "com.apparence.camerawesome.buttons  java "com.apparence.camerawesome.buttons  	messenger "com.apparence.camerawesome.buttons  	EventSink /com.apparence.camerawesome.buttons.EventChannel  
StreamHandler /com.apparence.camerawesome.buttons.EventChannel  buttonsHandler ?com.apparence.camerawesome.buttons.PhysicalButtonMessageHandler  Any 9com.apparence.camerawesome.buttons.PhysicalButtonsHandler  BROADCAST_VOLUME_BUTTONS 9com.apparence.camerawesome.buttons.PhysicalButtonsHandler  	Companion 9com.apparence.camerawesome.buttons.PhysicalButtonsHandler  EventChannel 9com.apparence.camerawesome.buttons.PhysicalButtonsHandler  Int 9com.apparence.camerawesome.buttons.PhysicalButtonsHandler  VOLUME_DOWN 9com.apparence.camerawesome.buttons.PhysicalButtonsHandler  	VOLUME_UP 9com.apparence.camerawesome.buttons.PhysicalButtonsHandler  
buttonPressed 9com.apparence.camerawesome.buttons.PhysicalButtonsHandler  sink 9com.apparence.camerawesome.buttons.PhysicalButtonsHandler  BROADCAST_VOLUME_BUTTONS Ccom.apparence.camerawesome.buttons.PhysicalButtonsHandler.Companion  VOLUME_DOWN Ccom.apparence.camerawesome.buttons.PhysicalButtonsHandler.Companion  	VOLUME_UP Ccom.apparence.camerawesome.buttons.PhysicalButtonsHandler.Companion  	EventSink Fcom.apparence.camerawesome.buttons.PhysicalButtonsHandler.EventChannel  Build 0com.apparence.camerawesome.buttons.PlayerService  MediaSessionCompat 0com.apparence.camerawesome.buttons.PlayerService  Message 0com.apparence.camerawesome.buttons.PlayerService  	Messenger 0com.apparence.camerawesome.buttons.PlayerService  PhysicalButtonsHandler 0com.apparence.camerawesome.buttons.PlayerService  PlaybackStateCompat 0com.apparence.camerawesome.buttons.PlayerService  apply 0com.apparence.camerawesome.buttons.PlayerService  java 0com.apparence.camerawesome.buttons.PlayerService  mediaSession 0com.apparence.camerawesome.buttons.PlayerService  	messenger 0com.apparence.camerawesome.buttons.PlayerService  Activity "com.apparence.camerawesome.cameraX  
ActivityAware "com.apparence.camerawesome.cameraX  ActivityCompat "com.apparence.camerawesome.cameraX  ActivityPluginBinding "com.apparence.camerawesome.cameraX  AnalysisImageConverter "com.apparence.camerawesome.cameraX  AnalysisImageFormat "com.apparence.camerawesome.cameraX  AnalysisImageUtils "com.apparence.camerawesome.cameraX  AnalysisImageUtilsCodec "com.apparence.camerawesome.cameraX  AnalysisImageWrapper "com.apparence.camerawesome.cameraX  AnalysisRotation "com.apparence.camerawesome.cameraX  AndroidFocusSettings "com.apparence.camerawesome.cameraX  AndroidVideoOptions "com.apparence.camerawesome.cameraX  Any "com.apparence.camerawesome.cameraX  Array "com.apparence.camerawesome.cameraX  	ArrayList "com.apparence.camerawesome.cameraX  AspectRatio "com.apparence.camerawesome.cameraX  AspectRatioStrategy "com.apparence.camerawesome.cameraX  AutoFitPreviewBuilder "com.apparence.camerawesome.cameraX  BasicMessageChannel "com.apparence.camerawesome.cameraX  BehaviorSubject "com.apparence.camerawesome.cameraX  BinaryMessenger "com.apparence.camerawesome.cameraX  Bitmap "com.apparence.camerawesome.cameraX  
BitmapFactory "com.apparence.camerawesome.cameraX  Boolean "com.apparence.camerawesome.cameraX  Build "com.apparence.camerawesome.cameraX  Byte "com.apparence.camerawesome.cameraX  	ByteArray "com.apparence.camerawesome.cameraX  ByteArrayOutputStream "com.apparence.camerawesome.cameraX  
ByteBuffer "com.apparence.camerawesome.cameraX  CamcorderProfileResolutionQuirk "com.apparence.camerawesome.cameraX  CamerAwesomePermission "com.apparence.camerawesome.cameraX  Camera "com.apparence.camerawesome.cameraX  Camera2CameraInfo "com.apparence.camerawesome.cameraX  
Camera2Config "com.apparence.camerawesome.cameraX  CameraAwesomeX "com.apparence.camerawesome.cameraX  CameraCapabilities "com.apparence.camerawesome.cameraX  CameraCharacteristics "com.apparence.camerawesome.cameraX  CameraCharacteristicsCompat "com.apparence.camerawesome.cameraX  
CameraControl "com.apparence.camerawesome.cameraX  
CameraInfo "com.apparence.camerawesome.cameraX  CameraInfoUnavailableException "com.apparence.camerawesome.cameraX  CameraInterface "com.apparence.camerawesome.cameraX  CameraInterfaceCodec "com.apparence.camerawesome.cameraX  CameraPermissions "com.apparence.camerawesome.cameraX  CameraSelector "com.apparence.camerawesome.cameraX  
CameraXConfig "com.apparence.camerawesome.cameraX  CameraXState "com.apparence.camerawesome.cameraX  CamerawesomePlugin "com.apparence.camerawesome.cameraX  CancellationTokenSource "com.apparence.camerawesome.cameraX  Canvas "com.apparence.camerawesome.cameraX  CaptureModes "com.apparence.camerawesome.cameraX  ColorMatrixColorFilter "com.apparence.camerawesome.cameraX  ConcurrentCamera "com.apparence.camerawesome.cameraX  Consumer "com.apparence.camerawesome.cameraX  Context "com.apparence.camerawesome.cameraX  
ContextCompat "com.apparence.camerawesome.cameraX  Continuation "com.apparence.camerawesome.cameraX  CoroutineScope "com.apparence.camerawesome.cameraX  CountDownTimer "com.apparence.camerawesome.cameraX  CropRectWrapper "com.apparence.camerawesome.cameraX  CupertinoCodecType "com.apparence.camerawesome.cameraX  CupertinoFileType "com.apparence.camerawesome.cameraX  CupertinoVideoOptions "com.apparence.camerawesome.cameraX  
Deprecated "com.apparence.camerawesome.cameraX  Dispatchers "com.apparence.camerawesome.cameraX  Display "com.apparence.camerawesome.cameraX  DisplayManager "com.apparence.camerawesome.cameraX  
Disposable "com.apparence.camerawesome.cameraX  Double "com.apparence.camerawesome.cameraX  EventChannel "com.apparence.camerawesome.cameraX  	EventSink "com.apparence.camerawesome.cameraX  	Exception "com.apparence.camerawesome.cameraX  Executor "com.apparence.camerawesome.cameraX  
ExifInterface "com.apparence.camerawesome.cameraX  ExifPreferences "com.apparence.camerawesome.cameraX  ExperimentalCamera2Interop "com.apparence.camerawesome.cameraX  FallbackStrategy "com.apparence.camerawesome.cameraX  File "com.apparence.camerawesome.cameraX  FileOutputOptions "com.apparence.camerawesome.cameraX  FileOutputStream "com.apparence.camerawesome.cameraX  	FlashMode "com.apparence.camerawesome.cameraX  Float "com.apparence.camerawesome.cameraX  FlutterError "com.apparence.camerawesome.cameraX  
FlutterPlugin "com.apparence.camerawesome.cameraX  FlutterPluginBinding "com.apparence.camerawesome.cameraX  FocusMeteringAction "com.apparence.camerawesome.cameraX  FusedLocationProviderClient "com.apparence.camerawesome.cameraX  Handler "com.apparence.camerawesome.cameraX  IOException "com.apparence.camerawesome.cameraX  IllegalArgumentException "com.apparence.camerawesome.cameraX  IllegalStateException "com.apparence.camerawesome.cameraX  
ImageAnalysis "com.apparence.camerawesome.cameraX  ImageAnalysisBuilder "com.apparence.camerawesome.cameraX  ImageCapture "com.apparence.camerawesome.cameraX  ImageCaptureException "com.apparence.camerawesome.cameraX  ImageFormat "com.apparence.camerawesome.cameraX  
ImageProxy "com.apparence.camerawesome.cameraX  	ImageUtil "com.apparence.camerawesome.cameraX  Int "com.apparence.camerawesome.cameraX  IntArray "com.apparence.camerawesome.cameraX  Intent "com.apparence.camerawesome.cameraX  LifecycleOwner "com.apparence.camerawesome.cameraX  List "com.apparence.camerawesome.cameraX  Location "com.apparence.camerawesome.cameraX  LocationServices "com.apparence.camerawesome.cameraX  Log "com.apparence.camerawesome.cameraX  Long "com.apparence.camerawesome.cameraX  Looper "com.apparence.camerawesome.cameraX  Manifest "com.apparence.camerawesome.cameraX  Map "com.apparence.camerawesome.cameraX  Math "com.apparence.camerawesome.cameraX  Matrix "com.apparence.camerawesome.cameraX  MessageCodec "com.apparence.camerawesome.cameraX  	Messenger "com.apparence.camerawesome.cameraX  MeteringPointFactory "com.apparence.camerawesome.cameraX  
MirrorMode "com.apparence.camerawesome.cameraX  MutableList "com.apparence.camerawesome.cameraX  
MutableMap "com.apparence.camerawesome.cameraX  ORIENTATION_UNKNOWN "com.apparence.camerawesome.cameraX  Objects "com.apparence.camerawesome.cameraX  OrientationEventListener "com.apparence.camerawesome.cameraX  OrientationStreamListener "com.apparence.camerawesome.cameraX  OutputImageFormat "com.apparence.camerawesome.cameraX  PERMISSIONS_MULTIPLE_REQUEST "com.apparence.camerawesome.cameraX  PackageManager "com.apparence.camerawesome.cameraX  Paint "com.apparence.camerawesome.cameraX  PermissionNotDeclaredException "com.apparence.camerawesome.cameraX  PermissionRequest "com.apparence.camerawesome.cameraX  PhysicalButtonMessageHandler "com.apparence.camerawesome.cameraX  PhysicalButtonsHandler "com.apparence.camerawesome.cameraX  PigeonSensor "com.apparence.camerawesome.cameraX  PigeonSensorPosition "com.apparence.camerawesome.cameraX  PigeonSensorType "com.apparence.camerawesome.cameraX  PigeonSensorTypeDevice "com.apparence.camerawesome.cameraX  PlaneWrapper "com.apparence.camerawesome.cameraX  
PlayerService "com.apparence.camerawesome.cameraX  Preview "com.apparence.camerawesome.cameraX  
PreviewConfig "com.apparence.camerawesome.cameraX  PreviewSize "com.apparence.camerawesome.cameraX  Priority "com.apparence.camerawesome.cameraX  ProcessCameraProvider "com.apparence.camerawesome.cameraX  Quality "com.apparence.camerawesome.cameraX  QualityFallbackStrategy "com.apparence.camerawesome.cameraX  QualitySelector "com.apparence.camerawesome.cameraX  Rational "com.apparence.camerawesome.cameraX  Recorder "com.apparence.camerawesome.cameraX  	Recording "com.apparence.camerawesome.cameraX  Rect "com.apparence.camerawesome.cameraX   RequestPermissionsResultListener "com.apparence.camerawesome.cameraX  ResettableCountDownLatch "com.apparence.camerawesome.cameraX  ResolutionSelector "com.apparence.camerawesome.cameraX  ResolutionStrategy "com.apparence.camerawesome.cameraX  Result "com.apparence.camerawesome.cameraX  SensorOrientation "com.apparence.camerawesome.cameraX  SensorOrientationListener "com.apparence.camerawesome.cameraX  Size "com.apparence.camerawesome.cameraX  StandardMessageCodec "com.apparence.camerawesome.cameraX  String "com.apparence.camerawesome.cameraX  Suppress "com.apparence.camerawesome.cameraX  SuppressLint "com.apparence.camerawesome.cameraX  Surface "com.apparence.camerawesome.cameraX  #SurfaceOrientedMeteringPointFactory "com.apparence.camerawesome.cameraX  SurfaceRequest "com.apparence.camerawesome.cameraX  System "com.apparence.camerawesome.cameraX  TAG "com.apparence.camerawesome.cameraX  TODO "com.apparence.camerawesome.cameraX  TextureRegistry "com.apparence.camerawesome.cameraX  TextureView "com.apparence.camerawesome.cameraX  	Throwable "com.apparence.camerawesome.cameraX  TimeUnit "com.apparence.camerawesome.cameraX  UUID "com.apparence.camerawesome.cameraX  Unit "com.apparence.camerawesome.cameraX  UseCaseGroup "com.apparence.camerawesome.cameraX  VideoCapture "com.apparence.camerawesome.cameraX  VideoOptions "com.apparence.camerawesome.cameraX  VideoRecordEvent "com.apparence.camerawesome.cameraX  VideoRecordingQuality "com.apparence.camerawesome.cameraX  View "com.apparence.camerawesome.cameraX  ViewPort "com.apparence.camerawesome.cameraX  
WeakReference "com.apparence.camerawesome.cameraX  YuvImage "com.apparence.camerawesome.cameraX  activity "com.apparence.camerawesome.cameraX  all "com.apparence.camerawesome.cameraX  allPermissions "com.apparence.camerawesome.cameraX  android "com.apparence.camerawesome.cameraX  androidx "com.apparence.camerawesome.cameraX  apply "com.apparence.camerawesome.cameraX  
asExecutor "com.apparence.camerawesome.cameraX  bufferDimens "com.apparence.camerawesome.cameraX  	buildList "com.apparence.camerawesome.cameraX  cameraPermissions "com.apparence.camerawesome.cameraX  cameraState "com.apparence.camerawesome.cameraX  colorMatrix "com.apparence.camerawesome.cameraX  	configure "com.apparence.camerawesome.cameraX  configureInstance "com.apparence.camerawesome.cameraX  countDownLatch "com.apparence.camerawesome.cameraX  currentOrientation "com.apparence.camerawesome.cameraX  delay "com.apparence.camerawesome.cameraX  displayListener "com.apparence.camerawesome.cameraX  displayManager "com.apparence.camerawesome.cameraX  exifPreferences "com.apparence.camerawesome.cameraX  failure "com.apparence.camerawesome.cameraX  first "com.apparence.camerawesome.cameraX  firstOrNull "com.apparence.camerawesome.cameraX  	flashMode "com.apparence.camerawesome.cameraX  forEach "com.apparence.camerawesome.cameraX  fromList "com.apparence.camerawesome.cameraX  getCameraLevel "com.apparence.camerawesome.cameraX  getDisplaySurfaceRotation "com.apparence.camerawesome.cameraX  getInstance "com.apparence.camerawesome.cameraX  	getOrNull "com.apparence.camerawesome.cameraX  getOrientedSize "com.apparence.camerawesome.cameraX  getValue "com.apparence.camerawesome.cameraX  indices "com.apparence.camerawesome.cameraX  isMultiCamSupported "com.apparence.camerawesome.cameraX  
isNotEmpty "com.apparence.camerawesome.cameraX  
isNullOrEmpty "com.apparence.camerawesome.cameraX  java "com.apparence.camerawesome.cameraX  	javaClass "com.apparence.camerawesome.cameraX  lastImageEmittedTimeStamp "com.apparence.camerawesome.cameraX  lastRecordedVideoSubscriptions "com.apparence.camerawesome.cameraX  lastRecordedVideos "com.apparence.camerawesome.cameraX  launch "com.apparence.camerawesome.cameraX  lazy "com.apparence.camerawesome.cameraX  let "com.apparence.camerawesome.cameraX  listOf "com.apparence.camerawesome.cameraX  	listeners "com.apparence.camerawesome.cameraX  	lowercase "com.apparence.camerawesome.cameraX  map "com.apparence.camerawesome.cameraX  
mapIndexed "com.apparence.camerawesome.cameraX  
mapNotNull "com.apparence.camerawesome.cameraX  mapOf "com.apparence.camerawesome.cameraX  	mapValues "com.apparence.camerawesome.cameraX  maxFramesPerSecond "com.apparence.camerawesome.cameraX  min "com.apparence.camerawesome.cameraX  
mutableListOf "com.apparence.camerawesome.cameraX  mutableMapOf "com.apparence.camerawesome.cameraX  
noneFilter "com.apparence.camerawesome.cameraX  ofRaw "com.apparence.camerawesome.cameraX  	onFailure "com.apparence.camerawesome.cameraX  	onSuccess "com.apparence.camerawesome.cameraX  orientationStreamListener "com.apparence.camerawesome.cameraX  
plusAssign "com.apparence.camerawesome.cameraX  provideDelegate "com.apparence.camerawesome.cameraX  rational "com.apparence.camerawesome.cameraX  requestPermissions "com.apparence.camerawesome.cameraX  resume "com.apparence.camerawesome.cameraX  retrieveLocation "com.apparence.camerawesome.cameraX  
roundToInt "com.apparence.camerawesome.cameraX  roundToLong "com.apparence.camerawesome.cameraX  run "com.apparence.camerawesome.cameraX  set "com.apparence.camerawesome.cameraX  setUp "com.apparence.camerawesome.cameraX  success "com.apparence.camerawesome.cameraX  suspendCancellableCoroutine "com.apparence.camerawesome.cameraX  suspendCoroutine "com.apparence.camerawesome.cameraX  
takePhotoWith "com.apparence.camerawesome.cameraX  to "com.apparence.camerawesome.cameraX  toFloatArray "com.apparence.camerawesome.cameraX  toList "com.apparence.camerawesome.cameraX  toMap "com.apparence.camerawesome.cameraX  toMutableMap "com.apparence.camerawesome.cameraX  toTypedArray "com.apparence.camerawesome.cameraX  until "com.apparence.camerawesome.cameraX  updateTransform "com.apparence.camerawesome.cameraX  	uppercase "com.apparence.camerawesome.cameraX  use "com.apparence.camerawesome.cameraX  values "com.apparence.camerawesome.cameraX  viewFinderDimens "com.apparence.camerawesome.cameraX  viewFinderDisplay "com.apparence.camerawesome.cameraX  	withIndex "com.apparence.camerawesome.cameraX  	wrapError "com.apparence.camerawesome.cameraX  
wrapResult "com.apparence.camerawesome.cameraX  AnalysisImageFormat 9com.apparence.camerawesome.cameraX.AnalysisImageConverter  AnalysisImageWrapper 9com.apparence.camerawesome.cameraX.AnalysisImageConverter  	ByteArray 9com.apparence.camerawesome.cameraX.AnalysisImageConverter  ByteArrayOutputStream 9com.apparence.camerawesome.cameraX.AnalysisImageConverter  
ByteBuffer 9com.apparence.camerawesome.cameraX.AnalysisImageConverter  	Exception 9com.apparence.camerawesome.cameraX.AnalysisImageConverter  ImageFormat 9com.apparence.camerawesome.cameraX.AnalysisImageConverter  IntArray 9com.apparence.camerawesome.cameraX.AnalysisImageConverter  Math 9com.apparence.camerawesome.cameraX.AnalysisImageConverter  Rect 9com.apparence.camerawesome.cameraX.AnalysisImageConverter  Result 9com.apparence.camerawesome.cameraX.AnalysisImageConverter  YuvImage 9com.apparence.camerawesome.cameraX.AnalysisImageConverter  android 9com.apparence.camerawesome.cameraX.AnalysisImageConverter  failure 9com.apparence.camerawesome.cameraX.AnalysisImageConverter  min 9com.apparence.camerawesome.cameraX.AnalysisImageConverter  
nv21toJpeg 9com.apparence.camerawesome.cameraX.AnalysisImageConverter  	onFailure 9com.apparence.camerawesome.cameraX.AnalysisImageConverter  	onSuccess 9com.apparence.camerawesome.cameraX.AnalysisImageConverter  
plusAssign 9com.apparence.camerawesome.cameraX.AnalysisImageConverter  success 9com.apparence.camerawesome.cameraX.AnalysisImageConverter  until 9com.apparence.camerawesome.cameraX.AnalysisImageConverter  yuv420toNv21 9com.apparence.camerawesome.cameraX.AnalysisImageConverter  AnalysisImageFormat 6com.apparence.camerawesome.cameraX.AnalysisImageFormat  	Companion 6com.apparence.camerawesome.cameraX.AnalysisImageFormat  Int 6com.apparence.camerawesome.cameraX.AnalysisImageFormat  JPEG 6com.apparence.camerawesome.cameraX.AnalysisImageFormat  NV21 6com.apparence.camerawesome.cameraX.AnalysisImageFormat  firstOrNull 6com.apparence.camerawesome.cameraX.AnalysisImageFormat  ofRaw 6com.apparence.camerawesome.cameraX.AnalysisImageFormat  raw 6com.apparence.camerawesome.cameraX.AnalysisImageFormat  values 6com.apparence.camerawesome.cameraX.AnalysisImageFormat  firstOrNull @com.apparence.camerawesome.cameraX.AnalysisImageFormat.Companion  ofRaw @com.apparence.camerawesome.cameraX.AnalysisImageFormat.Companion  values @com.apparence.camerawesome.cameraX.AnalysisImageFormat.Companion  AnalysisImageUtils 5com.apparence.camerawesome.cameraX.AnalysisImageUtils  AnalysisImageUtilsCodec 5com.apparence.camerawesome.cameraX.AnalysisImageUtils  AnalysisImageWrapper 5com.apparence.camerawesome.cameraX.AnalysisImageUtils  Any 5com.apparence.camerawesome.cameraX.AnalysisImageUtils  BasicMessageChannel 5com.apparence.camerawesome.cameraX.AnalysisImageUtils  BinaryMessenger 5com.apparence.camerawesome.cameraX.AnalysisImageUtils  	Companion 5com.apparence.camerawesome.cameraX.AnalysisImageUtils  Int 5com.apparence.camerawesome.cameraX.AnalysisImageUtils  List 5com.apparence.camerawesome.cameraX.AnalysisImageUtils  Long 5com.apparence.camerawesome.cameraX.AnalysisImageUtils  MessageCodec 5com.apparence.camerawesome.cameraX.AnalysisImageUtils  Result 5com.apparence.camerawesome.cameraX.AnalysisImageUtils  Suppress 5com.apparence.camerawesome.cameraX.AnalysisImageUtils  Unit 5com.apparence.camerawesome.cameraX.AnalysisImageUtils  bgra8888toJpeg 5com.apparence.camerawesome.cameraX.AnalysisImageUtils  codec 5com.apparence.camerawesome.cameraX.AnalysisImageUtils  getValue 5com.apparence.camerawesome.cameraX.AnalysisImageUtils  lazy 5com.apparence.camerawesome.cameraX.AnalysisImageUtils  let 5com.apparence.camerawesome.cameraX.AnalysisImageUtils  
nv21toJpeg 5com.apparence.camerawesome.cameraX.AnalysisImageUtils  provideDelegate 5com.apparence.camerawesome.cameraX.AnalysisImageUtils  run 5com.apparence.camerawesome.cameraX.AnalysisImageUtils  setUp 5com.apparence.camerawesome.cameraX.AnalysisImageUtils  	wrapError 5com.apparence.camerawesome.cameraX.AnalysisImageUtils  
wrapResult 5com.apparence.camerawesome.cameraX.AnalysisImageUtils  yuv420toJpeg 5com.apparence.camerawesome.cameraX.AnalysisImageUtils  yuv420toNv21 5com.apparence.camerawesome.cameraX.AnalysisImageUtils  AnalysisImageUtilsCodec ?com.apparence.camerawesome.cameraX.AnalysisImageUtils.Companion  BasicMessageChannel ?com.apparence.camerawesome.cameraX.AnalysisImageUtils.Companion  codec ?com.apparence.camerawesome.cameraX.AnalysisImageUtils.Companion  getValue ?com.apparence.camerawesome.cameraX.AnalysisImageUtils.Companion  lazy ?com.apparence.camerawesome.cameraX.AnalysisImageUtils.Companion  let ?com.apparence.camerawesome.cameraX.AnalysisImageUtils.Companion  provideDelegate ?com.apparence.camerawesome.cameraX.AnalysisImageUtils.Companion  run ?com.apparence.camerawesome.cameraX.AnalysisImageUtils.Companion  setUp ?com.apparence.camerawesome.cameraX.AnalysisImageUtils.Companion  	wrapError ?com.apparence.camerawesome.cameraX.AnalysisImageUtils.Companion  
wrapResult ?com.apparence.camerawesome.cameraX.AnalysisImageUtils.Companion  AnalysisImageWrapper :com.apparence.camerawesome.cameraX.AnalysisImageUtilsCodec  CropRectWrapper :com.apparence.camerawesome.cameraX.AnalysisImageUtilsCodec  PlaneWrapper :com.apparence.camerawesome.cameraX.AnalysisImageUtilsCodec  fromList :com.apparence.camerawesome.cameraX.AnalysisImageUtilsCodec  let :com.apparence.camerawesome.cameraX.AnalysisImageUtilsCodec  	readValue :com.apparence.camerawesome.cameraX.AnalysisImageUtilsCodec  
writeValue :com.apparence.camerawesome.cameraX.AnalysisImageUtilsCodec  AnalysisImageFormat 7com.apparence.camerawesome.cameraX.AnalysisImageWrapper  AnalysisImageWrapper 7com.apparence.camerawesome.cameraX.AnalysisImageWrapper  AnalysisRotation 7com.apparence.camerawesome.cameraX.AnalysisImageWrapper  Any 7com.apparence.camerawesome.cameraX.AnalysisImageWrapper  	ByteArray 7com.apparence.camerawesome.cameraX.AnalysisImageWrapper  	Companion 7com.apparence.camerawesome.cameraX.AnalysisImageWrapper  CropRectWrapper 7com.apparence.camerawesome.cameraX.AnalysisImageWrapper  Int 7com.apparence.camerawesome.cameraX.AnalysisImageWrapper  List 7com.apparence.camerawesome.cameraX.AnalysisImageWrapper  Long 7com.apparence.camerawesome.cameraX.AnalysisImageWrapper  PlaneWrapper 7com.apparence.camerawesome.cameraX.AnalysisImageWrapper  Suppress 7com.apparence.camerawesome.cameraX.AnalysisImageWrapper  bytes 7com.apparence.camerawesome.cameraX.AnalysisImageWrapper  cropRect 7com.apparence.camerawesome.cameraX.AnalysisImageWrapper  format 7com.apparence.camerawesome.cameraX.AnalysisImageWrapper  fromList 7com.apparence.camerawesome.cameraX.AnalysisImageWrapper  height 7com.apparence.camerawesome.cameraX.AnalysisImageWrapper  let 7com.apparence.camerawesome.cameraX.AnalysisImageWrapper  listOf 7com.apparence.camerawesome.cameraX.AnalysisImageWrapper  ofRaw 7com.apparence.camerawesome.cameraX.AnalysisImageWrapper  planes 7com.apparence.camerawesome.cameraX.AnalysisImageWrapper  rotation 7com.apparence.camerawesome.cameraX.AnalysisImageWrapper  toList 7com.apparence.camerawesome.cameraX.AnalysisImageWrapper  width 7com.apparence.camerawesome.cameraX.AnalysisImageWrapper  AnalysisImageFormat Acom.apparence.camerawesome.cameraX.AnalysisImageWrapper.Companion  AnalysisImageWrapper Acom.apparence.camerawesome.cameraX.AnalysisImageWrapper.Companion  AnalysisRotation Acom.apparence.camerawesome.cameraX.AnalysisImageWrapper.Companion  CropRectWrapper Acom.apparence.camerawesome.cameraX.AnalysisImageWrapper.Companion  fromList Acom.apparence.camerawesome.cameraX.AnalysisImageWrapper.Companion  let Acom.apparence.camerawesome.cameraX.AnalysisImageWrapper.Companion  listOf Acom.apparence.camerawesome.cameraX.AnalysisImageWrapper.Companion  ofRaw Acom.apparence.camerawesome.cameraX.AnalysisImageWrapper.Companion  AnalysisRotation 3com.apparence.camerawesome.cameraX.AnalysisRotation  	Companion 3com.apparence.camerawesome.cameraX.AnalysisRotation  Int 3com.apparence.camerawesome.cameraX.AnalysisRotation  firstOrNull 3com.apparence.camerawesome.cameraX.AnalysisRotation  ofRaw 3com.apparence.camerawesome.cameraX.AnalysisRotation  raw 3com.apparence.camerawesome.cameraX.AnalysisRotation  values 3com.apparence.camerawesome.cameraX.AnalysisRotation  firstOrNull =com.apparence.camerawesome.cameraX.AnalysisRotation.Companion  ofRaw =com.apparence.camerawesome.cameraX.AnalysisRotation.Companion  values =com.apparence.camerawesome.cameraX.AnalysisRotation.Companion  AndroidFocusSettings 7com.apparence.camerawesome.cameraX.AndroidFocusSettings  Any 7com.apparence.camerawesome.cameraX.AndroidFocusSettings  	Companion 7com.apparence.camerawesome.cameraX.AndroidFocusSettings  Int 7com.apparence.camerawesome.cameraX.AndroidFocusSettings  List 7com.apparence.camerawesome.cameraX.AndroidFocusSettings  Long 7com.apparence.camerawesome.cameraX.AndroidFocusSettings  Suppress 7com.apparence.camerawesome.cameraX.AndroidFocusSettings  autoCancelDurationInMillis 7com.apparence.camerawesome.cameraX.AndroidFocusSettings  fromList 7com.apparence.camerawesome.cameraX.AndroidFocusSettings  let 7com.apparence.camerawesome.cameraX.AndroidFocusSettings  listOf 7com.apparence.camerawesome.cameraX.AndroidFocusSettings  toList 7com.apparence.camerawesome.cameraX.AndroidFocusSettings  AndroidFocusSettings Acom.apparence.camerawesome.cameraX.AndroidFocusSettings.Companion  fromList Acom.apparence.camerawesome.cameraX.AndroidFocusSettings.Companion  let Acom.apparence.camerawesome.cameraX.AndroidFocusSettings.Companion  listOf Acom.apparence.camerawesome.cameraX.AndroidFocusSettings.Companion  AndroidVideoOptions 6com.apparence.camerawesome.cameraX.AndroidVideoOptions  Any 6com.apparence.camerawesome.cameraX.AndroidVideoOptions  	Companion 6com.apparence.camerawesome.cameraX.AndroidVideoOptions  Int 6com.apparence.camerawesome.cameraX.AndroidVideoOptions  List 6com.apparence.camerawesome.cameraX.AndroidVideoOptions  Long 6com.apparence.camerawesome.cameraX.AndroidVideoOptions  QualityFallbackStrategy 6com.apparence.camerawesome.cameraX.AndroidVideoOptions  Suppress 6com.apparence.camerawesome.cameraX.AndroidVideoOptions  bitrate 6com.apparence.camerawesome.cameraX.AndroidVideoOptions  fallbackStrategy 6com.apparence.camerawesome.cameraX.AndroidVideoOptions  fromList 6com.apparence.camerawesome.cameraX.AndroidVideoOptions  let 6com.apparence.camerawesome.cameraX.AndroidVideoOptions  listOf 6com.apparence.camerawesome.cameraX.AndroidVideoOptions  ofRaw 6com.apparence.camerawesome.cameraX.AndroidVideoOptions  toList 6com.apparence.camerawesome.cameraX.AndroidVideoOptions  AndroidVideoOptions @com.apparence.camerawesome.cameraX.AndroidVideoOptions.Companion  QualityFallbackStrategy @com.apparence.camerawesome.cameraX.AndroidVideoOptions.Companion  fromList @com.apparence.camerawesome.cameraX.AndroidVideoOptions.Companion  let @com.apparence.camerawesome.cameraX.AndroidVideoOptions.Companion  listOf @com.apparence.camerawesome.cameraX.AndroidVideoOptions.Companion  ofRaw @com.apparence.camerawesome.cameraX.AndroidVideoOptions.Companion  AutoFitPreviewBuilder 8com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder  Context 8com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder  Display 8com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder  DisplayManager 8com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder  IllegalArgumentException 8com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder  Int 8com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder  Math 8com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder  Matrix 8com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder  Objects 8com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder  Preview 8com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder  
PreviewConfig 8com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder  Size 8com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder  Surface 8com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder  TextureView 8com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder  Unit 8com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder  View 8com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder  
WeakReference 8com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder  bufferDimens 8com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder  displayListener 8com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder  displayManager 8com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder  getDisplaySurfaceRotation 8com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder  
roundToInt 8com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder  updateTransform 8com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder  useCase 8com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder  viewFinderDimens 8com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder  viewFinderDisplay 8com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder  viewFinderRotation 8com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder  AutoFitPreviewBuilder Bcom.apparence.camerawesome.cameraX.AutoFitPreviewBuilder.Companion  Context Bcom.apparence.camerawesome.cameraX.AutoFitPreviewBuilder.Companion  IllegalArgumentException Bcom.apparence.camerawesome.cameraX.AutoFitPreviewBuilder.Companion  Math Bcom.apparence.camerawesome.cameraX.AutoFitPreviewBuilder.Companion  Matrix Bcom.apparence.camerawesome.cameraX.AutoFitPreviewBuilder.Companion  Objects Bcom.apparence.camerawesome.cameraX.AutoFitPreviewBuilder.Companion  Preview Bcom.apparence.camerawesome.cameraX.AutoFitPreviewBuilder.Companion  Size Bcom.apparence.camerawesome.cameraX.AutoFitPreviewBuilder.Companion  Surface Bcom.apparence.camerawesome.cameraX.AutoFitPreviewBuilder.Companion  Unit Bcom.apparence.camerawesome.cameraX.AutoFitPreviewBuilder.Companion  
WeakReference Bcom.apparence.camerawesome.cameraX.AutoFitPreviewBuilder.Companion  bufferDimens Bcom.apparence.camerawesome.cameraX.AutoFitPreviewBuilder.Companion  displayListener Bcom.apparence.camerawesome.cameraX.AutoFitPreviewBuilder.Companion  displayManager Bcom.apparence.camerawesome.cameraX.AutoFitPreviewBuilder.Companion  getDisplaySurfaceRotation Bcom.apparence.camerawesome.cameraX.AutoFitPreviewBuilder.Companion  
roundToInt Bcom.apparence.camerawesome.cameraX.AutoFitPreviewBuilder.Companion  updateTransform Bcom.apparence.camerawesome.cameraX.AutoFitPreviewBuilder.Companion  viewFinderDimens Bcom.apparence.camerawesome.cameraX.AutoFitPreviewBuilder.Companion  viewFinderDisplay Bcom.apparence.camerawesome.cameraX.AutoFitPreviewBuilder.Companion  DisplayListener Gcom.apparence.camerawesome.cameraX.AutoFitPreviewBuilder.DisplayManager  OnAttachStateChangeListener =com.apparence.camerawesome.cameraX.AutoFitPreviewBuilder.View  CAMERA 9com.apparence.camerawesome.cameraX.CamerAwesomePermission  CamerAwesomePermission 9com.apparence.camerawesome.cameraX.CamerAwesomePermission  	Companion 9com.apparence.camerawesome.cameraX.CamerAwesomePermission  Int 9com.apparence.camerawesome.cameraX.CamerAwesomePermission  LOCATION 9com.apparence.camerawesome.cameraX.CamerAwesomePermission  RECORD_AUDIO 9com.apparence.camerawesome.cameraX.CamerAwesomePermission  STORAGE 9com.apparence.camerawesome.cameraX.CamerAwesomePermission  firstOrNull 9com.apparence.camerawesome.cameraX.CamerAwesomePermission  name 9com.apparence.camerawesome.cameraX.CamerAwesomePermission  raw 9com.apparence.camerawesome.cameraX.CamerAwesomePermission  values 9com.apparence.camerawesome.cameraX.CamerAwesomePermission  firstOrNull Ccom.apparence.camerawesome.cameraX.CamerAwesomePermission.Companion  values Ccom.apparence.camerawesome.cameraX.CamerAwesomePermission.Companion  ActivityCompat 1com.apparence.camerawesome.cameraX.CameraAwesomeX  AnalysisImageConverter 1com.apparence.camerawesome.cameraX.CameraAwesomeX  AnalysisImageUtils 1com.apparence.camerawesome.cameraX.CameraAwesomeX  AspectRatio 1com.apparence.camerawesome.cameraX.CameraAwesomeX  BehaviorSubject 1com.apparence.camerawesome.cameraX.CameraAwesomeX  Bitmap 1com.apparence.camerawesome.cameraX.CameraAwesomeX  
BitmapFactory 1com.apparence.camerawesome.cameraX.CameraAwesomeX  Build 1com.apparence.camerawesome.cameraX.CameraAwesomeX  CamerAwesomePermission 1com.apparence.camerawesome.cameraX.CameraAwesomeX  
Camera2Config 1com.apparence.camerawesome.cameraX.CameraAwesomeX  CameraCapabilities 1com.apparence.camerawesome.cameraX.CameraAwesomeX  CameraCharacteristics 1com.apparence.camerawesome.cameraX.CameraAwesomeX  CameraInterface 1com.apparence.camerawesome.cameraX.CameraAwesomeX  CameraPermissions 1com.apparence.camerawesome.cameraX.CameraAwesomeX  CameraSelector 1com.apparence.camerawesome.cameraX.CameraAwesomeX  
CameraXConfig 1com.apparence.camerawesome.cameraX.CameraAwesomeX  CameraXState 1com.apparence.camerawesome.cameraX.CameraAwesomeX  CamerawesomePlugin 1com.apparence.camerawesome.cameraX.CameraAwesomeX  CancellationTokenSource 1com.apparence.camerawesome.cameraX.CameraAwesomeX  Canvas 1com.apparence.camerawesome.cameraX.CameraAwesomeX  CaptureModes 1com.apparence.camerawesome.cameraX.CameraAwesomeX  ColorMatrixColorFilter 1com.apparence.camerawesome.cameraX.CameraAwesomeX  Consumer 1com.apparence.camerawesome.cameraX.CameraAwesomeX  
ContextCompat 1com.apparence.camerawesome.cameraX.CameraAwesomeX  CoroutineScope 1com.apparence.camerawesome.cameraX.CameraAwesomeX  Dispatchers 1com.apparence.camerawesome.cameraX.CameraAwesomeX  EventChannel 1com.apparence.camerawesome.cameraX.CameraAwesomeX  	Exception 1com.apparence.camerawesome.cameraX.CameraAwesomeX  
ExifInterface 1com.apparence.camerawesome.cameraX.CameraAwesomeX  ExifPreferences 1com.apparence.camerawesome.cameraX.CameraAwesomeX  File 1com.apparence.camerawesome.cameraX.CameraAwesomeX  FileOutputOptions 1com.apparence.camerawesome.cameraX.CameraAwesomeX  FileOutputStream 1com.apparence.camerawesome.cameraX.CameraAwesomeX  	FlashMode 1com.apparence.camerawesome.cameraX.CameraAwesomeX  FocusMeteringAction 1com.apparence.camerawesome.cameraX.CameraAwesomeX  Handler 1com.apparence.camerawesome.cameraX.CameraAwesomeX  ImageAnalysisBuilder 1com.apparence.camerawesome.cameraX.CameraAwesomeX  ImageCapture 1com.apparence.camerawesome.cameraX.CameraAwesomeX  Intent 1com.apparence.camerawesome.cameraX.CameraAwesomeX  LocationServices 1com.apparence.camerawesome.cameraX.CameraAwesomeX  Log 1com.apparence.camerawesome.cameraX.CameraAwesomeX  Looper 1com.apparence.camerawesome.cameraX.CameraAwesomeX  Manifest 1com.apparence.camerawesome.cameraX.CameraAwesomeX  	Messenger 1com.apparence.camerawesome.cameraX.CameraAwesomeX  OrientationStreamListener 1com.apparence.camerawesome.cameraX.CameraAwesomeX  OutputImageFormat 1com.apparence.camerawesome.cameraX.CameraAwesomeX  PackageManager 1com.apparence.camerawesome.cameraX.CameraAwesomeX  Paint 1com.apparence.camerawesome.cameraX.CameraAwesomeX  PhysicalButtonMessageHandler 1com.apparence.camerawesome.cameraX.CameraAwesomeX  PhysicalButtonsHandler 1com.apparence.camerawesome.cameraX.CameraAwesomeX  PigeonSensorPosition 1com.apparence.camerawesome.cameraX.CameraAwesomeX  
PlayerService 1com.apparence.camerawesome.cameraX.CameraAwesomeX  PreviewSize 1com.apparence.camerawesome.cameraX.CameraAwesomeX  Priority 1com.apparence.camerawesome.cameraX.CameraAwesomeX  ProcessCameraProvider 1com.apparence.camerawesome.cameraX.CameraAwesomeX  Rational 1com.apparence.camerawesome.cameraX.CameraAwesomeX  Result 1com.apparence.camerawesome.cameraX.CameraAwesomeX  SensorOrientationListener 1com.apparence.camerawesome.cameraX.CameraAwesomeX  Size 1com.apparence.camerawesome.cameraX.CameraAwesomeX  #SurfaceOrientedMeteringPointFactory 1com.apparence.camerawesome.cameraX.CameraAwesomeX  TODO 1com.apparence.camerawesome.cameraX.CameraAwesomeX  TimeUnit 1com.apparence.camerawesome.cameraX.CameraAwesomeX  Unit 1com.apparence.camerawesome.cameraX.CameraAwesomeX  activity 1com.apparence.camerawesome.cameraX.CameraAwesomeX  all 1com.apparence.camerawesome.cameraX.CameraAwesomeX  apply 1com.apparence.camerawesome.cameraX.CameraAwesomeX  binding 1com.apparence.camerawesome.cameraX.CameraAwesomeX  	buildList 1com.apparence.camerawesome.cameraX.CameraAwesomeX  cameraPermissions 1com.apparence.camerawesome.cameraX.CameraAwesomeX  cameraState 1com.apparence.camerawesome.cameraX.CameraAwesomeX  cancellationTokenSource 1com.apparence.camerawesome.cameraX.CameraAwesomeX  colorMatrix 1com.apparence.camerawesome.cameraX.CameraAwesomeX  	configure 1com.apparence.camerawesome.cameraX.CameraAwesomeX  configureCameraXLogs 1com.apparence.camerawesome.cameraX.CameraAwesomeX  configureInstance 1com.apparence.camerawesome.cameraX.CameraAwesomeX  exifPreferences 1com.apparence.camerawesome.cameraX.CameraAwesomeX  first 1com.apparence.camerawesome.cameraX.CameraAwesomeX  firstOrNull 1com.apparence.camerawesome.cameraX.CameraAwesomeX  focus 1com.apparence.camerawesome.cameraX.CameraAwesomeX  fusedLocationClient 1com.apparence.camerawesome.cameraX.CameraAwesomeX  getCameraLevel 1com.apparence.camerawesome.cameraX.CameraAwesomeX  getCameraProvider 1com.apparence.camerawesome.cameraX.CameraAwesomeX  getInstance 1com.apparence.camerawesome.cameraX.CameraAwesomeX  
getMaxZoom 1com.apparence.camerawesome.cameraX.CameraAwesomeX  
getMinZoom 1com.apparence.camerawesome.cameraX.CameraAwesomeX  	getOrNull 1com.apparence.camerawesome.cameraX.CameraAwesomeX  getOrientedSize 1com.apparence.camerawesome.cameraX.CameraAwesomeX  imageStreamChannel 1com.apparence.camerawesome.cameraX.CameraAwesomeX  isMultiCamSupported 1com.apparence.camerawesome.cameraX.CameraAwesomeX  
isNotEmpty 1com.apparence.camerawesome.cameraX.CameraAwesomeX  java 1com.apparence.camerawesome.cameraX.CameraAwesomeX  lastRecordedVideoSubscriptions 1com.apparence.camerawesome.cameraX.CameraAwesomeX  lastRecordedVideos 1com.apparence.camerawesome.cameraX.CameraAwesomeX  launch 1com.apparence.camerawesome.cameraX.CameraAwesomeX  listOf 1com.apparence.camerawesome.cameraX.CameraAwesomeX  	lowercase 1com.apparence.camerawesome.cameraX.CameraAwesomeX  map 1com.apparence.camerawesome.cameraX.CameraAwesomeX  
mapIndexed 1com.apparence.camerawesome.cameraX.CameraAwesomeX  
mapNotNull 1com.apparence.camerawesome.cameraX.CameraAwesomeX  	mapValues 1com.apparence.camerawesome.cameraX.CameraAwesomeX  
mutableListOf 1com.apparence.camerawesome.cameraX.CameraAwesomeX  
noneFilter 1com.apparence.camerawesome.cameraX.CameraAwesomeX  orientationStreamChannel 1com.apparence.camerawesome.cameraX.CameraAwesomeX  orientationStreamListener 1com.apparence.camerawesome.cameraX.CameraAwesomeX  physicalButtonHandler 1com.apparence.camerawesome.cameraX.CameraAwesomeX  resume 1com.apparence.camerawesome.cameraX.CameraAwesomeX  retrieveLocation 1com.apparence.camerawesome.cameraX.CameraAwesomeX  
roundToInt 1com.apparence.camerawesome.cameraX.CameraAwesomeX  run 1com.apparence.camerawesome.cameraX.CameraAwesomeX  sensorOrientationListener 1com.apparence.camerawesome.cameraX.CameraAwesomeX  set 1com.apparence.camerawesome.cameraX.CameraAwesomeX  setUp 1com.apparence.camerawesome.cameraX.CameraAwesomeX  success 1com.apparence.camerawesome.cameraX.CameraAwesomeX  suspendCancellableCoroutine 1com.apparence.camerawesome.cameraX.CameraAwesomeX  
takePhotoWith 1com.apparence.camerawesome.cameraX.CameraAwesomeX  textureRegistry 1com.apparence.camerawesome.cameraX.CameraAwesomeX  to 1com.apparence.camerawesome.cameraX.CameraAwesomeX  toFloatArray 1com.apparence.camerawesome.cameraX.CameraAwesomeX  toMap 1com.apparence.camerawesome.cameraX.CameraAwesomeX  toMutableMap 1com.apparence.camerawesome.cameraX.CameraAwesomeX  until 1com.apparence.camerawesome.cameraX.CameraAwesomeX  	uppercase 1com.apparence.camerawesome.cameraX.CameraAwesomeX  use 1com.apparence.camerawesome.cameraX.CameraAwesomeX  	withIndex 1com.apparence.camerawesome.cameraX.CameraAwesomeX  Build 5com.apparence.camerawesome.cameraX.CameraCapabilities  Camera2CameraInfo 5com.apparence.camerawesome.cameraX.CameraCapabilities  CameraCharacteristics 5com.apparence.camerawesome.cameraX.CameraCapabilities  CameraSelector 5com.apparence.camerawesome.cameraX.CameraCapabilities  	Companion 5com.apparence.camerawesome.cameraX.CameraCapabilities  ExperimentalCamera2Interop 5com.apparence.camerawesome.cameraX.CameraCapabilities  Int 5com.apparence.camerawesome.cameraX.CameraCapabilities  ProcessCameraProvider 5com.apparence.camerawesome.cameraX.CameraCapabilities  androidx 5com.apparence.camerawesome.cameraX.CameraCapabilities  firstOrNull 5com.apparence.camerawesome.cameraX.CameraCapabilities  getCameraLevel 5com.apparence.camerawesome.cameraX.CameraCapabilities  let 5com.apparence.camerawesome.cameraX.CameraCapabilities  Build ?com.apparence.camerawesome.cameraX.CameraCapabilities.Companion  Camera2CameraInfo ?com.apparence.camerawesome.cameraX.CameraCapabilities.Companion  CameraCharacteristics ?com.apparence.camerawesome.cameraX.CameraCapabilities.Companion  ExperimentalCamera2Interop ?com.apparence.camerawesome.cameraX.CameraCapabilities.Companion  firstOrNull ?com.apparence.camerawesome.cameraX.CameraCapabilities.Companion  getCameraLevel ?com.apparence.camerawesome.cameraX.CameraCapabilities.Companion  let ?com.apparence.camerawesome.cameraX.CameraCapabilities.Companion  
annotation >com.apparence.camerawesome.cameraX.CameraCapabilities.androidx  OptIn Icom.apparence.camerawesome.cameraX.CameraCapabilities.androidx.annotation  AndroidFocusSettings 2com.apparence.camerawesome.cameraX.CameraInterface  Any 2com.apparence.camerawesome.cameraX.CameraInterface  BasicMessageChannel 2com.apparence.camerawesome.cameraX.CameraInterface  BinaryMessenger 2com.apparence.camerawesome.cameraX.CameraInterface  Boolean 2com.apparence.camerawesome.cameraX.CameraInterface  CameraInterface 2com.apparence.camerawesome.cameraX.CameraInterface  CameraInterfaceCodec 2com.apparence.camerawesome.cameraX.CameraInterface  	Companion 2com.apparence.camerawesome.cameraX.CameraInterface  Double 2com.apparence.camerawesome.cameraX.CameraInterface  ExifPreferences 2com.apparence.camerawesome.cameraX.CameraInterface  Int 2com.apparence.camerawesome.cameraX.CameraInterface  List 2com.apparence.camerawesome.cameraX.CameraInterface  Long 2com.apparence.camerawesome.cameraX.CameraInterface  MessageCodec 2com.apparence.camerawesome.cameraX.CameraInterface  PigeonSensor 2com.apparence.camerawesome.cameraX.CameraInterface  PigeonSensorPosition 2com.apparence.camerawesome.cameraX.CameraInterface  PigeonSensorTypeDevice 2com.apparence.camerawesome.cameraX.CameraInterface  PreviewSize 2com.apparence.camerawesome.cameraX.CameraInterface  Result 2com.apparence.camerawesome.cameraX.CameraInterface  String 2com.apparence.camerawesome.cameraX.CameraInterface  Suppress 2com.apparence.camerawesome.cameraX.CameraInterface  	Throwable 2com.apparence.camerawesome.cameraX.CameraInterface  Unit 2com.apparence.camerawesome.cameraX.CameraInterface  VideoOptions 2com.apparence.camerawesome.cameraX.CameraInterface  availableSizes 2com.apparence.camerawesome.cameraX.CameraInterface  checkPermissions 2com.apparence.camerawesome.cameraX.CameraInterface  codec 2com.apparence.camerawesome.cameraX.CameraInterface  focusOnPoint 2com.apparence.camerawesome.cameraX.CameraInterface  getBackSensors 2com.apparence.camerawesome.cameraX.CameraInterface  getEffectivPreviewSize 2com.apparence.camerawesome.cameraX.CameraInterface  getFrontSensors 2com.apparence.camerawesome.cameraX.CameraInterface  
getMaxZoom 2com.apparence.camerawesome.cameraX.CameraInterface  
getMinZoom 2com.apparence.camerawesome.cameraX.CameraInterface  getPreviewTextureId 2com.apparence.camerawesome.cameraX.CameraInterface  getValue 2com.apparence.camerawesome.cameraX.CameraInterface  handleAutoFocus 2com.apparence.camerawesome.cameraX.CameraInterface  isMultiCamSupported 2com.apparence.camerawesome.cameraX.CameraInterface  )isVideoRecordingAndImageAnalysisSupported 2com.apparence.camerawesome.cameraX.CameraInterface  lazy 2com.apparence.camerawesome.cameraX.CameraInterface  let 2com.apparence.camerawesome.cameraX.CameraInterface  listOf 2com.apparence.camerawesome.cameraX.CameraInterface  ofRaw 2com.apparence.camerawesome.cameraX.CameraInterface  pauseVideoRecording 2com.apparence.camerawesome.cameraX.CameraInterface  provideDelegate 2com.apparence.camerawesome.cameraX.CameraInterface  receivedImageFromStream 2com.apparence.camerawesome.cameraX.CameraInterface  recordVideo 2com.apparence.camerawesome.cameraX.CameraInterface  refresh 2com.apparence.camerawesome.cameraX.CameraInterface  requestPermissions 2com.apparence.camerawesome.cameraX.CameraInterface  resumeVideoRecording 2com.apparence.camerawesome.cameraX.CameraInterface  run 2com.apparence.camerawesome.cameraX.CameraInterface  setAspectRatio 2com.apparence.camerawesome.cameraX.CameraInterface  setCaptureMode 2com.apparence.camerawesome.cameraX.CameraInterface  
setCorrection 2com.apparence.camerawesome.cameraX.CameraInterface  setExifPreferences 2com.apparence.camerawesome.cameraX.CameraInterface  	setFilter 2com.apparence.camerawesome.cameraX.CameraInterface  setFlashMode 2com.apparence.camerawesome.cameraX.CameraInterface  setMirrorFrontCamera 2com.apparence.camerawesome.cameraX.CameraInterface  setPhotoSize 2com.apparence.camerawesome.cameraX.CameraInterface  setPreviewSize 2com.apparence.camerawesome.cameraX.CameraInterface  setRecordingAudioMode 2com.apparence.camerawesome.cameraX.CameraInterface  	setSensor 2com.apparence.camerawesome.cameraX.CameraInterface  setUp 2com.apparence.camerawesome.cameraX.CameraInterface  setZoom 2com.apparence.camerawesome.cameraX.CameraInterface  setupCamera 2com.apparence.camerawesome.cameraX.CameraInterface  setupImageAnalysisStream 2com.apparence.camerawesome.cameraX.CameraInterface  start 2com.apparence.camerawesome.cameraX.CameraInterface  
startAnalysis 2com.apparence.camerawesome.cameraX.CameraInterface  stop 2com.apparence.camerawesome.cameraX.CameraInterface  stopAnalysis 2com.apparence.camerawesome.cameraX.CameraInterface  stopRecordingVideo 2com.apparence.camerawesome.cameraX.CameraInterface  	takePhoto 2com.apparence.camerawesome.cameraX.CameraInterface  	wrapError 2com.apparence.camerawesome.cameraX.CameraInterface  
wrapResult 2com.apparence.camerawesome.cameraX.CameraInterface  BasicMessageChannel <com.apparence.camerawesome.cameraX.CameraInterface.Companion  CameraInterfaceCodec <com.apparence.camerawesome.cameraX.CameraInterface.Companion  PigeonSensorPosition <com.apparence.camerawesome.cameraX.CameraInterface.Companion  codec <com.apparence.camerawesome.cameraX.CameraInterface.Companion  getValue <com.apparence.camerawesome.cameraX.CameraInterface.Companion  lazy <com.apparence.camerawesome.cameraX.CameraInterface.Companion  let <com.apparence.camerawesome.cameraX.CameraInterface.Companion  listOf <com.apparence.camerawesome.cameraX.CameraInterface.Companion  ofRaw <com.apparence.camerawesome.cameraX.CameraInterface.Companion  provideDelegate <com.apparence.camerawesome.cameraX.CameraInterface.Companion  run <com.apparence.camerawesome.cameraX.CameraInterface.Companion  setUp <com.apparence.camerawesome.cameraX.CameraInterface.Companion  	wrapError <com.apparence.camerawesome.cameraX.CameraInterface.Companion  
wrapResult <com.apparence.camerawesome.cameraX.CameraInterface.Companion  AndroidFocusSettings 7com.apparence.camerawesome.cameraX.CameraInterfaceCodec  AndroidVideoOptions 7com.apparence.camerawesome.cameraX.CameraInterfaceCodec  CupertinoVideoOptions 7com.apparence.camerawesome.cameraX.CameraInterfaceCodec  ExifPreferences 7com.apparence.camerawesome.cameraX.CameraInterfaceCodec  PigeonSensor 7com.apparence.camerawesome.cameraX.CameraInterfaceCodec  PigeonSensorTypeDevice 7com.apparence.camerawesome.cameraX.CameraInterfaceCodec  PreviewSize 7com.apparence.camerawesome.cameraX.CameraInterfaceCodec  VideoOptions 7com.apparence.camerawesome.cameraX.CameraInterfaceCodec  fromList 7com.apparence.camerawesome.cameraX.CameraInterfaceCodec  let 7com.apparence.camerawesome.cameraX.CameraInterfaceCodec  	readValue 7com.apparence.camerawesome.cameraX.CameraInterfaceCodec  
writeValue 7com.apparence.camerawesome.cameraX.CameraInterfaceCodec  Activity 4com.apparence.camerawesome.cameraX.CameraPermissions  ActivityCompat 4com.apparence.camerawesome.cameraX.CameraPermissions  Any 4com.apparence.camerawesome.cameraX.CameraPermissions  Array 4com.apparence.camerawesome.cameraX.CameraPermissions  	ArrayList 4com.apparence.camerawesome.cameraX.CameraPermissions  Boolean 4com.apparence.camerawesome.cameraX.CameraPermissions  CameraPermissions 4com.apparence.camerawesome.cameraX.CameraPermissions  	Companion 4com.apparence.camerawesome.cameraX.CameraPermissions  Context 4com.apparence.camerawesome.cameraX.CameraPermissions  
ContextCompat 4com.apparence.camerawesome.cameraX.CameraPermissions  Continuation 4com.apparence.camerawesome.cameraX.CameraPermissions  CoroutineScope 4com.apparence.camerawesome.cameraX.CameraPermissions  Dispatchers 4com.apparence.camerawesome.cameraX.CameraPermissions  	EventSink 4com.apparence.camerawesome.cameraX.CameraPermissions  Int 4com.apparence.camerawesome.cameraX.CameraPermissions  IntArray 4com.apparence.camerawesome.cameraX.CameraPermissions  List 4com.apparence.camerawesome.cameraX.CameraPermissions  Log 4com.apparence.camerawesome.cameraX.CameraPermissions  Manifest 4com.apparence.camerawesome.cameraX.CameraPermissions  MutableList 4com.apparence.camerawesome.cameraX.CameraPermissions  PERMISSIONS_MULTIPLE_REQUEST 4com.apparence.camerawesome.cameraX.CameraPermissions  PERMISSION_GEOLOC 4com.apparence.camerawesome.cameraX.CameraPermissions  PERMISSION_RECORD_AUDIO 4com.apparence.camerawesome.cameraX.CameraPermissions  PackageManager 4com.apparence.camerawesome.cameraX.CameraPermissions  PermissionNotDeclaredException 4com.apparence.camerawesome.cameraX.CameraPermissions  PermissionRequest 4com.apparence.camerawesome.cameraX.CameraPermissions  String 4com.apparence.camerawesome.cameraX.CameraPermissions  TAG 4com.apparence.camerawesome.cameraX.CameraPermissions  UUID 4com.apparence.camerawesome.cameraX.CameraPermissions  Unit 4com.apparence.camerawesome.cameraX.CameraPermissions  allPermissions 4com.apparence.camerawesome.cameraX.CameraPermissions  apply 4com.apparence.camerawesome.cameraX.CameraPermissions  	callbacks 4com.apparence.camerawesome.cameraX.CameraPermissions  declaredCameraPermissions 4com.apparence.camerawesome.cameraX.CameraPermissions  events 4com.apparence.camerawesome.cameraX.CameraPermissions  
hasPermission 4com.apparence.camerawesome.cameraX.CameraPermissions  indices 4com.apparence.camerawesome.cameraX.CameraPermissions  
isNullOrEmpty 4com.apparence.camerawesome.cameraX.CameraPermissions  java 4com.apparence.camerawesome.cameraX.CameraPermissions  launch 4com.apparence.camerawesome.cameraX.CameraPermissions  listOf 4com.apparence.camerawesome.cameraX.CameraPermissions  
mutableListOf 4com.apparence.camerawesome.cameraX.CameraPermissions  onCancel 4com.apparence.camerawesome.cameraX.CameraPermissions  permissionGranted 4com.apparence.camerawesome.cameraX.CameraPermissions  requestBasePermissions 4com.apparence.camerawesome.cameraX.CameraPermissions  requestPermissions 4com.apparence.camerawesome.cameraX.CameraPermissions  resume 4com.apparence.camerawesome.cameraX.CameraPermissions  suspendCoroutine 4com.apparence.camerawesome.cameraX.CameraPermissions  toList 4com.apparence.camerawesome.cameraX.CameraPermissions  toTypedArray 4com.apparence.camerawesome.cameraX.CameraPermissions  ActivityCompat >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  	ArrayList >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  CameraPermissions >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  
ContextCompat >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  CoroutineScope >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  Dispatchers >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  Log >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  Manifest >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  PERMISSIONS_MULTIPLE_REQUEST >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  PERMISSION_GEOLOC >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  PERMISSION_RECORD_AUDIO >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  PackageManager >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  PermissionNotDeclaredException >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  PermissionRequest >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  TAG >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  UUID >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  allPermissions >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  apply >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  indices >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  
isNullOrEmpty >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  java >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  launch >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  listOf >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  
mutableListOf >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  requestPermissions >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  resume >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  suspendCoroutine >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  toList >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  toTypedArray >com.apparence.camerawesome.cameraX.CameraPermissions.Companion  AspectRatio /com.apparence.camerawesome.cameraX.CameraXState  AspectRatioStrategy /com.apparence.camerawesome.cameraX.CameraXState  CamcorderProfileResolutionQuirk /com.apparence.camerawesome.cameraX.CameraXState  Camera2CameraInfo /com.apparence.camerawesome.cameraX.CameraXState  CameraCapabilities /com.apparence.camerawesome.cameraX.CameraXState  CameraCharacteristics /com.apparence.camerawesome.cameraX.CameraXState  CameraCharacteristicsCompat /com.apparence.camerawesome.cameraX.CameraXState  CameraSelector /com.apparence.camerawesome.cameraX.CameraXState  CamerawesomePlugin /com.apparence.camerawesome.cameraX.CameraXState  CaptureModes /com.apparence.camerawesome.cameraX.CameraXState  ConcurrentCamera /com.apparence.camerawesome.cameraX.CameraXState  
ContextCompat /com.apparence.camerawesome.cameraX.CameraXState  	Exception /com.apparence.camerawesome.cameraX.CameraXState  FallbackStrategy /com.apparence.camerawesome.cameraX.CameraXState  	FlashMode /com.apparence.camerawesome.cameraX.CameraXState  ImageAnalysisBuilder /com.apparence.camerawesome.cameraX.CameraXState  ImageCapture /com.apparence.camerawesome.cameraX.CameraXState  Log /com.apparence.camerawesome.cameraX.CameraXState  
MirrorMode /com.apparence.camerawesome.cameraX.CameraXState  OutputImageFormat /com.apparence.camerawesome.cameraX.CameraXState  PigeonSensorPosition /com.apparence.camerawesome.cameraX.CameraXState  Preview /com.apparence.camerawesome.cameraX.CameraXState  Quality /com.apparence.camerawesome.cameraX.CameraXState  QualityFallbackStrategy /com.apparence.camerawesome.cameraX.CameraXState  QualitySelector /com.apparence.camerawesome.cameraX.CameraXState  Rational /com.apparence.camerawesome.cameraX.CameraXState  Recorder /com.apparence.camerawesome.cameraX.CameraXState  ResolutionSelector /com.apparence.camerawesome.cameraX.CameraXState  ResolutionStrategy /com.apparence.camerawesome.cameraX.CameraXState  Surface /com.apparence.camerawesome.cameraX.CameraXState  UseCaseGroup /com.apparence.camerawesome.cameraX.CameraXState  VideoCapture /com.apparence.camerawesome.cameraX.CameraXState  VideoRecordingQuality /com.apparence.camerawesome.cameraX.CameraXState  ViewPort /com.apparence.camerawesome.cameraX.CameraXState  activity /com.apparence.camerawesome.cameraX.CameraXState  all /com.apparence.camerawesome.cameraX.CameraXState  apply /com.apparence.camerawesome.cameraX.CameraXState  aspectRatio /com.apparence.camerawesome.cameraX.CameraXState  buildVideoCapture /com.apparence.camerawesome.cameraX.CameraXState  cameraProvider /com.apparence.camerawesome.cameraX.CameraXState  cameraState /com.apparence.camerawesome.cameraX.CameraXState  concurrentCamera /com.apparence.camerawesome.cameraX.CameraXState  	configure /com.apparence.camerawesome.cameraX.CameraXState  currentCaptureMode /com.apparence.camerawesome.cameraX.CameraXState  enableAudioRecording /com.apparence.camerawesome.cameraX.CameraXState  enableImageStream /com.apparence.camerawesome.cameraX.CameraXState  executor /com.apparence.camerawesome.cameraX.CameraXState  first /com.apparence.camerawesome.cameraX.CameraXState  firstOrNull /com.apparence.camerawesome.cameraX.CameraXState  	flashMode /com.apparence.camerawesome.cameraX.CameraXState  getCameraLevel /com.apparence.camerawesome.cameraX.CameraXState  getOrientedSize /com.apparence.camerawesome.cameraX.CameraXState  getResolutionSelector /com.apparence.camerawesome.cameraX.CameraXState  
imageAnalysis /com.apparence.camerawesome.cameraX.CameraXState  imageAnalysisBuilder /com.apparence.camerawesome.cameraX.CameraXState  
imageCaptures /com.apparence.camerawesome.cameraX.CameraXState  isMultiCamSupported /com.apparence.camerawesome.cameraX.CameraXState  mainCameraControl /com.apparence.camerawesome.cameraX.CameraXState  mainCameraInfos /com.apparence.camerawesome.cameraX.CameraXState  map /com.apparence.camerawesome.cameraX.CameraXState  maxZoomRatio /com.apparence.camerawesome.cameraX.CameraXState  minZoomRatio /com.apparence.camerawesome.cameraX.CameraXState  mirrorFrontCamera /com.apparence.camerawesome.cameraX.CameraXState  
mutableListOf /com.apparence.camerawesome.cameraX.CameraXState  
onStreamReady /com.apparence.camerawesome.cameraX.CameraXState  	photoSize /com.apparence.camerawesome.cameraX.CameraXState  portrait /com.apparence.camerawesome.cameraX.CameraXState  
previewCamera /com.apparence.camerawesome.cameraX.CameraXState  previewSize /com.apparence.camerawesome.cameraX.CameraXState  previewSizes /com.apparence.camerawesome.cameraX.CameraXState  previews /com.apparence.camerawesome.cameraX.CameraXState  rational /com.apparence.camerawesome.cameraX.CameraXState  
recordings /com.apparence.camerawesome.cameraX.CameraXState  sensors /com.apparence.camerawesome.cameraX.CameraXState  set /com.apparence.camerawesome.cameraX.CameraXState  setCaptureMode /com.apparence.camerawesome.cameraX.CameraXState  
setLinearZoom /com.apparence.camerawesome.cameraX.CameraXState  startFocusAndMetering /com.apparence.camerawesome.cameraX.CameraXState  stop /com.apparence.camerawesome.cameraX.CameraXState  surfaceProvider /com.apparence.camerawesome.cameraX.CameraXState  textureEntries /com.apparence.camerawesome.cameraX.CameraXState  until /com.apparence.camerawesome.cameraX.CameraXState  updateAspectRatio /com.apparence.camerawesome.cameraX.CameraXState  updateLifecycle /com.apparence.camerawesome.cameraX.CameraXState  	uppercase /com.apparence.camerawesome.cameraX.CameraXState  
videoCaptures /com.apparence.camerawesome.cameraX.CameraXState  videoOptions /com.apparence.camerawesome.cameraX.CameraXState  videoRecordingQuality /com.apparence.camerawesome.cameraX.CameraXState  	withIndex /com.apparence.camerawesome.cameraX.CameraXState  
ANALYSIS_ONLY /com.apparence.camerawesome.cameraX.CaptureModes  PHOTO /com.apparence.camerawesome.cameraX.CaptureModes  VIDEO /com.apparence.camerawesome.cameraX.CaptureModes  valueOf /com.apparence.camerawesome.cameraX.CaptureModes  SingleCameraConfig 3com.apparence.camerawesome.cameraX.ConcurrentCamera  Any 2com.apparence.camerawesome.cameraX.CropRectWrapper  	Companion 2com.apparence.camerawesome.cameraX.CropRectWrapper  CropRectWrapper 2com.apparence.camerawesome.cameraX.CropRectWrapper  Int 2com.apparence.camerawesome.cameraX.CropRectWrapper  List 2com.apparence.camerawesome.cameraX.CropRectWrapper  Long 2com.apparence.camerawesome.cameraX.CropRectWrapper  Suppress 2com.apparence.camerawesome.cameraX.CropRectWrapper  fromList 2com.apparence.camerawesome.cameraX.CropRectWrapper  height 2com.apparence.camerawesome.cameraX.CropRectWrapper  left 2com.apparence.camerawesome.cameraX.CropRectWrapper  let 2com.apparence.camerawesome.cameraX.CropRectWrapper  listOf 2com.apparence.camerawesome.cameraX.CropRectWrapper  toList 2com.apparence.camerawesome.cameraX.CropRectWrapper  top 2com.apparence.camerawesome.cameraX.CropRectWrapper  width 2com.apparence.camerawesome.cameraX.CropRectWrapper  CropRectWrapper <com.apparence.camerawesome.cameraX.CropRectWrapper.Companion  fromList <com.apparence.camerawesome.cameraX.CropRectWrapper.Companion  let <com.apparence.camerawesome.cameraX.CropRectWrapper.Companion  listOf <com.apparence.camerawesome.cameraX.CropRectWrapper.Companion  	Companion 5com.apparence.camerawesome.cameraX.CupertinoCodecType  CupertinoCodecType 5com.apparence.camerawesome.cameraX.CupertinoCodecType  Int 5com.apparence.camerawesome.cameraX.CupertinoCodecType  firstOrNull 5com.apparence.camerawesome.cameraX.CupertinoCodecType  ofRaw 5com.apparence.camerawesome.cameraX.CupertinoCodecType  raw 5com.apparence.camerawesome.cameraX.CupertinoCodecType  values 5com.apparence.camerawesome.cameraX.CupertinoCodecType  firstOrNull ?com.apparence.camerawesome.cameraX.CupertinoCodecType.Companion  ofRaw ?com.apparence.camerawesome.cameraX.CupertinoCodecType.Companion  values ?com.apparence.camerawesome.cameraX.CupertinoCodecType.Companion  	Companion 4com.apparence.camerawesome.cameraX.CupertinoFileType  CupertinoFileType 4com.apparence.camerawesome.cameraX.CupertinoFileType  Int 4com.apparence.camerawesome.cameraX.CupertinoFileType  firstOrNull 4com.apparence.camerawesome.cameraX.CupertinoFileType  ofRaw 4com.apparence.camerawesome.cameraX.CupertinoFileType  raw 4com.apparence.camerawesome.cameraX.CupertinoFileType  values 4com.apparence.camerawesome.cameraX.CupertinoFileType  firstOrNull >com.apparence.camerawesome.cameraX.CupertinoFileType.Companion  ofRaw >com.apparence.camerawesome.cameraX.CupertinoFileType.Companion  values >com.apparence.camerawesome.cameraX.CupertinoFileType.Companion  Any 8com.apparence.camerawesome.cameraX.CupertinoVideoOptions  	Companion 8com.apparence.camerawesome.cameraX.CupertinoVideoOptions  CupertinoCodecType 8com.apparence.camerawesome.cameraX.CupertinoVideoOptions  CupertinoFileType 8com.apparence.camerawesome.cameraX.CupertinoVideoOptions  CupertinoVideoOptions 8com.apparence.camerawesome.cameraX.CupertinoVideoOptions  Int 8com.apparence.camerawesome.cameraX.CupertinoVideoOptions  List 8com.apparence.camerawesome.cameraX.CupertinoVideoOptions  Long 8com.apparence.camerawesome.cameraX.CupertinoVideoOptions  Suppress 8com.apparence.camerawesome.cameraX.CupertinoVideoOptions  codec 8com.apparence.camerawesome.cameraX.CupertinoVideoOptions  fileType 8com.apparence.camerawesome.cameraX.CupertinoVideoOptions  fps 8com.apparence.camerawesome.cameraX.CupertinoVideoOptions  fromList 8com.apparence.camerawesome.cameraX.CupertinoVideoOptions  let 8com.apparence.camerawesome.cameraX.CupertinoVideoOptions  listOf 8com.apparence.camerawesome.cameraX.CupertinoVideoOptions  ofRaw 8com.apparence.camerawesome.cameraX.CupertinoVideoOptions  toList 8com.apparence.camerawesome.cameraX.CupertinoVideoOptions  CupertinoCodecType Bcom.apparence.camerawesome.cameraX.CupertinoVideoOptions.Companion  CupertinoFileType Bcom.apparence.camerawesome.cameraX.CupertinoVideoOptions.Companion  CupertinoVideoOptions Bcom.apparence.camerawesome.cameraX.CupertinoVideoOptions.Companion  fromList Bcom.apparence.camerawesome.cameraX.CupertinoVideoOptions.Companion  let Bcom.apparence.camerawesome.cameraX.CupertinoVideoOptions.Companion  listOf Bcom.apparence.camerawesome.cameraX.CupertinoVideoOptions.Companion  ofRaw Bcom.apparence.camerawesome.cameraX.CupertinoVideoOptions.Companion  DisplayListener 1com.apparence.camerawesome.cameraX.DisplayManager  	EventSink /com.apparence.camerawesome.cameraX.EventChannel  
StreamHandler /com.apparence.camerawesome.cameraX.EventChannel  Any 2com.apparence.camerawesome.cameraX.ExifPreferences  Boolean 2com.apparence.camerawesome.cameraX.ExifPreferences  	Companion 2com.apparence.camerawesome.cameraX.ExifPreferences  ExifPreferences 2com.apparence.camerawesome.cameraX.ExifPreferences  List 2com.apparence.camerawesome.cameraX.ExifPreferences  Suppress 2com.apparence.camerawesome.cameraX.ExifPreferences  fromList 2com.apparence.camerawesome.cameraX.ExifPreferences  listOf 2com.apparence.camerawesome.cameraX.ExifPreferences  saveGPSLocation 2com.apparence.camerawesome.cameraX.ExifPreferences  toList 2com.apparence.camerawesome.cameraX.ExifPreferences  ExifPreferences <com.apparence.camerawesome.cameraX.ExifPreferences.Companion  fromList <com.apparence.camerawesome.cameraX.ExifPreferences.Companion  listOf <com.apparence.camerawesome.cameraX.ExifPreferences.Companion  code /com.apparence.camerawesome.cameraX.FlutterError  details /com.apparence.camerawesome.cameraX.FlutterError  message /com.apparence.camerawesome.cameraX.FlutterError  Any 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  AspectRatio 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  	ByteArray 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  	Companion 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  CoroutineScope 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  Dispatchers 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  Double 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  EventChannel 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  Executor 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  
ImageAnalysis 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  ImageAnalysisBuilder 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  
ImageProxy 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  	ImageUtil 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  Int 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  List 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  Long 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  Map 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  
MutableMap 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  OutputImageFormat 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  Rect 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  ResettableCountDownLatch 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  Size 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  String 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  SuppressLint 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  System 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  
asExecutor 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  build 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  	configure 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  countDownLatch 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  cropRect 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  delay 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  executor 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  format 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  height 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  imagePlanesAdapter 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  imageProxyBaseAdapter 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  lastFrameAnalysisFinished 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  lastImageEmittedTimeStamp 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  launch 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  let 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  	lowercase 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  map 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  mapOf 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  maxFramesPerSecond 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  mutableMapOf 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  previewStreamSink 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  
roundToInt 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  roundToLong 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  set 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  to 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  width 7com.apparence.camerawesome.cameraX.ImageAnalysisBuilder  AspectRatio Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  	ByteArray Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  CoroutineScope Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  Dispatchers Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  
ImageAnalysis Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  ImageAnalysisBuilder Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  	ImageUtil Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  OutputImageFormat Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  Rect Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  ResettableCountDownLatch Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  Size Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  System Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  
asExecutor Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  	configure Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  countDownLatch Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  delay Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  lastImageEmittedTimeStamp Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  launch Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  let Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  	lowercase Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  map Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  mapOf Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  maxFramesPerSecond Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  mutableMapOf Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  
roundToInt Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  roundToLong Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  set Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  to Acom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.Companion  	EventSink Dcom.apparence.camerawesome.cameraX.ImageAnalysisBuilder.EventChannel  OnImageSavedCallback /com.apparence.camerawesome.cameraX.ImageCapture  OutputFileResults /com.apparence.camerawesome.cameraX.ImageCapture  ORIENTATION_UNKNOWN <com.apparence.camerawesome.cameraX.OrientationStreamListener  Surface <com.apparence.camerawesome.cameraX.OrientationStreamListener  currentOrientation <com.apparence.camerawesome.cameraX.OrientationStreamListener  	listeners <com.apparence.camerawesome.cameraX.OrientationStreamListener  orientationEventListener <com.apparence.camerawesome.cameraX.OrientationStreamListener  stop <com.apparence.camerawesome.cameraX.OrientationStreamListener  surfaceOrientation <com.apparence.camerawesome.cameraX.OrientationStreamListener  until <com.apparence.camerawesome.cameraX.OrientationStreamListener  JPEG 4com.apparence.camerawesome.cameraX.OutputImageFormat  NV21 4com.apparence.camerawesome.cameraX.OutputImageFormat  	RGBA_8888 4com.apparence.camerawesome.cameraX.OutputImageFormat  YUV_420_888 4com.apparence.camerawesome.cameraX.OutputImageFormat  name 4com.apparence.camerawesome.cameraX.OutputImageFormat  callback 4com.apparence.camerawesome.cameraX.PermissionRequest  permissionsAsked 4com.apparence.camerawesome.cameraX.PermissionRequest  Any /com.apparence.camerawesome.cameraX.PigeonSensor  	Companion /com.apparence.camerawesome.cameraX.PigeonSensor  Int /com.apparence.camerawesome.cameraX.PigeonSensor  List /com.apparence.camerawesome.cameraX.PigeonSensor  PigeonSensor /com.apparence.camerawesome.cameraX.PigeonSensor  PigeonSensorPosition /com.apparence.camerawesome.cameraX.PigeonSensor  PigeonSensorType /com.apparence.camerawesome.cameraX.PigeonSensor  String /com.apparence.camerawesome.cameraX.PigeonSensor  Suppress /com.apparence.camerawesome.cameraX.PigeonSensor  deviceId /com.apparence.camerawesome.cameraX.PigeonSensor  fromList /com.apparence.camerawesome.cameraX.PigeonSensor  listOf /com.apparence.camerawesome.cameraX.PigeonSensor  ofRaw /com.apparence.camerawesome.cameraX.PigeonSensor  position /com.apparence.camerawesome.cameraX.PigeonSensor  to /com.apparence.camerawesome.cameraX.PigeonSensor  toList /com.apparence.camerawesome.cameraX.PigeonSensor  type /com.apparence.camerawesome.cameraX.PigeonSensor  PigeonSensor 9com.apparence.camerawesome.cameraX.PigeonSensor.Companion  PigeonSensorPosition 9com.apparence.camerawesome.cameraX.PigeonSensor.Companion  PigeonSensorType 9com.apparence.camerawesome.cameraX.PigeonSensor.Companion  fromList 9com.apparence.camerawesome.cameraX.PigeonSensor.Companion  listOf 9com.apparence.camerawesome.cameraX.PigeonSensor.Companion  ofRaw 9com.apparence.camerawesome.cameraX.PigeonSensor.Companion  BACK 7com.apparence.camerawesome.cameraX.PigeonSensorPosition  	Companion 7com.apparence.camerawesome.cameraX.PigeonSensorPosition  FRONT 7com.apparence.camerawesome.cameraX.PigeonSensorPosition  Int 7com.apparence.camerawesome.cameraX.PigeonSensorPosition  PigeonSensorPosition 7com.apparence.camerawesome.cameraX.PigeonSensorPosition  firstOrNull 7com.apparence.camerawesome.cameraX.PigeonSensorPosition  ofRaw 7com.apparence.camerawesome.cameraX.PigeonSensorPosition  raw 7com.apparence.camerawesome.cameraX.PigeonSensorPosition  values 7com.apparence.camerawesome.cameraX.PigeonSensorPosition  firstOrNull Acom.apparence.camerawesome.cameraX.PigeonSensorPosition.Companion  ofRaw Acom.apparence.camerawesome.cameraX.PigeonSensorPosition.Companion  values Acom.apparence.camerawesome.cameraX.PigeonSensorPosition.Companion  	Companion 3com.apparence.camerawesome.cameraX.PigeonSensorType  Int 3com.apparence.camerawesome.cameraX.PigeonSensorType  PigeonSensorType 3com.apparence.camerawesome.cameraX.PigeonSensorType  	TELEPHOTO 3com.apparence.camerawesome.cameraX.PigeonSensorType  ULTRAWIDEANGLE 3com.apparence.camerawesome.cameraX.PigeonSensorType  UNKNOWN 3com.apparence.camerawesome.cameraX.PigeonSensorType  	WIDEANGLE 3com.apparence.camerawesome.cameraX.PigeonSensorType  firstOrNull 3com.apparence.camerawesome.cameraX.PigeonSensorType  ofRaw 3com.apparence.camerawesome.cameraX.PigeonSensorType  raw 3com.apparence.camerawesome.cameraX.PigeonSensorType  values 3com.apparence.camerawesome.cameraX.PigeonSensorType  firstOrNull =com.apparence.camerawesome.cameraX.PigeonSensorType.Companion  ofRaw =com.apparence.camerawesome.cameraX.PigeonSensorType.Companion  values =com.apparence.camerawesome.cameraX.PigeonSensorType.Companion  Any 9com.apparence.camerawesome.cameraX.PigeonSensorTypeDevice  Boolean 9com.apparence.camerawesome.cameraX.PigeonSensorTypeDevice  	Companion 9com.apparence.camerawesome.cameraX.PigeonSensorTypeDevice  Double 9com.apparence.camerawesome.cameraX.PigeonSensorTypeDevice  Int 9com.apparence.camerawesome.cameraX.PigeonSensorTypeDevice  List 9com.apparence.camerawesome.cameraX.PigeonSensorTypeDevice  PigeonSensorType 9com.apparence.camerawesome.cameraX.PigeonSensorTypeDevice  PigeonSensorTypeDevice 9com.apparence.camerawesome.cameraX.PigeonSensorTypeDevice  String 9com.apparence.camerawesome.cameraX.PigeonSensorTypeDevice  Suppress 9com.apparence.camerawesome.cameraX.PigeonSensorTypeDevice  flashAvailable 9com.apparence.camerawesome.cameraX.PigeonSensorTypeDevice  fromList 9com.apparence.camerawesome.cameraX.PigeonSensorTypeDevice  iso 9com.apparence.camerawesome.cameraX.PigeonSensorTypeDevice  listOf 9com.apparence.camerawesome.cameraX.PigeonSensorTypeDevice  name 9com.apparence.camerawesome.cameraX.PigeonSensorTypeDevice  ofRaw 9com.apparence.camerawesome.cameraX.PigeonSensorTypeDevice  
sensorType 9com.apparence.camerawesome.cameraX.PigeonSensorTypeDevice  toList 9com.apparence.camerawesome.cameraX.PigeonSensorTypeDevice  uid 9com.apparence.camerawesome.cameraX.PigeonSensorTypeDevice  PigeonSensorType Ccom.apparence.camerawesome.cameraX.PigeonSensorTypeDevice.Companion  PigeonSensorTypeDevice Ccom.apparence.camerawesome.cameraX.PigeonSensorTypeDevice.Companion  fromList Ccom.apparence.camerawesome.cameraX.PigeonSensorTypeDevice.Companion  listOf Ccom.apparence.camerawesome.cameraX.PigeonSensorTypeDevice.Companion  ofRaw Ccom.apparence.camerawesome.cameraX.PigeonSensorTypeDevice.Companion  Any /com.apparence.camerawesome.cameraX.PlaneWrapper  	ByteArray /com.apparence.camerawesome.cameraX.PlaneWrapper  	Companion /com.apparence.camerawesome.cameraX.PlaneWrapper  Int /com.apparence.camerawesome.cameraX.PlaneWrapper  List /com.apparence.camerawesome.cameraX.PlaneWrapper  Long /com.apparence.camerawesome.cameraX.PlaneWrapper  PlaneWrapper /com.apparence.camerawesome.cameraX.PlaneWrapper  Suppress /com.apparence.camerawesome.cameraX.PlaneWrapper  bytes /com.apparence.camerawesome.cameraX.PlaneWrapper  
bytesPerPixel /com.apparence.camerawesome.cameraX.PlaneWrapper  bytesPerRow /com.apparence.camerawesome.cameraX.PlaneWrapper  fromList /com.apparence.camerawesome.cameraX.PlaneWrapper  height /com.apparence.camerawesome.cameraX.PlaneWrapper  let /com.apparence.camerawesome.cameraX.PlaneWrapper  listOf /com.apparence.camerawesome.cameraX.PlaneWrapper  toList /com.apparence.camerawesome.cameraX.PlaneWrapper  width /com.apparence.camerawesome.cameraX.PlaneWrapper  PlaneWrapper 9com.apparence.camerawesome.cameraX.PlaneWrapper.Companion  fromList 9com.apparence.camerawesome.cameraX.PlaneWrapper.Companion  let 9com.apparence.camerawesome.cameraX.PlaneWrapper.Companion  listOf 9com.apparence.camerawesome.cameraX.PlaneWrapper.Companion  SurfaceProvider *com.apparence.camerawesome.cameraX.Preview  Any .com.apparence.camerawesome.cameraX.PreviewSize  	Companion .com.apparence.camerawesome.cameraX.PreviewSize  Double .com.apparence.camerawesome.cameraX.PreviewSize  List .com.apparence.camerawesome.cameraX.PreviewSize  PreviewSize .com.apparence.camerawesome.cameraX.PreviewSize  Suppress .com.apparence.camerawesome.cameraX.PreviewSize  fromList .com.apparence.camerawesome.cameraX.PreviewSize  height .com.apparence.camerawesome.cameraX.PreviewSize  listOf .com.apparence.camerawesome.cameraX.PreviewSize  toList .com.apparence.camerawesome.cameraX.PreviewSize  width .com.apparence.camerawesome.cameraX.PreviewSize  PreviewSize 8com.apparence.camerawesome.cameraX.PreviewSize.Companion  fromList 8com.apparence.camerawesome.cameraX.PreviewSize.Companion  listOf 8com.apparence.camerawesome.cameraX.PreviewSize.Companion  	Companion :com.apparence.camerawesome.cameraX.QualityFallbackStrategy  Int :com.apparence.camerawesome.cameraX.QualityFallbackStrategy  LOWER :com.apparence.camerawesome.cameraX.QualityFallbackStrategy  QualityFallbackStrategy :com.apparence.camerawesome.cameraX.QualityFallbackStrategy  firstOrNull :com.apparence.camerawesome.cameraX.QualityFallbackStrategy  ofRaw :com.apparence.camerawesome.cameraX.QualityFallbackStrategy  raw :com.apparence.camerawesome.cameraX.QualityFallbackStrategy  values :com.apparence.camerawesome.cameraX.QualityFallbackStrategy  firstOrNull Dcom.apparence.camerawesome.cameraX.QualityFallbackStrategy.Companion  ofRaw Dcom.apparence.camerawesome.cameraX.QualityFallbackStrategy.Companion  values Dcom.apparence.camerawesome.cameraX.QualityFallbackStrategy.Companion  SurfaceTextureEntry 2com.apparence.camerawesome.cameraX.TextureRegistry  AndroidVideoOptions /com.apparence.camerawesome.cameraX.VideoOptions  Any /com.apparence.camerawesome.cameraX.VideoOptions  Boolean /com.apparence.camerawesome.cameraX.VideoOptions  	Companion /com.apparence.camerawesome.cameraX.VideoOptions  CupertinoVideoOptions /com.apparence.camerawesome.cameraX.VideoOptions  Int /com.apparence.camerawesome.cameraX.VideoOptions  List /com.apparence.camerawesome.cameraX.VideoOptions  Suppress /com.apparence.camerawesome.cameraX.VideoOptions  VideoOptions /com.apparence.camerawesome.cameraX.VideoOptions  VideoRecordingQuality /com.apparence.camerawesome.cameraX.VideoOptions  android /com.apparence.camerawesome.cameraX.VideoOptions  enableAudio /com.apparence.camerawesome.cameraX.VideoOptions  fromList /com.apparence.camerawesome.cameraX.VideoOptions  ios /com.apparence.camerawesome.cameraX.VideoOptions  let /com.apparence.camerawesome.cameraX.VideoOptions  listOf /com.apparence.camerawesome.cameraX.VideoOptions  ofRaw /com.apparence.camerawesome.cameraX.VideoOptions  quality /com.apparence.camerawesome.cameraX.VideoOptions  toList /com.apparence.camerawesome.cameraX.VideoOptions  AndroidVideoOptions 9com.apparence.camerawesome.cameraX.VideoOptions.Companion  CupertinoVideoOptions 9com.apparence.camerawesome.cameraX.VideoOptions.Companion  VideoOptions 9com.apparence.camerawesome.cameraX.VideoOptions.Companion  VideoRecordingQuality 9com.apparence.camerawesome.cameraX.VideoOptions.Companion  fromList 9com.apparence.camerawesome.cameraX.VideoOptions.Companion  let 9com.apparence.camerawesome.cameraX.VideoOptions.Companion  listOf 9com.apparence.camerawesome.cameraX.VideoOptions.Companion  ofRaw 9com.apparence.camerawesome.cameraX.VideoOptions.Companion  Finalize 3com.apparence.camerawesome.cameraX.VideoRecordEvent  Start 3com.apparence.camerawesome.cameraX.VideoRecordEvent  	Companion 8com.apparence.camerawesome.cameraX.VideoRecordingQuality  FHD 8com.apparence.camerawesome.cameraX.VideoRecordingQuality  HD 8com.apparence.camerawesome.cameraX.VideoRecordingQuality  Int 8com.apparence.camerawesome.cameraX.VideoRecordingQuality  LOWEST 8com.apparence.camerawesome.cameraX.VideoRecordingQuality  SD 8com.apparence.camerawesome.cameraX.VideoRecordingQuality  UHD 8com.apparence.camerawesome.cameraX.VideoRecordingQuality  VideoRecordingQuality 8com.apparence.camerawesome.cameraX.VideoRecordingQuality  firstOrNull 8com.apparence.camerawesome.cameraX.VideoRecordingQuality  ofRaw 8com.apparence.camerawesome.cameraX.VideoRecordingQuality  raw 8com.apparence.camerawesome.cameraX.VideoRecordingQuality  values 8com.apparence.camerawesome.cameraX.VideoRecordingQuality  firstOrNull Bcom.apparence.camerawesome.cameraX.VideoRecordingQuality.Companion  ofRaw Bcom.apparence.camerawesome.cameraX.VideoRecordingQuality.Companion  values Bcom.apparence.camerawesome.cameraX.VideoRecordingQuality.Companion  OnAttachStateChangeListener 'com.apparence.camerawesome.cameraX.View  
annotation +com.apparence.camerawesome.cameraX.androidx  OptIn 6com.apparence.camerawesome.cameraX.androidx.annotation  	Exception %com.apparence.camerawesome.exceptions  PermissionNotDeclaredException %com.apparence.camerawesome.exceptions  String %com.apparence.camerawesome.exceptions  	FlashMode !com.apparence.camerawesome.models  ALWAYS +com.apparence.camerawesome.models.FlashMode  AUTO +com.apparence.camerawesome.models.FlashMode  NONE +com.apparence.camerawesome.models.FlashMode  ON +com.apparence.camerawesome.models.FlashMode  valueOf +com.apparence.camerawesome.models.FlashMode  Any "com.apparence.camerawesome.sensors  EventChannel "com.apparence.camerawesome.sensors  	EventSink "com.apparence.camerawesome.sensors  Int "com.apparence.camerawesome.sensors  SensorOrientation "com.apparence.camerawesome.sensors  SensorOrientationListener "com.apparence.camerawesome.sensors  
StreamHandler /com.apparence.camerawesome.sensors.EventChannel  onOrientationChanged 4com.apparence.camerawesome.sensors.SensorOrientation  events <com.apparence.camerawesome.sensors.SensorOrientationListener  AbstractQueuedSynchronizer  com.apparence.camerawesome.utils  Boolean  com.apparence.camerawesome.utils  Camera2CameraInfo  com.apparence.camerawesome.utils  CameraCharacteristics  com.apparence.camerawesome.utils  ExperimentalCamera2Interop  com.apparence.camerawesome.utils  Float  com.apparence.camerawesome.utils  Int  com.apparence.camerawesome.utils  InterruptedException  com.apparence.camerawesome.utils  LENS_FACING_BACK  com.apparence.camerawesome.utils  Long  com.apparence.camerawesome.utils  PigeonSensorPosition  com.apparence.camerawesome.utils  PigeonSensorType  com.apparence.camerawesome.utils  ProcessCameraProvider  com.apparence.camerawesome.utils  ResettableCountDownLatch  com.apparence.camerawesome.utils  Size  com.apparence.camerawesome.utils  Size35mm  com.apparence.camerawesome.utils  SizeF  com.apparence.camerawesome.utils  String  com.apparence.camerawesome.utils  SuppressLint  com.apparence.camerawesome.utils  Sync  com.apparence.camerawesome.utils  Throws  com.apparence.camerawesome.utils  TimeUnit  com.apparence.camerawesome.utils  any  com.apparence.camerawesome.utils  bigger  com.apparence.camerawesome.utils  getPigeonPosition  com.apparence.camerawesome.utils  
getSensorType  com.apparence.camerawesome.utils  isMultiCamSupported  com.apparence.camerawesome.utils  max  com.apparence.camerawesome.utils  min  com.apparence.camerawesome.utils  require  com.apparence.camerawesome.utils  smaller  com.apparence.camerawesome.utils  AbstractQueuedSynchronizer 9com.apparence.camerawesome.utils.ResettableCountDownLatch  Boolean 9com.apparence.camerawesome.utils.ResettableCountDownLatch  Int 9com.apparence.camerawesome.utils.ResettableCountDownLatch  InterruptedException 9com.apparence.camerawesome.utils.ResettableCountDownLatch  Long 9com.apparence.camerawesome.utils.ResettableCountDownLatch  String 9com.apparence.camerawesome.utils.ResettableCountDownLatch  Sync 9com.apparence.camerawesome.utils.ResettableCountDownLatch  Throws 9com.apparence.camerawesome.utils.ResettableCountDownLatch  TimeUnit 9com.apparence.camerawesome.utils.ResettableCountDownLatch  await 9com.apparence.camerawesome.utils.ResettableCountDownLatch  	countDown 9com.apparence.camerawesome.utils.ResettableCountDownLatch  require 9com.apparence.camerawesome.utils.ResettableCountDownLatch  reset 9com.apparence.camerawesome.utils.ResettableCountDownLatch  sync 9com.apparence.camerawesome.utils.ResettableCountDownLatch  Boolean >com.apparence.camerawesome.utils.ResettableCountDownLatch.Sync  Int >com.apparence.camerawesome.utils.ResettableCountDownLatch.Sync  acquireSharedInterruptibly >com.apparence.camerawesome.utils.ResettableCountDownLatch.Sync  compareAndSetState >com.apparence.camerawesome.utils.ResettableCountDownLatch.Sync  count >com.apparence.camerawesome.utils.ResettableCountDownLatch.Sync  
releaseShared >com.apparence.camerawesome.utils.ResettableCountDownLatch.Sync  reset >com.apparence.camerawesome.utils.ResettableCountDownLatch.Sync  
startCount >com.apparence.camerawesome.utils.ResettableCountDownLatch.Sync  state >com.apparence.camerawesome.utils.ResettableCountDownLatch.Sync  tryAcquireSharedNanos >com.apparence.camerawesome.utils.ResettableCountDownLatch.Sync  FlutterActivity "com.apparence.camerawesome_example  MainActivity "com.apparence.camerawesome_example  FusedLocationProviderClient com.google.android.gms.location  LocationServices com.google.android.gms.location  Priority com.google.android.gms.location  getCurrentLocation ;com.google.android.gms.location.FusedLocationProviderClient  getFusedLocationProviderClient 0com.google.android.gms.location.LocationServices  PRIORITY_HIGH_ACCURACY (com.google.android.gms.location.Priority  CancellationToken com.google.android.gms.tasks  CancellationTokenSource com.google.android.gms.tasks  OnCompleteListener com.google.android.gms.tasks  Task com.google.android.gms.tasks  cancel 4com.google.android.gms.tasks.CancellationTokenSource  token 4com.google.android.gms.tasks.CancellationTokenSource  <SAM-CONSTRUCTOR> /com.google.android.gms.tasks.OnCompleteListener  addOnCompleteListener !com.google.android.gms.tasks.Task  	exception !com.google.android.gms.tasks.Task  isSuccessful !com.google.android.gms.tasks.Task  result !com.google.android.gms.tasks.Task  ListenableFuture !com.google.common.util.concurrent  get 2com.google.common.util.concurrent.ListenableFuture  FlutterActivity io.flutter.embedding.android  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  textureRegistry Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  
ActivityAware ,io.flutter.embedding.engine.plugins.activity  ActivityPluginBinding ,io.flutter.embedding.engine.plugins.activity  activity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  #addRequestPermissionsResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  BasicMessageChannel io.flutter.plugin.common  BinaryMessenger io.flutter.plugin.common  EventChannel io.flutter.plugin.common  MessageCodec io.flutter.plugin.common  StandardMessageCodec io.flutter.plugin.common  MessageHandler ,io.flutter.plugin.common.BasicMessageChannel  Reply ,io.flutter.plugin.common.BasicMessageChannel  setMessageHandler ,io.flutter.plugin.common.BasicMessageChannel  <SAM-CONSTRUCTOR> ;io.flutter.plugin.common.BasicMessageChannel.MessageHandler  reply 2io.flutter.plugin.common.BasicMessageChannel.Reply  	EventSink %io.flutter.plugin.common.EventChannel  
StreamHandler %io.flutter.plugin.common.EventChannel  setStreamHandler %io.flutter.plugin.common.EventChannel  endOfStream /io.flutter.plugin.common.EventChannel.EventSink  success /io.flutter.plugin.common.EventChannel.EventSink   RequestPermissionsResultListener 'io.flutter.plugin.common.PluginRegistry  AnalysisImageWrapper -io.flutter.plugin.common.StandardMessageCodec  AndroidFocusSettings -io.flutter.plugin.common.StandardMessageCodec  AndroidVideoOptions -io.flutter.plugin.common.StandardMessageCodec  Any -io.flutter.plugin.common.StandardMessageCodec  CropRectWrapper -io.flutter.plugin.common.StandardMessageCodec  CupertinoVideoOptions -io.flutter.plugin.common.StandardMessageCodec  ExifPreferences -io.flutter.plugin.common.StandardMessageCodec  List -io.flutter.plugin.common.StandardMessageCodec  PigeonSensor -io.flutter.plugin.common.StandardMessageCodec  PigeonSensorTypeDevice -io.flutter.plugin.common.StandardMessageCodec  PlaneWrapper -io.flutter.plugin.common.StandardMessageCodec  PreviewSize -io.flutter.plugin.common.StandardMessageCodec  VideoOptions -io.flutter.plugin.common.StandardMessageCodec  fromList -io.flutter.plugin.common.StandardMessageCodec  let -io.flutter.plugin.common.StandardMessageCodec  	readValue -io.flutter.plugin.common.StandardMessageCodec  readValueOfType -io.flutter.plugin.common.StandardMessageCodec  
writeValue -io.flutter.plugin.common.StandardMessageCodec  TextureRegistry io.flutter.view  SurfaceTextureEntry io.flutter.view.TextureRegistry  createSurfaceTexture io.flutter.view.TextureRegistry  id 3io.flutter.view.TextureRegistry.SurfaceTextureEntry  surfaceTexture 3io.flutter.view.TextureRegistry.SurfaceTextureEntry  id ,io.flutter.view.TextureRegistry.TextureEntry  
Observable io.reactivex.rxjava3.core  create $io.reactivex.rxjava3.core.Observable  
Disposable  io.reactivex.rxjava3.disposables  dispose +io.reactivex.rxjava3.disposables.Disposable  Consumer io.reactivex.rxjava3.functions  <SAM-CONSTRUCTOR> 'io.reactivex.rxjava3.functions.Consumer  BehaviorSubject io.reactivex.rxjava3.subjects  create -io.reactivex.rxjava3.subjects.BehaviorSubject  onNext -io.reactivex.rxjava3.subjects.BehaviorSubject  	subscribe -io.reactivex.rxjava3.subjects.BehaviorSubject  ByteArrayOutputStream java.io  File java.io  FileOutputStream java.io  IOException java.io  Serializable java.io  toByteArray java.io.ByteArrayOutputStream  write java.io.ByteArrayOutputStream  mkdirs java.io.File  
parentFile java.io.File  use java.io.FileOutputStream  printStackTrace java.io.IOException  write java.io.OutputStream  Class 	java.lang  	Exception 	java.lang  IllegalArgumentException 	java.lang  IllegalStateException 	java.lang  InterruptedException 	java.lang  Runnable 	java.lang  name java.lang.Class  
simpleName java.lang.Class  min java.lang.Math  round java.lang.Math  <SAM-CONSTRUCTOR> java.lang.Runnable  join java.lang.String  currentTimeMillis java.lang.System  
WeakReference 
java.lang.ref  get java.lang.ref.WeakReference  
ByteBuffer java.nio  position java.nio.Buffer  	remaining java.nio.Buffer  get java.nio.ByteBuffer  position java.nio.ByteBuffer  rewind java.nio.ByteBuffer  wrap java.nio.ByteBuffer  Activity 	java.util  ActivityCompat 	java.util  Any 	java.util  Array 	java.util  	ArrayList 	java.util  AutoFitPreviewBuilder 	java.util  Boolean 	java.util  CameraPermissions 	java.util  Context 	java.util  
ContextCompat 	java.util  Continuation 	java.util  CoroutineScope 	java.util  Dispatchers 	java.util  Display 	java.util  DisplayManager 	java.util  EventChannel 	java.util  	EventSink 	java.util  IllegalArgumentException 	java.util  Int 	java.util  IntArray 	java.util  
LinkedHashSet 	java.util  List 	java.util  Log 	java.util  Manifest 	java.util  Math 	java.util  Matrix 	java.util  MutableList 	java.util  Objects 	java.util  PERMISSIONS_MULTIPLE_REQUEST 	java.util  PackageManager 	java.util  PermissionNotDeclaredException 	java.util  PermissionRequest 	java.util  Preview 	java.util  
PreviewConfig 	java.util   RequestPermissionsResultListener 	java.util  Size 	java.util  String 	java.util  SuppressLint 	java.util  Surface 	java.util  TAG 	java.util  TextureView 	java.util  UUID 	java.util  Unit 	java.util  View 	java.util  
WeakReference 	java.util  allPermissions 	java.util  apply 	java.util  bufferDimens 	java.util  displayListener 	java.util  displayManager 	java.util  getDisplaySurfaceRotation 	java.util  indices 	java.util  
isNullOrEmpty 	java.util  java 	java.util  launch 	java.util  listOf 	java.util  
mutableListOf 	java.util  requestPermissions 	java.util  resume 	java.util  
roundToInt 	java.util  suspendCoroutine 	java.util  toList 	java.util  toTypedArray 	java.util  updateTransform 	java.util  viewFinderDimens 	java.util  viewFinderDisplay 	java.util  DisplayListener java.util.DisplayManager  
StreamHandler java.util.EventChannel  equals java.util.Objects  
randomUUID java.util.UUID  toString java.util.UUID  OnAttachStateChangeListener java.util.View  Executor java.util.concurrent  TimeUnit java.util.concurrent  execute java.util.concurrent.Executor  MILLISECONDS java.util.concurrent.TimeUnit  SECONDS java.util.concurrent.TimeUnit  toNanos java.util.concurrent.TimeUnit  AbstractQueuedSynchronizer java.util.concurrent.locks  acquireSharedInterruptibly 5java.util.concurrent.locks.AbstractQueuedSynchronizer  compareAndSetState 5java.util.concurrent.locks.AbstractQueuedSynchronizer  
releaseShared 5java.util.concurrent.locks.AbstractQueuedSynchronizer  state 5java.util.concurrent.locks.AbstractQueuedSynchronizer  tryAcquireSharedNanos 5java.util.concurrent.locks.AbstractQueuedSynchronizer  Array kotlin  	ByteArray kotlin  
Comparable kotlin  
Deprecated kotlin  Enum kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	Function9 kotlin  IntArray kotlin  Lazy kotlin  Nothing kotlin  Pair kotlin  Result kotlin  Suppress kotlin  TODO kotlin  	Throwable kotlin  apply kotlin  getValue kotlin  lazy kotlin  let kotlin  map kotlin  	onFailure kotlin  	onSuccess kotlin  require kotlin  run kotlin  to kotlin  toList kotlin  use kotlin  let 
kotlin.Any  toString 
kotlin.Any  firstOrNull kotlin.Array  get kotlin.Array  indices kotlin.Array  iterator kotlin.Array  toList kotlin.Array  not kotlin.Boolean  toInt kotlin.Byte  get kotlin.ByteArray  set kotlin.ByteArray  size kotlin.ByteArray  	compareTo 
kotlin.Double  div 
kotlin.Double  let 
kotlin.Double  plus 
kotlin.Double  
roundToInt 
kotlin.Double  roundToLong 
kotlin.Double  times 
kotlin.Double  toFloat 
kotlin.Double  toInt 
kotlin.Double  AnalysisImageFormat kotlin.Enum  AnalysisRotation kotlin.Enum  CamerAwesomePermission kotlin.Enum  	Companion kotlin.Enum  CupertinoCodecType kotlin.Enum  CupertinoFileType kotlin.Enum  Int kotlin.Enum  PigeonSensorPosition kotlin.Enum  PigeonSensorType kotlin.Enum  QualityFallbackStrategy kotlin.Enum  VideoRecordingQuality kotlin.Enum  firstOrNull kotlin.Enum  values kotlin.Enum  firstOrNull kotlin.Enum.Companion  values kotlin.Enum.Companion  	compareTo kotlin.Float  div kotlin.Float  
roundToInt kotlin.Float  times kotlin.Float  toDouble kotlin.Float  toInt kotlin.Float  
unaryMinus kotlin.Float  any kotlin.FloatArray  invoke kotlin.Function1  invoke kotlin.Function2  and 
kotlin.Int  	compareTo 
kotlin.Int  div 
kotlin.Int  inc 
kotlin.Int  let 
kotlin.Int  minus 
kotlin.Int  or 
kotlin.Int  plus 
kotlin.Int  
plusAssign 
kotlin.Int  rem 
kotlin.Int  shl 
kotlin.Int  times 
kotlin.Int  toByte 
kotlin.Int  toDouble 
kotlin.Int  toFloat 
kotlin.Int  toLong 
kotlin.Int  toString 
kotlin.Int  get kotlin.IntArray  set kotlin.IntArray  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  	compareTo kotlin.Long  minus kotlin.Long  toInt kotlin.Long  toString kotlin.Long  	Companion 
kotlin.Result  exceptionOrNull 
kotlin.Result  failure 
kotlin.Result  	getOrNull 
kotlin.Result  	onFailure 
kotlin.Result  	onSuccess 
kotlin.Result  success 
kotlin.Result  failure kotlin.Result.Companion  success kotlin.Result.Companion  	lowercase 
kotlin.String  plus 
kotlin.String  to 
kotlin.String  	uppercase 
kotlin.String  cause kotlin.Throwable  	javaClass kotlin.Throwable  message kotlin.Throwable  printStackTrace kotlin.Throwable  toString kotlin.Throwable  IndexedValue kotlin.collections  IntIterator kotlin.collections  Iterable kotlin.collections  Iterator kotlin.collections  List kotlin.collections  LongIterator kotlin.collections  Map kotlin.collections  MutableCollection kotlin.collections  MutableIterator kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  Set kotlin.collections  all kotlin.collections  any kotlin.collections  	buildList kotlin.collections  first kotlin.collections  firstOrNull kotlin.collections  forEach kotlin.collections  	getOrNull kotlin.collections  getValue kotlin.collections  indices kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrEmpty kotlin.collections  listOf kotlin.collections  map kotlin.collections  
mapIndexed kotlin.collections  
mapNotNull kotlin.collections  mapOf kotlin.collections  	mapValues kotlin.collections  max kotlin.collections  min kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  
plusAssign kotlin.collections  set kotlin.collections  toFloatArray kotlin.collections  toList kotlin.collections  toMap kotlin.collections  toMutableMap kotlin.collections  toTypedArray kotlin.collections  	withIndex kotlin.collections  
component1 kotlin.collections.IndexedValue  
component2 kotlin.collections.IndexedValue  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  iterator kotlin.collections.Iterable  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  contains kotlin.collections.List  containsAll kotlin.collections.List  first kotlin.collections.List  get kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  iterator kotlin.collections.List  let kotlin.collections.List  map kotlin.collections.List  
mapIndexed kotlin.collections.List  
mapNotNull kotlin.collections.List  size kotlin.collections.List  toFloatArray kotlin.collections.List  toMap kotlin.collections.List  toTypedArray kotlin.collections.List  	withIndex kotlin.collections.List  hasNext kotlin.collections.LongIterator  next kotlin.collections.LongIterator  Entry kotlin.collections.Map  entries kotlin.collections.Map  get kotlin.collections.Map  	mapValues kotlin.collections.Map  size kotlin.collections.Map  toMutableMap kotlin.collections.Map  key kotlin.collections.Map.Entry  value kotlin.collections.Map.Entry  	withIndex $kotlin.collections.MutableCollection  hasNext "kotlin.collections.MutableIterator  next "kotlin.collections.MutableIterator  BehaviorSubject kotlin.collections.MutableList  add kotlin.collections.MutableList  addAll kotlin.collections.MutableList  all kotlin.collections.MutableList  apply kotlin.collections.MutableList  clear kotlin.collections.MutableList  contains kotlin.collections.MutableList  first kotlin.collections.MutableList  get kotlin.collections.MutableList  	getOrNull kotlin.collections.MutableList  isEmpty kotlin.collections.MutableList  iterator kotlin.collections.MutableList  remove kotlin.collections.MutableList  	removeAll kotlin.collections.MutableList  size kotlin.collections.MutableList  until kotlin.collections.MutableList  all kotlin.collections.MutableMap  clear kotlin.collections.MutableMap  set kotlin.collections.MutableMap  size kotlin.collections.MutableMap  values kotlin.collections.MutableMap  	withIndex kotlin.collections.Set  Continuation kotlin.coroutines  SuspendFunction1 kotlin.coroutines  resume kotlin.coroutines  suspendCoroutine kotlin.coroutines  resume kotlin.coroutines.Continuation  use 	kotlin.io  Throws 
kotlin.jvm  java 
kotlin.jvm  	javaClass 
kotlin.jvm  max kotlin.math  min kotlin.math  
roundToInt kotlin.math  roundToLong kotlin.math  	CharRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  first 
kotlin.ranges  firstOrNull 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  contains kotlin.ranges.IntRange  iterator kotlin.ranges.IntRange  iterator kotlin.ranges.LongProgression  iterator kotlin.ranges.LongRange  KClass kotlin.reflect  
KProperty1 kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  all kotlin.sequences  any kotlin.sequences  first kotlin.sequences  firstOrNull kotlin.sequences  forEach kotlin.sequences  map kotlin.sequences  
mapIndexed kotlin.sequences  
mapNotNull kotlin.sequences  max kotlin.sequences  min kotlin.sequences  toList kotlin.sequences  	withIndex kotlin.sequences  all kotlin.text  any kotlin.text  first kotlin.text  firstOrNull kotlin.text  forEach kotlin.text  	getOrNull kotlin.text  indices kotlin.text  
isNotEmpty kotlin.text  
isNullOrEmpty kotlin.text  	lowercase kotlin.text  map kotlin.text  
mapIndexed kotlin.text  
mapNotNull kotlin.text  max kotlin.text  min kotlin.text  set kotlin.text  toList kotlin.text  	uppercase kotlin.text  	withIndex kotlin.text  Activity kotlinx.coroutines  
ActivityAware kotlinx.coroutines  ActivityCompat kotlinx.coroutines  ActivityPluginBinding kotlinx.coroutines  AnalysisImageConverter kotlinx.coroutines  AnalysisImageUtils kotlinx.coroutines  AndroidFocusSettings kotlinx.coroutines  Any kotlinx.coroutines  AspectRatio kotlinx.coroutines  BehaviorSubject kotlinx.coroutines  Bitmap kotlinx.coroutines  
BitmapFactory kotlinx.coroutines  Boolean kotlinx.coroutines  Build kotlinx.coroutines  	ByteArray kotlinx.coroutines  CamerAwesomePermission kotlinx.coroutines  
Camera2Config kotlinx.coroutines  CameraCapabilities kotlinx.coroutines  CameraCharacteristics kotlinx.coroutines  CameraInfoUnavailableException kotlinx.coroutines  CameraInterface kotlinx.coroutines  CameraPermissions kotlinx.coroutines  CameraSelector kotlinx.coroutines  
CameraXConfig kotlinx.coroutines  CameraXState kotlinx.coroutines  CamerawesomePlugin kotlinx.coroutines  CancellableContinuation kotlinx.coroutines  CancellationTokenSource kotlinx.coroutines  Canvas kotlinx.coroutines  CaptureModes kotlinx.coroutines  ColorMatrixColorFilter kotlinx.coroutines  Consumer kotlinx.coroutines  
ContextCompat kotlinx.coroutines  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  CountDownTimer kotlinx.coroutines  Delay kotlinx.coroutines  
Deprecated kotlinx.coroutines  Dispatchers kotlinx.coroutines  
Disposable kotlinx.coroutines  Double kotlinx.coroutines  EventChannel kotlinx.coroutines  	Exception kotlinx.coroutines  Executor kotlinx.coroutines  
ExifInterface kotlinx.coroutines  ExifPreferences kotlinx.coroutines  ExperimentalCamera2Interop kotlinx.coroutines  File kotlinx.coroutines  FileOutputOptions kotlinx.coroutines  FileOutputStream kotlinx.coroutines  	FlashMode kotlinx.coroutines  
FlutterPlugin kotlinx.coroutines  FlutterPluginBinding kotlinx.coroutines  FocusMeteringAction kotlinx.coroutines  FusedLocationProviderClient kotlinx.coroutines  Handler kotlinx.coroutines  IOException kotlinx.coroutines  IllegalStateException kotlinx.coroutines  
ImageAnalysis kotlinx.coroutines  ImageAnalysisBuilder kotlinx.coroutines  ImageCapture kotlinx.coroutines  ImageCaptureException kotlinx.coroutines  
ImageProxy kotlinx.coroutines  	ImageUtil kotlinx.coroutines  Int kotlinx.coroutines  Intent kotlinx.coroutines  Job kotlinx.coroutines  List kotlinx.coroutines  Location kotlinx.coroutines  LocationServices kotlinx.coroutines  Log kotlinx.coroutines  Long kotlinx.coroutines  Looper kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  Manifest kotlinx.coroutines  Map kotlinx.coroutines  	Messenger kotlinx.coroutines  MeteringPointFactory kotlinx.coroutines  MutableList kotlinx.coroutines  
MutableMap kotlinx.coroutines  OrientationStreamListener kotlinx.coroutines  OutputImageFormat kotlinx.coroutines  PackageManager kotlinx.coroutines  Paint kotlinx.coroutines  PhysicalButtonMessageHandler kotlinx.coroutines  PhysicalButtonsHandler kotlinx.coroutines  PigeonSensor kotlinx.coroutines  PigeonSensorPosition kotlinx.coroutines  PigeonSensorTypeDevice kotlinx.coroutines  
PlayerService kotlinx.coroutines  PreviewSize kotlinx.coroutines  Priority kotlinx.coroutines  ProcessCameraProvider kotlinx.coroutines  Rational kotlinx.coroutines  Rect kotlinx.coroutines  ResettableCountDownLatch kotlinx.coroutines  Result kotlinx.coroutines  SensorOrientationListener kotlinx.coroutines  Size kotlinx.coroutines  String kotlinx.coroutines  SuppressLint kotlinx.coroutines  #SurfaceOrientedMeteringPointFactory kotlinx.coroutines  System kotlinx.coroutines  TODO kotlinx.coroutines  TextureRegistry kotlinx.coroutines  TimeUnit kotlinx.coroutines  Unit kotlinx.coroutines  VideoOptions kotlinx.coroutines  VideoRecordEvent kotlinx.coroutines  activity kotlinx.coroutines  all kotlinx.coroutines  apply kotlinx.coroutines  
asExecutor kotlinx.coroutines  	buildList kotlinx.coroutines  cameraPermissions kotlinx.coroutines  cameraState kotlinx.coroutines  colorMatrix kotlinx.coroutines  	configure kotlinx.coroutines  configureInstance kotlinx.coroutines  countDownLatch kotlinx.coroutines  delay kotlinx.coroutines  exifPreferences kotlinx.coroutines  first kotlinx.coroutines  firstOrNull kotlinx.coroutines  forEach kotlinx.coroutines  getCameraLevel kotlinx.coroutines  getInstance kotlinx.coroutines  	getOrNull kotlinx.coroutines  getOrientedSize kotlinx.coroutines  isMultiCamSupported kotlinx.coroutines  
isNotEmpty kotlinx.coroutines  java kotlinx.coroutines  lastImageEmittedTimeStamp kotlinx.coroutines  lastRecordedVideoSubscriptions kotlinx.coroutines  lastRecordedVideos kotlinx.coroutines  launch kotlinx.coroutines  let kotlinx.coroutines  listOf kotlinx.coroutines  	lowercase kotlinx.coroutines  map kotlinx.coroutines  
mapIndexed kotlinx.coroutines  
mapNotNull kotlinx.coroutines  mapOf kotlinx.coroutines  	mapValues kotlinx.coroutines  maxFramesPerSecond kotlinx.coroutines  
mutableListOf kotlinx.coroutines  mutableMapOf kotlinx.coroutines  
noneFilter kotlinx.coroutines  orientationStreamListener kotlinx.coroutines  resume kotlinx.coroutines  retrieveLocation kotlinx.coroutines  
roundToInt kotlinx.coroutines  roundToLong kotlinx.coroutines  run kotlinx.coroutines  set kotlinx.coroutines  setUp kotlinx.coroutines  success kotlinx.coroutines  suspendCancellableCoroutine kotlinx.coroutines  
takePhotoWith kotlinx.coroutines  to kotlinx.coroutines  toFloatArray kotlinx.coroutines  toMap kotlinx.coroutines  toMutableMap kotlinx.coroutines  until kotlinx.coroutines  	uppercase kotlinx.coroutines  use kotlinx.coroutines  	withIndex kotlinx.coroutines  isActive *kotlinx.coroutines.CancellableContinuation  resume *kotlinx.coroutines.CancellableContinuation  
asExecutor &kotlinx.coroutines.CoroutineDispatcher  BehaviorSubject !kotlinx.coroutines.CoroutineScope  CameraPermissions !kotlinx.coroutines.CoroutineScope  CamerawesomePlugin !kotlinx.coroutines.CoroutineScope  Consumer !kotlinx.coroutines.CoroutineScope  Dispatchers !kotlinx.coroutines.CoroutineScope  File !kotlinx.coroutines.CoroutineScope  FileOutputOptions !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  Manifest !kotlinx.coroutines.CoroutineScope  PERMISSIONS_MULTIPLE_REQUEST !kotlinx.coroutines.CoroutineScope  Result !kotlinx.coroutines.CoroutineScope  System !kotlinx.coroutines.CoroutineScope  Unit !kotlinx.coroutines.CoroutineScope  activity !kotlinx.coroutines.CoroutineScope  all !kotlinx.coroutines.CoroutineScope  apply !kotlinx.coroutines.CoroutineScope  	buildList !kotlinx.coroutines.CoroutineScope  cameraPermissions !kotlinx.coroutines.CoroutineScope  cameraState !kotlinx.coroutines.CoroutineScope  countDownLatch !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  
isNotEmpty !kotlinx.coroutines.CoroutineScope  lastImageEmittedTimeStamp !kotlinx.coroutines.CoroutineScope  lastRecordedVideoSubscriptions !kotlinx.coroutines.CoroutineScope  lastRecordedVideos !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope  listOf !kotlinx.coroutines.CoroutineScope  	mapValues !kotlinx.coroutines.CoroutineScope  maxFramesPerSecond !kotlinx.coroutines.CoroutineScope  
mutableListOf !kotlinx.coroutines.CoroutineScope  orientationStreamListener !kotlinx.coroutines.CoroutineScope  requestPermissions !kotlinx.coroutines.CoroutineScope  
roundToInt !kotlinx.coroutines.CoroutineScope  roundToLong !kotlinx.coroutines.CoroutineScope  run !kotlinx.coroutines.CoroutineScope  set !kotlinx.coroutines.CoroutineScope  success !kotlinx.coroutines.CoroutineScope  
takePhotoWith !kotlinx.coroutines.CoroutineScope  toMutableMap !kotlinx.coroutines.CoroutineScope  until !kotlinx.coroutines.CoroutineScope  	withIndex !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  Main kotlinx.coroutines.Dispatchers  	EventSink kotlinx.coroutines.EventChannel  OnImageSavedCallback kotlinx.coroutines.ImageCapture  OutputFileResults kotlinx.coroutines.ImageCapture  Result *kotlinx.coroutines.MainCoroutineDispatcher  
isNotEmpty *kotlinx.coroutines.MainCoroutineDispatcher  run *kotlinx.coroutines.MainCoroutineDispatcher  success *kotlinx.coroutines.MainCoroutineDispatcher  Finalize #kotlinx.coroutines.VideoRecordEvent  Start #kotlinx.coroutines.VideoRecordEvent                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                