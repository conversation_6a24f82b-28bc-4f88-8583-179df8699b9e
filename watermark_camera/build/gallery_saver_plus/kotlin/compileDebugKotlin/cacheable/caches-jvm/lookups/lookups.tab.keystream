  Manifest android  WRITE_EXTERNAL_STORAGE android.Manifest.permission  Activity android.app  contentResolver android.app.Activity  ContentResolver android.content  ContentUris android.content  
ContentValues android.content  delete android.content.ContentResolver  insert android.content.ContentResolver  openOutputStream android.content.ContentResolver  parseId android.content.ContentUris  put android.content.ContentValues  contentResolver android.content.ContextWrapper  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  Bitmap android.graphics  
BitmapFactory android.graphics  Matrix android.graphics  CompressFormat android.graphics.Bitmap  compress android.graphics.Bitmap  createBitmap android.graphics.Bitmap  height android.graphics.Bitmap  recycle android.graphics.Bitmap  width android.graphics.Bitmap  JPEG &android.graphics.Bitmap.CompressFormat  decodeByteArray android.graphics.BitmapFactory  	preRotate android.graphics.Matrix  setScale android.graphics.Matrix  MediaMetadataRetriever 
android.media  METADATA_KEY_DURATION $android.media.MediaMetadataRetriever  extractMetadata $android.media.MediaMetadataRetriever  
setDataSource $android.media.MediaMetadataRetriever  Uri android.net  Environment 
android.os  SDK_INT android.os.Build.VERSION  DIRECTORY_DCIM android.os.Environment  DIRECTORY_MOVIES android.os.Environment  DIRECTORY_PICTURES android.os.Environment  getExternalStorageDirectory android.os.Environment  !getExternalStoragePublicDirectory android.os.Environment  
MediaStore android.provider  DATA /android.provider.MediaStore.Images.ImageColumns  
DATE_ADDED (android.provider.MediaStore.Images.Media  
DATE_MODIFIED (android.provider.MediaStore.Images.Media  
DATE_TAKEN (android.provider.MediaStore.Images.Media  DISPLAY_NAME (android.provider.MediaStore.Images.Media  EXTERNAL_CONTENT_URI (android.provider.MediaStore.Images.Media  	MIME_TYPE (android.provider.MediaStore.Images.Media  
RELATIVE_PATH (android.provider.MediaStore.Images.Media  SIZE (android.provider.MediaStore.Images.Media  TITLE (android.provider.MediaStore.Images.Media  EXTERNAL_CONTENT_URI -android.provider.MediaStore.Images.Thumbnails  HEIGHT -android.provider.MediaStore.Images.Thumbnails  IMAGE_ID -android.provider.MediaStore.Images.Thumbnails  KIND -android.provider.MediaStore.Images.Thumbnails  
MICRO_KIND -android.provider.MediaStore.Images.Thumbnails  	MINI_KIND -android.provider.MediaStore.Images.Thumbnails  WIDTH -android.provider.MediaStore.Images.Thumbnails  getThumbnail -android.provider.MediaStore.Images.Thumbnails  DATA (android.provider.MediaStore.MediaColumns  
DATE_ADDED (android.provider.MediaStore.MediaColumns  
DATE_MODIFIED (android.provider.MediaStore.MediaColumns  
DATE_TAKEN (android.provider.MediaStore.MediaColumns  DISPLAY_NAME (android.provider.MediaStore.MediaColumns  DURATION (android.provider.MediaStore.MediaColumns  	MIME_TYPE (android.provider.MediaStore.MediaColumns  
RELATIVE_PATH (android.provider.MediaStore.MediaColumns  SIZE (android.provider.MediaStore.MediaColumns  TITLE (android.provider.MediaStore.MediaColumns  
DATE_ADDED 'android.provider.MediaStore.Video.Media  
DATE_MODIFIED 'android.provider.MediaStore.Video.Media  
DATE_TAKEN 'android.provider.MediaStore.Video.Media  DISPLAY_NAME 'android.provider.MediaStore.Video.Media  DURATION 'android.provider.MediaStore.Video.Media  EXTERNAL_CONTENT_URI 'android.provider.MediaStore.Video.Media  	MIME_TYPE 'android.provider.MediaStore.Video.Media  
RELATIVE_PATH 'android.provider.MediaStore.Video.Media  TITLE 'android.provider.MediaStore.Video.Media  DATA .android.provider.MediaStore.Video.VideoColumns  	TextUtils android.text  isEmpty android.text.TextUtils  Log android.util  d android.util.Log  e android.util.Log  MimeTypeMap android.webkit  getFileExtensionFromUrl android.webkit.MimeTypeMap  getMimeTypeFromExtension android.webkit.MimeTypeMap  getSingleton android.webkit.MimeTypeMap  NonNull androidx.annotation  ActivityCompat androidx.core.app  checkSelfPermission  androidx.core.app.ActivityCompat  requestPermissions  androidx.core.app.ActivityCompat  checkSelfPermission #androidx.core.content.ContextCompat  
ExifInterface androidx.exifinterface.media  ORIENTATION_NORMAL *androidx.exifinterface.media.ExifInterface  ORIENTATION_ROTATE_180 *androidx.exifinterface.media.ExifInterface  ORIENTATION_ROTATE_270 *androidx.exifinterface.media.ExifInterface  ORIENTATION_ROTATE_90 *androidx.exifinterface.media.ExifInterface  TAG_ORIENTATION *androidx.exifinterface.media.ExifInterface  getAttributeInt *androidx.exifinterface.media.ExifInterface  Activity "carnegietechnologies.gallery_saver  
ActivityAware "carnegietechnologies.gallery_saver  ActivityCompat "carnegietechnologies.gallery_saver  ActivityPluginBinding "carnegietechnologies.gallery_saver  Any "carnegietechnologies.gallery_saver  Array "carnegietechnologies.gallery_saver  Bitmap "carnegietechnologies.gallery_saver  
BitmapFactory "carnegietechnologies.gallery_saver  Boolean "carnegietechnologies.gallery_saver  BufferedInputStream "carnegietechnologies.gallery_saver  	ByteArray "carnegietechnologies.gallery_saver  ByteArrayOutputStream "carnegietechnologies.gallery_saver  ContentResolver "carnegietechnologies.gallery_saver  ContentUris "carnegietechnologies.gallery_saver  
ContentValues "carnegietechnologies.gallery_saver  CoroutineScope "carnegietechnologies.gallery_saver  Dispatchers "carnegietechnologies.gallery_saver  Environment "carnegietechnologies.gallery_saver  	Exception "carnegietechnologies.gallery_saver  
ExifInterface "carnegietechnologies.gallery_saver  File "carnegietechnologies.gallery_saver  FileInputStream "carnegietechnologies.gallery_saver  FileNotFoundException "carnegietechnologies.gallery_saver  	FileUtils "carnegietechnologies.gallery_saver  
FlutterPlugin "carnegietechnologies.gallery_saver  GallerySaver "carnegietechnologies.gallery_saver  GallerySaverPlugin "carnegietechnologies.gallery_saver  IOException "carnegietechnologies.gallery_saver  InputStream "carnegietechnologies.gallery_saver  Int "carnegietechnologies.gallery_saver  IntArray "carnegietechnologies.gallery_saver  Job "carnegietechnologies.gallery_saver  KEY_ALBUM_NAME "carnegietechnologies.gallery_saver  KEY_PATH "carnegietechnologies.gallery_saver  KEY_TO_DCIM "carnegietechnologies.gallery_saver  Log "carnegietechnologies.gallery_saver  Long "carnegietechnologies.gallery_saver  Manifest "carnegietechnologies.gallery_saver  Matrix "carnegietechnologies.gallery_saver  MediaMetadataRetriever "carnegietechnologies.gallery_saver  
MediaStore "carnegietechnologies.gallery_saver  	MediaType "carnegietechnologies.gallery_saver  
MethodCall "carnegietechnologies.gallery_saver  MethodCallHandler "carnegietechnologies.gallery_saver  
MethodChannel "carnegietechnologies.gallery_saver  MimeTypeMap "carnegietechnologies.gallery_saver  NonNull "carnegietechnologies.gallery_saver  OutputStream "carnegietechnologies.gallery_saver  PackageManager "carnegietechnologies.gallery_saver  PluginRegistry "carnegietechnologies.gallery_saver  )REQUEST_EXTERNAL_IMAGE_STORAGE_PERMISSION "carnegietechnologies.gallery_saver  String "carnegietechnologies.gallery_saver  System "carnegietechnologies.gallery_saver  	TextUtils "carnegietechnologies.gallery_saver  	Throwable "carnegietechnologies.gallery_saver  Throws "carnegietechnologies.gallery_saver  activity "carnegietechnologies.gallery_saver  	albumName "carnegietechnologies.gallery_saver  android "carnegietechnologies.gallery_saver  arrayOf "carnegietechnologies.gallery_saver  async "carnegietechnologies.gallery_saver  filePath "carnegietechnologies.gallery_saver  finishWithSuccess "carnegietechnologies.gallery_saver  insertImage "carnegietechnologies.gallery_saver  insertVideo "carnegietechnologies.gallery_saver  
isNotEmpty "carnegietechnologies.gallery_saver  launch "carnegietechnologies.gallery_saver  	mediaType "carnegietechnologies.gallery_saver  
plusAssign "carnegietechnologies.gallery_saver  println "carnegietechnologies.gallery_saver  toDcim "carnegietechnologies.gallery_saver  toInt "carnegietechnologies.gallery_saver  use "carnegietechnologies.gallery_saver  BUFFER_SIZE ,carnegietechnologies.gallery_saver.FileUtils  Bitmap ,carnegietechnologies.gallery_saver.FileUtils  
BitmapFactory ,carnegietechnologies.gallery_saver.FileUtils  BufferedInputStream ,carnegietechnologies.gallery_saver.FileUtils  	ByteArray ,carnegietechnologies.gallery_saver.FileUtils  ByteArrayOutputStream ,carnegietechnologies.gallery_saver.FileUtils  ContentUris ,carnegietechnologies.gallery_saver.FileUtils  
ContentValues ,carnegietechnologies.gallery_saver.FileUtils  DEGREES_180 ,carnegietechnologies.gallery_saver.FileUtils  DEGREES_270 ,carnegietechnologies.gallery_saver.FileUtils  
DEGREES_90 ,carnegietechnologies.gallery_saver.FileUtils  EOF ,carnegietechnologies.gallery_saver.FileUtils  Environment ,carnegietechnologies.gallery_saver.FileUtils  
ExifInterface ,carnegietechnologies.gallery_saver.FileUtils  File ,carnegietechnologies.gallery_saver.FileUtils  FileInputStream ,carnegietechnologies.gallery_saver.FileUtils  IOException ,carnegietechnologies.gallery_saver.FileUtils  Log ,carnegietechnologies.gallery_saver.FileUtils  Matrix ,carnegietechnologies.gallery_saver.FileUtils  MediaMetadataRetriever ,carnegietechnologies.gallery_saver.FileUtils  
MediaStore ,carnegietechnologies.gallery_saver.FileUtils  	MediaType ,carnegietechnologies.gallery_saver.FileUtils  MimeTypeMap ,carnegietechnologies.gallery_saver.FileUtils  SCALE_FACTOR ,carnegietechnologies.gallery_saver.FileUtils  System ,carnegietechnologies.gallery_saver.FileUtils  TAG ,carnegietechnologies.gallery_saver.FileUtils  	TextUtils ,carnegietechnologies.gallery_saver.FileUtils  android ,carnegietechnologies.gallery_saver.FileUtils  
bitmapToArray ,carnegietechnologies.gallery_saver.FileUtils  createDirIfNotExist ,carnegietechnologies.gallery_saver.FileUtils  
exifToDegrees ,carnegietechnologies.gallery_saver.FileUtils  getAlbumFolderPath ,carnegietechnologies.gallery_saver.FileUtils  getBytesFromFile ,carnegietechnologies.gallery_saver.FileUtils  getRotatedBytesIfNecessary ,carnegietechnologies.gallery_saver.FileUtils  getRotation ,carnegietechnologies.gallery_saver.FileUtils  insertImage ,carnegietechnologies.gallery_saver.FileUtils  insertVideo ,carnegietechnologies.gallery_saver.FileUtils  
plusAssign ,carnegietechnologies.gallery_saver.FileUtils  storeThumbnail ,carnegietechnologies.gallery_saver.FileUtils  toInt ,carnegietechnologies.gallery_saver.FileUtils  use ,carnegietechnologies.gallery_saver.FileUtils  FlutterPluginBinding 0carnegietechnologies.gallery_saver.FlutterPlugin  Activity /carnegietechnologies.gallery_saver.GallerySaver  ActivityCompat /carnegietechnologies.gallery_saver.GallerySaver  Any /carnegietechnologies.gallery_saver.GallerySaver  Array /carnegietechnologies.gallery_saver.GallerySaver  Boolean /carnegietechnologies.gallery_saver.GallerySaver  CoroutineScope /carnegietechnologies.gallery_saver.GallerySaver  Dispatchers /carnegietechnologies.gallery_saver.GallerySaver  	FileUtils /carnegietechnologies.gallery_saver.GallerySaver  Int /carnegietechnologies.gallery_saver.GallerySaver  IntArray /carnegietechnologies.gallery_saver.GallerySaver  Job /carnegietechnologies.gallery_saver.GallerySaver  KEY_ALBUM_NAME /carnegietechnologies.gallery_saver.GallerySaver  KEY_PATH /carnegietechnologies.gallery_saver.GallerySaver  KEY_TO_DCIM /carnegietechnologies.gallery_saver.GallerySaver  Manifest /carnegietechnologies.gallery_saver.GallerySaver  	MediaType /carnegietechnologies.gallery_saver.GallerySaver  
MethodCall /carnegietechnologies.gallery_saver.GallerySaver  
MethodChannel /carnegietechnologies.gallery_saver.GallerySaver  PackageManager /carnegietechnologies.gallery_saver.GallerySaver  )REQUEST_EXTERNAL_IMAGE_STORAGE_PERMISSION /carnegietechnologies.gallery_saver.GallerySaver  String /carnegietechnologies.gallery_saver.GallerySaver  activity /carnegietechnologies.gallery_saver.GallerySaver  	albumName /carnegietechnologies.gallery_saver.GallerySaver  android /carnegietechnologies.gallery_saver.GallerySaver  arrayOf /carnegietechnologies.gallery_saver.GallerySaver  async /carnegietechnologies.gallery_saver.GallerySaver  checkPermissionAndSaveFile /carnegietechnologies.gallery_saver.GallerySaver  filePath /carnegietechnologies.gallery_saver.GallerySaver  finishWithFailure /carnegietechnologies.gallery_saver.GallerySaver  finishWithSuccess /carnegietechnologies.gallery_saver.GallerySaver  insertImage /carnegietechnologies.gallery_saver.GallerySaver  insertVideo /carnegietechnologies.gallery_saver.GallerySaver  
isNotEmpty /carnegietechnologies.gallery_saver.GallerySaver  isWritePermissionGranted /carnegietechnologies.gallery_saver.GallerySaver  job /carnegietechnologies.gallery_saver.GallerySaver  launch /carnegietechnologies.gallery_saver.GallerySaver  	mediaType /carnegietechnologies.gallery_saver.GallerySaver  
pendingResult /carnegietechnologies.gallery_saver.GallerySaver  
saveMediaFile /carnegietechnologies.gallery_saver.GallerySaver  toDcim /carnegietechnologies.gallery_saver.GallerySaver  uiScope /carnegietechnologies.gallery_saver.GallerySaver  ActivityCompat 9carnegietechnologies.gallery_saver.GallerySaver.Companion  CoroutineScope 9carnegietechnologies.gallery_saver.GallerySaver.Companion  Dispatchers 9carnegietechnologies.gallery_saver.GallerySaver.Companion  	FileUtils 9carnegietechnologies.gallery_saver.GallerySaver.Companion  Job 9carnegietechnologies.gallery_saver.GallerySaver.Companion  KEY_ALBUM_NAME 9carnegietechnologies.gallery_saver.GallerySaver.Companion  KEY_PATH 9carnegietechnologies.gallery_saver.GallerySaver.Companion  KEY_TO_DCIM 9carnegietechnologies.gallery_saver.GallerySaver.Companion  Manifest 9carnegietechnologies.gallery_saver.GallerySaver.Companion  	MediaType 9carnegietechnologies.gallery_saver.GallerySaver.Companion  PackageManager 9carnegietechnologies.gallery_saver.GallerySaver.Companion  )REQUEST_EXTERNAL_IMAGE_STORAGE_PERMISSION 9carnegietechnologies.gallery_saver.GallerySaver.Companion  activity 9carnegietechnologies.gallery_saver.GallerySaver.Companion  	albumName 9carnegietechnologies.gallery_saver.GallerySaver.Companion  android 9carnegietechnologies.gallery_saver.GallerySaver.Companion  arrayOf 9carnegietechnologies.gallery_saver.GallerySaver.Companion  async 9carnegietechnologies.gallery_saver.GallerySaver.Companion  filePath 9carnegietechnologies.gallery_saver.GallerySaver.Companion  finishWithSuccess 9carnegietechnologies.gallery_saver.GallerySaver.Companion  insertImage 9carnegietechnologies.gallery_saver.GallerySaver.Companion  insertVideo 9carnegietechnologies.gallery_saver.GallerySaver.Companion  
isNotEmpty 9carnegietechnologies.gallery_saver.GallerySaver.Companion  launch 9carnegietechnologies.gallery_saver.GallerySaver.Companion  	mediaType 9carnegietechnologies.gallery_saver.GallerySaver.Companion  toDcim 9carnegietechnologies.gallery_saver.GallerySaver.Companion  Result =carnegietechnologies.gallery_saver.GallerySaver.MethodChannel  GallerySaver 5carnegietechnologies.gallery_saver.GallerySaverPlugin  	MediaType 5carnegietechnologies.gallery_saver.GallerySaverPlugin  
MethodChannel 5carnegietechnologies.gallery_saver.GallerySaverPlugin  activity 5carnegietechnologies.gallery_saver.GallerySaverPlugin  channel 5carnegietechnologies.gallery_saver.GallerySaverPlugin  gallerySaver 5carnegietechnologies.gallery_saver.GallerySaverPlugin  println 5carnegietechnologies.gallery_saver.GallerySaverPlugin  image ,carnegietechnologies.gallery_saver.MediaType  video ,carnegietechnologies.gallery_saver.MediaType  Result 0carnegietechnologies.gallery_saver.MethodChannel   RequestPermissionsResultListener 1carnegietechnologies.gallery_saver.PluginRegistry  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  
ActivityAware ,io.flutter.embedding.engine.plugins.activity  ActivityPluginBinding ,io.flutter.embedding.engine.plugins.activity  activity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  #addRequestPermissionsResultListener Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  BinaryMessenger io.flutter.plugin.common  
MethodCall io.flutter.plugin.common  
MethodChannel io.flutter.plugin.common  PluginRegistry io.flutter.plugin.common  argument #io.flutter.plugin.common.MethodCall  method #io.flutter.plugin.common.MethodCall  MethodCallHandler &io.flutter.plugin.common.MethodChannel  Result &io.flutter.plugin.common.MethodChannel  setMethodCallHandler &io.flutter.plugin.common.MethodChannel  notImplemented -io.flutter.plugin.common.MethodChannel.Result  success -io.flutter.plugin.common.MethodChannel.Result   RequestPermissionsResultListener 'io.flutter.plugin.common.PluginRegistry  Bitmap java.io  
BitmapFactory java.io  Boolean java.io  BufferedInputStream java.io  	ByteArray java.io  ByteArrayOutputStream java.io  ContentResolver java.io  ContentUris java.io  
ContentValues java.io  Environment java.io  	Exception java.io  
ExifInterface java.io  File java.io  FileInputStream java.io  FileNotFoundException java.io  IOException java.io  InputStream java.io  Int java.io  Log java.io  Long java.io  Matrix java.io  MediaMetadataRetriever java.io  
MediaStore java.io  	MediaType java.io  MimeTypeMap java.io  OutputStream java.io  String java.io  System java.io  	TextUtils java.io  	Throwable java.io  Throws java.io  android java.io  
plusAssign java.io  toInt java.io  use java.io  read java.io.BufferedInputStream  use java.io.BufferedInputStream  toByteArray java.io.ByteArrayOutputStream  absolutePath java.io.File  exists java.io.File  length java.io.File  mkdirs java.io.File  name java.io.File  path java.io.File  	separator java.io.File  toString java.io.File  read java.io.FileInputStream  use java.io.FileInputStream  message java.io.FileNotFoundException  toString java.io.FileNotFoundException  read java.io.FilterInputStream  toString java.io.IOException  read java.io.InputStream  use java.io.OutputStream  write java.io.OutputStream  	Exception 	java.lang  message java.lang.Exception  toString java.lang.Exception  currentTimeMillis java.lang.System  Array kotlin  	ByteArray kotlin  	Function1 kotlin  IntArray kotlin  Nothing kotlin  	Throwable kotlin  arrayOf kotlin  use kotlin  toString 
kotlin.Any  not kotlin.Boolean  size kotlin.ByteArray  toFloat 
kotlin.Double  div kotlin.Float  	compareTo 
kotlin.Int  times 
kotlin.Int  toFloat 
kotlin.Int  get kotlin.IntArray  
isNotEmpty kotlin.IntArray  div kotlin.Long  toInt kotlin.Long  plus 
kotlin.String  
plusAssign 
kotlin.String  toInt 
kotlin.String  message kotlin.Throwable  
isNotEmpty kotlin.collections  
plusAssign kotlin.collections  CoroutineContext kotlin.coroutines  SuspendFunction1 kotlin.coroutines  plus "kotlin.coroutines.CoroutineContext  println 	kotlin.io  use 	kotlin.io  Throws 
kotlin.jvm  
isNotEmpty kotlin.text  toInt kotlin.text  Activity kotlinx.coroutines  ActivityCompat kotlinx.coroutines  Any kotlinx.coroutines  Array kotlinx.coroutines  Boolean kotlinx.coroutines  CompletableJob kotlinx.coroutines  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Deferred kotlinx.coroutines  Dispatchers kotlinx.coroutines  	FileUtils kotlinx.coroutines  Int kotlinx.coroutines  IntArray kotlinx.coroutines  Job kotlinx.coroutines  KEY_ALBUM_NAME kotlinx.coroutines  KEY_PATH kotlinx.coroutines  KEY_TO_DCIM kotlinx.coroutines  MainCoroutineDispatcher kotlinx.coroutines  Manifest kotlinx.coroutines  	MediaType kotlinx.coroutines  
MethodCall kotlinx.coroutines  
MethodChannel kotlinx.coroutines  PackageManager kotlinx.coroutines  PluginRegistry kotlinx.coroutines  )REQUEST_EXTERNAL_IMAGE_STORAGE_PERMISSION kotlinx.coroutines  String kotlinx.coroutines  activity kotlinx.coroutines  	albumName kotlinx.coroutines  android kotlinx.coroutines  arrayOf kotlinx.coroutines  async kotlinx.coroutines  filePath kotlinx.coroutines  finishWithSuccess kotlinx.coroutines  insertImage kotlinx.coroutines  insertVideo kotlinx.coroutines  
isNotEmpty kotlinx.coroutines  launch kotlinx.coroutines  	mediaType kotlinx.coroutines  toDcim kotlinx.coroutines  plus &kotlinx.coroutines.CoroutineDispatcher  Dispatchers !kotlinx.coroutines.CoroutineScope  	FileUtils !kotlinx.coroutines.CoroutineScope  	MediaType !kotlinx.coroutines.CoroutineScope  activity !kotlinx.coroutines.CoroutineScope  	albumName !kotlinx.coroutines.CoroutineScope  async !kotlinx.coroutines.CoroutineScope  filePath !kotlinx.coroutines.CoroutineScope  finishWithSuccess !kotlinx.coroutines.CoroutineScope  insertImage !kotlinx.coroutines.CoroutineScope  insertVideo !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  	mediaType !kotlinx.coroutines.CoroutineScope  toDcim !kotlinx.coroutines.CoroutineScope  await kotlinx.coroutines.Deferred  IO kotlinx.coroutines.Dispatchers  Main kotlinx.coroutines.Dispatchers  plus *kotlinx.coroutines.MainCoroutineDispatcher  Result  kotlinx.coroutines.MethodChannel   RequestPermissionsResultListener !kotlinx.coroutines.PluginRegistry                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              