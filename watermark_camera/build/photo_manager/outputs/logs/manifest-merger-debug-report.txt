-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/android/src/main/AndroidManifest.xml:1:1-5:12
INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/android/src/main/AndroidManifest.xml:1:1-5:12
	package
		INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/android/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/android/src/main/AndroidManifest.xml:1:11-69
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from /Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/android/src/main/AndroidManifest.xml:2:5-4:37
	android:maxSdkVersion
		ADDED from /Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/android/src/main/AndroidManifest.xml:4:9-35
	android:name
		ADDED from /Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/android/src/main/AndroidManifest.xml:3:9-64
uses-sdk
INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/android/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/android/src/main/AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/android/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/.pub-cache/hosted/pub.dev/photo_manager-3.7.1/android/src/main/AndroidManifest.xml
